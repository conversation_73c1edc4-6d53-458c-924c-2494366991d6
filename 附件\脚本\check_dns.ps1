# DNS Pollution Check Script
# For detecting DNS pollution and potential man-in-the-middle attacks
# Author: DNS Security Check Script
# Date: 2025-01-05

param(
    [switch]$Detailed,
    [switch]$Export,
    [string]$OutputFile = "dns_check_report.txt"
)

Write-Host "=== DNS污染检查工具 ===" -ForegroundColor Green
Write-Host "检查时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# 定义测试域名列表（包含常见被污染的域名）
$TestDomains = @(
    "google.com",
    "youtube.com", 
    "facebook.com",
    "twitter.com",
    "github.com",
    "stackoverflow.com",
    "wikipedia.org",
    "cloudflare.com",
    "baidu.com",
    "qq.com"
)

# 定义可信DNS服务器
$TrustedDNS = @{
    "本地DNS" = (Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object {$_.InterfaceAlias -notlike "*Loopback*"} | Select-Object -First 1).ServerAddresses[0]
    "Cloudflare" = "*******"
    "Google" = "*******"
    "阿里DNS" = "*********"
    "腾讯DNS" = "************"
}

$Results = @()

function Test-DNSResolution {
    param($Domain, $DNSServer, $ServerName)
    
    try {
        $Result = Resolve-DnsName -Name $Domain -Server $DNSServer -Type A -ErrorAction Stop
        $IPs = $Result | Where-Object {$_.Type -eq "A"} | Select-Object -ExpandProperty IPAddress
        
        return @{
            Domain = $Domain
            DNSServer = $ServerName
            IPs = $IPs -join ", "
            Status = "成功"
            ResponseTime = (Measure-Command { Resolve-DnsName -Name $Domain -Server $DNSServer -Type A -ErrorAction SilentlyContinue }).TotalMilliseconds
        }
    }
    catch {
        return @{
            Domain = $Domain
            DNSServer = $ServerName
            IPs = "解析失败"
            Status = "失败: $($_.Exception.Message)"
            ResponseTime = 0
        }
    }
}

Write-Host "1. 检查当前DNS配置..." -ForegroundColor Cyan
$NetworkAdapters = Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object {$_.ServerAddresses.Count -gt 0}
foreach ($Adapter in $NetworkAdapters) {
    Write-Host "  网卡: $($Adapter.InterfaceAlias)" -ForegroundColor White
    Write-Host "  DNS服务器: $($Adapter.ServerAddresses -join ', ')" -ForegroundColor White
}
Write-Host ""

Write-Host "2. 测试DNS解析一致性..." -ForegroundColor Cyan
foreach ($Domain in $TestDomains) {
    Write-Host "测试域名: $Domain" -ForegroundColor Yellow
    
    $DomainResults = @()
    foreach ($DNS in $TrustedDNS.GetEnumerator()) {
        $Result = Test-DNSResolution -Domain $Domain -DNSServer $DNS.Value -ServerName $DNS.Key
        $DomainResults += $Result
        
        if ($Result.Status -eq "成功") {
            Write-Host "  $($DNS.Key): $($Result.IPs) (${[math]::Round($Result.ResponseTime, 2)}ms)" -ForegroundColor Green
        } else {
            Write-Host "  $($DNS.Key): $($Result.Status)" -ForegroundColor Red
        }
    }
    
    # 检查IP地址一致性
    $UniqueIPs = $DomainResults | Where-Object {$_.Status -eq "成功"} | Select-Object -ExpandProperty IPs | Sort-Object -Unique
    if ($UniqueIPs.Count -gt 1) {
        Write-Host "  ⚠️  警告: 不同DNS服务器返回不同IP地址，可能存在DNS污染!" -ForegroundColor Red
    }
    
    $Results += $DomainResults
    Write-Host ""
}

Write-Host "3. 检查DNS劫持迹象..." -ForegroundColor Cyan

# 检查是否有异常的DNS响应
$SuspiciousIPs = @(
    "127.0.0.1",
    "0.0.0.0", 
    "192.168.",
    "10.",
    "172.16.",
    "172.17.",
    "172.18.",
    "172.19.",
    "172.20.",
    "172.21.",
    "172.22.",
    "172.23.",
    "172.24.",
    "172.25.",
    "172.26.",
    "172.27.",
    "172.28.",
    "172.29.",
    "172.30.",
    "172.31."
)

$SuspiciousResults = $Results | Where-Object {
    $_.Status -eq "成功" -and 
    ($SuspiciousIPs | Where-Object { $_.IPs -like "*$_*" })
}

if ($SuspiciousResults.Count -gt 0) {
    Write-Host "发现可疑的DNS解析结果:" -ForegroundColor Red
    $SuspiciousResults | ForEach-Object {
        Write-Host "  $($_.Domain) -> $($_.IPs) (通过 $($_.DNSServer))" -ForegroundColor Red
    }
} else {
    Write-Host "未发现明显的DNS劫持迹象" -ForegroundColor Green
}
Write-Host ""

Write-Host "4. DNS缓存检查..." -ForegroundColor Cyan
try {
    $DNSCache = Get-DnsClientCache | Select-Object Name, Data, TimeToLive | Sort-Object Name
    Write-Host "当前DNS缓存条目数: $($DNSCache.Count)" -ForegroundColor White
    
    if ($Detailed) {
        Write-Host "DNS缓存详情:" -ForegroundColor White
        $DNSCache | Format-Table -AutoSize
    }
} catch {
    Write-Host "无法获取DNS缓存信息: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "5. 网络连接检查..." -ForegroundColor Cyan
$TestConnections = @(
    @{Host="*******"; Port=53; Name="Cloudflare DNS"},
    @{Host="*******"; Port=53; Name="Google DNS"},
    @{Host="*********"; Port=53; Name="阿里DNS"}
)

foreach ($Test in $TestConnections) {
    try {
        $Connection = Test-NetConnection -ComputerName $Test.Host -Port $Test.Port -WarningAction SilentlyContinue
        if ($Connection.TcpTestSucceeded) {
            Write-Host "  ✓ $($Test.Name) 连接正常" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $($Test.Name) 连接失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "  ✗ $($Test.Name) 测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 检查完成 ===" -ForegroundColor Green

# 生成建议
Write-Host "建议:" -ForegroundColor Yellow
Write-Host "1. 如果发现DNS污染，建议更换DNS服务器为 ******* 或 *******" -ForegroundColor White
Write-Host "2. 清除DNS缓存: ipconfig /flushdns" -ForegroundColor White
Write-Host "3. 如果问题持续，考虑使用DoH(DNS over HTTPS)或DoT(DNS over TLS)" -ForegroundColor White
Write-Host "4. 检查路由器DNS设置，确保未被篡改" -ForegroundColor White

# 导出报告
if ($Export) {
    $ReportContent = @"
DNS污染检查报告
生成时间: $(Get-Date)

=== DNS配置 ===
$(($NetworkAdapters | ForEach-Object { "网卡: $($_.InterfaceAlias), DNS: $($_.ServerAddresses -join ', ')" }) -join "`n")

=== 解析结果 ===
$($Results | ForEach-Object { "$($_.Domain) - $($_.DNSServer): $($_.IPs) ($($_.Status))" } | Out-String)

=== 可疑结果 ===
$(if ($SuspiciousResults.Count -gt 0) { $SuspiciousResults | ForEach-Object { "$($_.Domain) -> $($_.IPs) (通过 $($_.DNSServer))" } } else { "无可疑结果" })
"@
    
    $ReportContent | Out-File -FilePath $OutputFile -Encoding UTF8
    Write-Host "报告已导出到: $OutputFile" -ForegroundColor Green
}
