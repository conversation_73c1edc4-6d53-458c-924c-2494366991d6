---
aliases:
  - 网络套接字
  - Network socket
英文: Network socket
tags: 
发布时间: 1983-01-01
开发者: 加州大学伯克利分校
类型:
  - 技术分析
cssclasses:
  - editor-full
created: 2024-09-09T20:59
updated: 2025-07-05T11:27
---

# 简介
1. 这是一个网络编程当中的抽象概念，并不能将其视为协议，
	1. 也无法将其归为OSI模型或是[[TCP协议]]/IP协议栈中的特定层级
	2. **但可以将其理解为编程接口**；
	3. 虽然无法归为特定的层级，但是可以将其视为**运行在传输层和[[应用层模型(网络协议)]]之间的接口**；
		1. 传输层负责数据的传输
		2. 而套接字则是提供了在传输层协议上的编程接口；
2. **Socket**在如今已经成为了网络通信的基础，大部分语言都可以按照该机制进行操作开发；
	1. 无论使用那种编程语言，**套接字的底层实现都需要依靠操作系统提供的API**；

---


