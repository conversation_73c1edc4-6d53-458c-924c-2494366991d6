#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNS安全检查工具 - 支持代理版本
用于检测DNS污染、劫持和中间人攻击，支持通过代理访问国外DNS
"""

import socket
import time
import subprocess
import sys
import json
import requests
import socks
from datetime import datetime
import concurrent.futures
import argparse

class DNSSecurityCheckerWithProxy:
    def __init__(self, proxy_host='127.0.0.1', proxy_port=7890):
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        
        # 分类域名：国外和国内
        self.foreign_domains = [
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'github.com', 'stackoverflow.com', 'wikipedia.org', 'cloudflare.com'
        ]
        
        self.domestic_domains = [
            'baidu.com', 'qq.com', 'taobao.com', 'weibo.com'
        ]
        
        # 分类DNS服务器：国外和国内
        self.foreign_dns_servers = {
            'Cloudflare': '*******',
            'Google': '*******',
            'Quad9': '*******',
            'OpenDNS': '**************'
        }
        
        self.domestic_dns_servers = {
            '阿里DNS': '*********',
            '腾讯DNS': '************',
            '百度DNS': '************'
        }
        
        # 可疑IP段
        self.suspicious_patterns = [
            '127.0.0.1', '0.0.0.0', '192.168.', '10.', '172.16.',
            '172.17.', '172.18.', '172.19.', '172.20.', '172.21.',
            '172.22.', '172.23.', '172.24.', '172.25.', '172.26.',
            '172.27.', '172.28.', '172.29.', '172.30.', '172.31.'
        ]

    def test_proxy_connection(self):
        """测试代理连接"""
        try:
            # 设置代理
            proxies = {
                'http': f'socks5://{self.proxy_host}:{self.proxy_port}',
                'https': f'socks5://{self.proxy_host}:{self.proxy_port}'
            }
            
            # 测试连接到Google
            response = requests.get('http://www.google.com', 
                                  proxies=proxies, 
                                  timeout=10)
            return True, f"代理连接正常 (状态码: {response.status_code})"
        except Exception as e:
            return False, f"代理连接失败: {e}"

    def resolve_domain_direct(self, domain, dns_server, timeout=5):
        """直接DNS解析（用于国内域名和DNS）"""
        try:
            cmd = f'nslookup {domain} {dns_server}'
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                ips = []
                for line in lines:
                    if 'Address:' in line and dns_server not in line:
                        ip = line.split('Address:')[-1].strip()
                        if self.is_valid_ip(ip):
                            ips.append(ip)
                return ips
            return []
        except Exception as e:
            print(f"直接解析 {domain} 时出错 (DNS: {dns_server}): {e}")
            return []

    def resolve_domain_via_proxy(self, domain, dns_server, timeout=10):
        """通过代理进行DNS解析（用于国外域名和DNS）"""
        try:
            # 使用DoH (DNS over HTTPS) 通过代理
            proxies = {
                'http': f'socks5://{self.proxy_host}:{self.proxy_port}',
                'https': f'socks5://{self.proxy_host}:{self.proxy_port}'
            }
            
            # 使用Cloudflare的DoH服务
            if dns_server == '*******':
                url = f'https://cloudflare-dns.com/dns-query?name={domain}&type=A'
            elif dns_server == '*******':
                url = f'https://dns.google/resolve?name={domain}&type=A'
            elif dns_server == '*******':
                url = f'https://dns.quad9.net:5053/dns-query?name={domain}&type=A'
            else:
                # 默认使用Cloudflare DoH
                url = f'https://cloudflare-dns.com/dns-query?name={domain}&type=A'
            
            headers = {'Accept': 'application/dns-json'}
            response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                ips = []
                if 'Answer' in data:
                    for answer in data['Answer']:
                        if answer.get('type') == 1:  # A record
                            ips.append(answer['data'])
                return ips
            return []
        except Exception as e:
            print(f"代理解析 {domain} 时出错 (DNS: {dns_server}): {e}")
            return []

    def is_valid_ip(self, ip):
        """验证IP地址格式"""
        try:
            socket.inet_aton(ip)
            return True
        except socket.error:
            return False

    def is_suspicious_ip(self, ip):
        """检查IP是否可疑"""
        return any(ip.startswith(pattern) for pattern in self.suspicious_patterns)

    def check_domain_consistency(self, domain, is_foreign=False):
        """检查域名在不同DNS服务器上的解析一致性"""
        results = {}
        
        print(f"\n检查域名: {domain} {'(通过代理)' if is_foreign else '(直接连接)'}")
        print("-" * 60)
        
        # 选择DNS服务器
        if is_foreign:
            dns_servers = self.foreign_dns_servers
        else:
            dns_servers = {**self.foreign_dns_servers, **self.domestic_dns_servers}
        
        for name, dns_server in dns_servers.items():
            start_time = time.time()
            
            # 根据域名类型选择解析方式
            if is_foreign and dns_server in self.foreign_dns_servers.values():
                ips = self.resolve_domain_via_proxy(domain, dns_server)
            else:
                ips = self.resolve_domain_direct(domain, dns_server)
            
            response_time = (time.time() - start_time) * 1000
            
            results[name] = {
                'ips': ips,
                'response_time': response_time,
                'dns_server': dns_server,
                'via_proxy': is_foreign and dns_server in self.foreign_dns_servers.values()
            }
            
            if ips:
                ip_str = ', '.join(ips)
                suspicious = any(self.is_suspicious_ip(ip) for ip in ips)
                proxy_indicator = " [代理]" if results[name]['via_proxy'] else " [直连]"
                status = "🚨 可疑" if suspicious else "✓ 正常"
                print(f"{name:12} ({dns_server:15}): {ip_str:30} [{response_time:6.1f}ms]{proxy_indicator} {status}")
            else:
                proxy_indicator = " [代理]" if results[name]['via_proxy'] else " [直连]"
                print(f"{name:12} ({dns_server:15}): 解析失败{proxy_indicator}")
        
        # 分析一致性
        all_ips = set()
        for result in results.values():
            all_ips.update(result['ips'])
        
        if len(all_ips) > 1:
            print(f"\n⚠️  警告: 发现不一致的解析结果，可能存在DNS污染!")
            print(f"   发现的不同IP: {', '.join(all_ips)}")
        elif len(all_ips) == 1:
            print(f"\n✅ 解析结果一致: {list(all_ips)[0]}")
        
        return results

    def get_system_dns(self):
        """获取系统DNS配置"""
        try:
            if sys.platform == "win32":
                cmd = 'ipconfig /all'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                lines = result.stdout.split('\n')
                dns_servers = []
                for line in lines:
                    if 'DNS' in line and ':' in line:
                        dns_servers.append(line.strip())
                return dns_servers
            else:
                with open('/etc/resolv.conf', 'r') as f:
                    lines = f.readlines()
                return [line.strip() for line in lines if line.startswith('nameserver')]
        except Exception as e:
            print(f"获取系统DNS配置失败: {e}")
            return []

    def test_dns_connectivity(self):
        """测试DNS服务器连通性"""
        print("\n检查DNS服务器连通性:")
        print("-" * 60)
        
        all_dns = {**self.foreign_dns_servers, **self.domestic_dns_servers}
        
        for name, server in all_dns.items():
            try:
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((server, 53))
                sock.close()
                response_time = (time.time() - start_time) * 1000
                
                if result == 0:
                    print(f"✓ {name:12} ({server:15}): 连接正常 [{response_time:.1f}ms]")
                else:
                    print(f"✗ {name:12} ({server:15}): 连接失败")
            except Exception as e:
                print(f"✗ {name:12} ({server:15}): 测试失败 - {e}")

    def generate_report(self, results, proxy_status):
        """生成检查报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'proxy_status': proxy_status,
            'system_dns': self.get_system_dns(),
            'test_results': results,
            'summary': {
                'total_domains': len(self.foreign_domains) + len(self.domestic_domains),
                'suspicious_domains': [],
                'inconsistent_domains': [],
                'proxy_resolved_domains': []
            }
        }
        
        # 分析结果
        for domain, domain_results in results.items():
            all_ips = set()
            suspicious_found = False
            proxy_used = False
            
            for dns_name, result in domain_results.items():
                all_ips.update(result['ips'])
                if any(self.is_suspicious_ip(ip) for ip in result['ips']):
                    suspicious_found = True
                if result.get('via_proxy', False):
                    proxy_used = True
            
            if suspicious_found:
                report['summary']['suspicious_domains'].append(domain)
            
            if len(all_ips) > 1:
                report['summary']['inconsistent_domains'].append(domain)
            
            if proxy_used:
                report['summary']['proxy_resolved_domains'].append(domain)
        
        return report

    def run_check(self, export_report=False):
        """运行完整的DNS安全检查"""
        print("=" * 70)
        print("           DNS安全检查工具 (支持代理版本)")
        print("=" * 70)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试代理连接
        print(f"\n测试代理连接 ({self.proxy_host}:{self.proxy_port}):")
        print("-" * 60)
        proxy_ok, proxy_msg = self.test_proxy_connection()
        print(f"代理状态: {proxy_msg}")
        
        if not proxy_ok:
            print("⚠️  警告: 代理连接失败，国外DNS查询可能受限")
        
        # 显示系统DNS配置
        print("\n当前系统DNS配置:")
        print("-" * 60)
        system_dns = self.get_system_dns()
        for dns in system_dns:
            print(f"  {dns}")
        
        # 测试DNS连通性
        self.test_dns_connectivity()
        
        # 检查域名解析一致性
        results = {}
        
        # 检查国外域名（通过代理）
        print(f"\n{'='*70}")
        print("检查国外域名 (通过代理)")
        print(f"{'='*70}")
        for domain in self.foreign_domains:
            results[domain] = self.check_domain_consistency(domain, is_foreign=True)
        
        # 检查国内域名（直接连接）
        print(f"\n{'='*70}")
        print("检查国内域名 (直接连接)")
        print(f"{'='*70}")
        for domain in self.domestic_domains:
            results[domain] = self.check_domain_consistency(domain, is_foreign=False)
        
        # 生成报告
        report = self.generate_report(results, {'proxy_ok': proxy_ok, 'proxy_msg': proxy_msg})
        
        # 显示总结
        print("\n" + "=" * 70)
        print("检查总结:")
        print("-" * 70)
        print(f"代理状态: {'正常' if proxy_ok else '异常'}")
        print(f"测试域名总数: {report['summary']['total_domains']}")
        print(f"发现可疑域名: {len(report['summary']['suspicious_domains'])}")
        print(f"解析不一致域名: {len(report['summary']['inconsistent_domains'])}")
        print(f"通过代理解析域名: {len(report['summary']['proxy_resolved_domains'])}")
        
        if report['summary']['suspicious_domains']:
            print(f"\n可疑域名: {', '.join(report['summary']['suspicious_domains'])}")
        
        if report['summary']['inconsistent_domains']:
            print(f"\n解析不一致域名: {', '.join(report['summary']['inconsistent_domains'])}")
        
        # 建议
        print("\n建议:")
        if proxy_ok:
            print("✓ 代理工作正常，国外DNS查询已通过代理")
        else:
            print("⚠️  代理连接失败，建议检查代理设置")
        print("1. 对于解析不一致的域名，建议使用可信DNS服务器")
        print("2. 推荐国外DNS: ******* (Cloudflare) 或 ******* (Google)")
        print("3. 推荐国内DNS: ********* (阿里) 或 ************ (腾讯)")
        print("4. 清除DNS缓存: ipconfig /flushdns (Windows)")
        print("5. 考虑使用DoH或DoT加密DNS")
        
        if export_report:
            filename = f"dns_proxy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n详细报告已导出: {filename}")

def main():
    parser = argparse.ArgumentParser(description='DNS安全检查工具 (支持代理)')
    parser.add_argument('--export', action='store_true', help='导出详细报告')
    parser.add_argument('--proxy-host', default='127.0.0.1', help='代理主机地址')
    parser.add_argument('--proxy-port', type=int, default=7890, help='代理端口')
    args = parser.parse_args()
    
    checker = DNSSecurityCheckerWithProxy(proxy_host=args.proxy_host, proxy_port=args.proxy_port)
    checker.run_check(export_report=args.export)

if __name__ == "__main__":
    main()
