/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

(function initCjs() {
  const globalThisRecord = globalThis;
  globalThisRecord["__name"] ??= name;
  const originalRequire = require;
  if (originalRequire && !originalRequire.__isPatched) {
    require = Object.assign(
      (id) => requirePatched(id),
      originalRequire,
      {
        __isPatched: true
      }
    );
  }
  const newFuncs = {
    __extractDefault: () => extractDefault,
    process: () => {
      const browserProcess = {
        browser: true,
        cwd: () => "/",
        env: {},
        platform: "android"
      };
      return browserProcess;
    }
  };
  for (const key of Object.keys(newFuncs)) {
    globalThisRecord[key] ??= newFuncs[key]?.();
  }
  function name(obj) {
    return obj;
  }
  function extractDefault(module2) {
    return module2 && module2.__esModule && module2.default ? module2.default : module2;
  }
  function requirePatched(id) {
    const module2 = originalRequire?.(id);
    if (module2) {
      return extractDefault(module2);
    }
    if (id === "process" || id === "node:process") {
      console.error(`Module not found: ${id}. Fake process object is returned instead.`);
      return globalThis.process;
    }
    console.error(`Module not found: ${id}. Empty object is returned instead.`);
    return {};
  }
})();

"use strict";var Sy=Object.create;var $i=Object.defineProperty;var Ty=Object.getOwnPropertyDescriptor;var _y=Object.getOwnPropertyNames;var Fy=Object.getPrototypeOf,Py=Object.prototype.hasOwnProperty;var V=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),yc=(t,e)=>{for(var n in e)$i(t,n,{get:e[n],enumerable:!0})},bc=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of _y(e))!Py.call(t,s)&&s!==n&&$i(t,s,{get:()=>e[s],enumerable:!(r=Ty(e,s))||r.enumerable});return t};var Ne=(t,e,n)=>(n=t!=null?Sy(Fy(t)):{},bc(e||!t||!t.__esModule?$i(n,"default",{value:t,enumerable:!0}):n,t)),Iy=t=>bc($i({},"__esModule",{value:!0}),t);var jt=V((ya,Jn)=>{(function(t,e){typeof ya=="object"&&typeof Jn<"u"?Jn.exports=e():typeof define=="function"&&define.amd?define(e):t.moment=e()})(ya,function(){"use strict";var t;function e(){return t.apply(null,arguments)}function n(i){t=i}function r(i){return i instanceof Array||Object.prototype.toString.call(i)==="[object Array]"}function s(i){return i!=null&&Object.prototype.toString.call(i)==="[object Object]"}function o(i,a){return Object.prototype.hasOwnProperty.call(i,a)}function l(i){if(Object.getOwnPropertyNames)return Object.getOwnPropertyNames(i).length===0;var a;for(a in i)if(o(i,a))return!1;return!0}function u(i){return i===void 0}function d(i){return typeof i=="number"||Object.prototype.toString.call(i)==="[object Number]"}function f(i){return i instanceof Date||Object.prototype.toString.call(i)==="[object Date]"}function m(i,a){var c=[],h,w=i.length;for(h=0;h<w;++h)c.push(a(i[h],h));return c}function p(i,a){for(var c in a)o(a,c)&&(i[c]=a[c]);return o(a,"toString")&&(i.toString=a.toString),o(a,"valueOf")&&(i.valueOf=a.valueOf),i}function y(i,a,c,h){return Ku(i,a,c,h,!0).utc()}function g(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function A(i){return i._pf==null&&(i._pf=g()),i._pf}var P;Array.prototype.some?P=Array.prototype.some:P=function(i){var a=Object(this),c=a.length>>>0,h;for(h=0;h<c;h++)if(h in a&&i.call(this,a[h],h,a))return!0;return!1};function S(i){var a=null,c=!1,h=i._d&&!isNaN(i._d.getTime());if(h&&(a=A(i),c=P.call(a.parsedDateParts,function(w){return w!=null}),h=a.overflow<0&&!a.empty&&!a.invalidEra&&!a.invalidMonth&&!a.invalidWeekday&&!a.weekdayMismatch&&!a.nullInput&&!a.invalidFormat&&!a.userInvalidated&&(!a.meridiem||a.meridiem&&c),i._strict&&(h=h&&a.charsLeftOver===0&&a.unusedTokens.length===0&&a.bigHour===void 0)),Object.isFrozen==null||!Object.isFrozen(i))i._isValid=h;else return h;return i._isValid}function E(i){var a=y(NaN);return i!=null?p(A(a),i):A(a).userInvalidated=!0,a}var _=e.momentProperties=[],F=!1;function z(i,a){var c,h,w,k=_.length;if(u(a._isAMomentObject)||(i._isAMomentObject=a._isAMomentObject),u(a._i)||(i._i=a._i),u(a._f)||(i._f=a._f),u(a._l)||(i._l=a._l),u(a._strict)||(i._strict=a._strict),u(a._tzm)||(i._tzm=a._tzm),u(a._isUTC)||(i._isUTC=a._isUTC),u(a._offset)||(i._offset=a._offset),u(a._pf)||(i._pf=A(a)),u(a._locale)||(i._locale=a._locale),k>0)for(c=0;c<k;c++)h=_[c],w=a[h],u(w)||(i[h]=w);return i}function W(i){z(this,i),this._d=new Date(i._d!=null?i._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),F===!1&&(F=!0,e.updateOffset(this),F=!1)}function C(i){return i instanceof W||i!=null&&i._isAMomentObject!=null}function te(i){e.suppressDeprecationWarnings===!1&&typeof console<"u"&&console.warn&&console.warn("Deprecation warning: "+i)}function X(i,a){var c=!0;return p(function(){if(e.deprecationHandler!=null&&e.deprecationHandler(null,i),c){var h=[],w,k,v,O=arguments.length;for(k=0;k<O;k++){if(w="",typeof arguments[k]=="object"){w+=`
[`+k+"] ";for(v in arguments[0])o(arguments[0],v)&&(w+=v+": "+arguments[0][v]+", ");w=w.slice(0,-2)}else w=arguments[k];h.push(w)}te(i+`
Arguments: `+Array.prototype.slice.call(h).join("")+`
`+new Error().stack),c=!1}return a.apply(this,arguments)},a)}var Z={};function oe(i,a){e.deprecationHandler!=null&&e.deprecationHandler(i,a),Z[i]||(te(a),Z[i]=!0)}e.suppressDeprecationWarnings=!1,e.deprecationHandler=null;function de(i){return typeof Function<"u"&&i instanceof Function||Object.prototype.toString.call(i)==="[object Function]"}function re(i){var a,c;for(c in i)o(i,c)&&(a=i[c],de(a)?this[c]=a:this["_"+c]=a);this._config=i,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function Y(i,a){var c=p({},i),h;for(h in a)o(a,h)&&(s(i[h])&&s(a[h])?(c[h]={},p(c[h],i[h]),p(c[h],a[h])):a[h]!=null?c[h]=a[h]:delete c[h]);for(h in i)o(i,h)&&!o(a,h)&&s(i[h])&&(c[h]=p({},c[h]));return c}function q(i){i!=null&&this.set(i)}var U;Object.keys?U=Object.keys:U=function(i){var a,c=[];for(a in i)o(i,a)&&c.push(a);return c};var ie={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function ge(i,a,c){var h=this._calendar[i]||this._calendar.sameElse;return de(h)?h.call(a,c):h}function ye(i,a,c){var h=""+Math.abs(i),w=a-h.length,k=i>=0;return(k?c?"+":"":"-")+Math.pow(10,Math.max(0,w)).toString().substr(1)+h}var Ze=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,x=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,De={},Oe={};function b(i,a,c,h){var w=h;typeof h=="string"&&(w=function(){return this[h]()}),i&&(Oe[i]=w),a&&(Oe[a[0]]=function(){return ye(w.apply(this,arguments),a[1],a[2])}),c&&(Oe[c]=function(){return this.localeData().ordinal(w.apply(this,arguments),i)})}function ze(i){return i.match(/\[[\s\S]/)?i.replace(/^\[|\]$/g,""):i.replace(/\\/g,"")}function Nt(i){var a=i.match(Ze),c,h;for(c=0,h=a.length;c<h;c++)Oe[a[c]]?a[c]=Oe[a[c]]:a[c]=ze(a[c]);return function(w){var k="",v;for(v=0;v<h;v++)k+=de(a[v])?a[v].call(w,i):a[v];return k}}function sn(i,a){return i.isValid()?(a=An(a,i.localeData()),De[a]=De[a]||Nt(a),De[a](i)):i.localeData().invalidDate()}function An(i,a){var c=5;function h(w){return a.longDateFormat(w)||w}for(x.lastIndex=0;c>=0&&x.test(i);)i=i.replace(x,h),x.lastIndex=0,c-=1;return i}var Ce={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function En(i){var a=this._longDateFormat[i],c=this._longDateFormat[i.toUpperCase()];return a||!c?a:(this._longDateFormat[i]=c.match(Ze).map(function(h){return h==="MMMM"||h==="MM"||h==="DD"||h==="dddd"?h.slice(1):h}).join(""),this._longDateFormat[i])}var kt="Invalid date";function Bt(){return this._invalidDate}var on="%d",Pr=/\d{1,2}/;function Ko(i){return this._ordinal.replace("%d",i)}var ki={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function xi(i,a,c,h){var w=this._relativeTime[c];return de(w)?w(i,a,c,h):w.replace(/%d/i,i)}function Ai(i,a){var c=this._relativeTime[i>0?"future":"past"];return de(c)?c(a):c.replace(/%s/i,a)}var Ei={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function We(i){return typeof i=="string"?Ei[i]||Ei[i.toLowerCase()]:void 0}function Un(i){var a={},c,h;for(h in i)o(i,h)&&(c=We(h),c&&(a[c]=i[h]));return a}var Xo={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};function Zo(i){var a=[],c;for(c in i)o(i,c)&&a.push({unit:c,priority:Xo[c]});return a.sort(function(h,w){return h.priority-w.priority}),a}var vi=/\d/,Ue=/\d\d/,Ci=/\d{3}/,T=/\d{4}/,R=/[+-]?\d{6}/,D=/\d\d?/,J=/\d\d\d\d?/,ae=/\d\d\d\d\d\d?/,Le=/\d{1,3}/,xt=/\d{1,4}/,Je=/[+-]?\d{1,6}/,lt=/\d+/,_t=/[+-]?\d+/,He=/Z|[+-]\d\d:?\d\d/gi,At=/Z|[+-]\d\d(?::?\d\d)?/gi,Et=/[+-]?\d+(\.\d{1,3})?/,Ir=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,Hn=/^[1-9]\d?/,Jo=/^([1-9]\d|\d)/,Si;Si={};function N(i,a,c){Si[i]=de(a)?a:function(h,w){return h&&c?c:a}}function Gp(i,a){return o(Si,i)?Si[i](a._strict,a._locale):new RegExp(jp(i))}function jp(i){return $t(i.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(a,c,h,w,k){return c||h||w||k}))}function $t(i){return i.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function ut(i){return i<0?Math.ceil(i)||0:Math.floor(i)}function Q(i){var a=+i,c=0;return a!==0&&isFinite(a)&&(c=ut(a)),c}var Qo={};function le(i,a){var c,h=a,w;for(typeof i=="string"&&(i=[i]),d(a)&&(h=function(k,v){v[a]=Q(k)}),w=i.length,c=0;c<w;c++)Qo[i[c]]=h}function Rr(i,a){le(i,function(c,h,w,k){w._w=w._w||{},a(c,w._w,w,k)})}function Kp(i,a,c){a!=null&&o(Qo,i)&&Qo[i](a,c._a,c,i)}function Ti(i){return i%4===0&&i%100!==0||i%400===0}var Me=0,Yt=1,Ft=2,Se=3,vt=4,Vt=5,vn=6,Xp=7,Zp=8;b("Y",0,0,function(){var i=this.year();return i<=9999?ye(i,4):"+"+i}),b(0,["YY",2],0,function(){return this.year()%100}),b(0,["YYYY",4],0,"year"),b(0,["YYYYY",5],0,"year"),b(0,["YYYYYY",6,!0],0,"year"),N("Y",_t),N("YY",D,Ue),N("YYYY",xt,T),N("YYYYY",Je,R),N("YYYYYY",Je,R),le(["YYYYY","YYYYYY"],Me),le("YYYY",function(i,a){a[Me]=i.length===2?e.parseTwoDigitYear(i):Q(i)}),le("YY",function(i,a){a[Me]=e.parseTwoDigitYear(i)}),le("Y",function(i,a){a[Me]=parseInt(i,10)});function Dr(i){return Ti(i)?366:365}e.parseTwoDigitYear=function(i){return Q(i)+(Q(i)>68?1900:2e3)};var Du=qn("FullYear",!0);function Jp(){return Ti(this.year())}function qn(i,a){return function(c){return c!=null?(Ou(this,i,c),e.updateOffset(this,a),this):Or(this,i)}}function Or(i,a){if(!i.isValid())return NaN;var c=i._d,h=i._isUTC;switch(a){case"Milliseconds":return h?c.getUTCMilliseconds():c.getMilliseconds();case"Seconds":return h?c.getUTCSeconds():c.getSeconds();case"Minutes":return h?c.getUTCMinutes():c.getMinutes();case"Hours":return h?c.getUTCHours():c.getHours();case"Date":return h?c.getUTCDate():c.getDate();case"Day":return h?c.getUTCDay():c.getDay();case"Month":return h?c.getUTCMonth():c.getMonth();case"FullYear":return h?c.getUTCFullYear():c.getFullYear();default:return NaN}}function Ou(i,a,c){var h,w,k,v,O;if(!(!i.isValid()||isNaN(c))){switch(h=i._d,w=i._isUTC,a){case"Milliseconds":return void(w?h.setUTCMilliseconds(c):h.setMilliseconds(c));case"Seconds":return void(w?h.setUTCSeconds(c):h.setSeconds(c));case"Minutes":return void(w?h.setUTCMinutes(c):h.setMinutes(c));case"Hours":return void(w?h.setUTCHours(c):h.setHours(c));case"Date":return void(w?h.setUTCDate(c):h.setDate(c));case"FullYear":break;default:return}k=c,v=i.month(),O=i.date(),O=O===29&&v===1&&!Ti(k)?28:O,w?h.setUTCFullYear(k,v,O):h.setFullYear(k,v,O)}}function Qp(i){return i=We(i),de(this[i])?this[i]():this}function eg(i,a){if(typeof i=="object"){i=Un(i);var c=Zo(i),h,w=c.length;for(h=0;h<w;h++)this[c[h].unit](i[c[h].unit])}else if(i=We(i),de(this[i]))return this[i](a);return this}function tg(i,a){return(i%a+a)%a}var xe;Array.prototype.indexOf?xe=Array.prototype.indexOf:xe=function(i){var a;for(a=0;a<this.length;++a)if(this[a]===i)return a;return-1};function ea(i,a){if(isNaN(i)||isNaN(a))return NaN;var c=tg(a,12);return i+=(a-c)/12,c===1?Ti(i)?29:28:31-c%7%2}b("M",["MM",2],"Mo",function(){return this.month()+1}),b("MMM",0,0,function(i){return this.localeData().monthsShort(this,i)}),b("MMMM",0,0,function(i){return this.localeData().months(this,i)}),N("M",D,Hn),N("MM",D,Ue),N("MMM",function(i,a){return a.monthsShortRegex(i)}),N("MMMM",function(i,a){return a.monthsRegex(i)}),le(["M","MM"],function(i,a){a[Yt]=Q(i)-1}),le(["MMM","MMMM"],function(i,a,c,h){var w=c._locale.monthsParse(i,h,c._strict);w!=null?a[Yt]=w:A(c).invalidMonth=i});var ng="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Lu="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Mu=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,rg=Ir,ig=Ir;function sg(i,a){return i?r(this._months)?this._months[i.month()]:this._months[(this._months.isFormat||Mu).test(a)?"format":"standalone"][i.month()]:r(this._months)?this._months:this._months.standalone}function og(i,a){return i?r(this._monthsShort)?this._monthsShort[i.month()]:this._monthsShort[Mu.test(a)?"format":"standalone"][i.month()]:r(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function ag(i,a,c){var h,w,k,v=i.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],h=0;h<12;++h)k=y([2e3,h]),this._shortMonthsParse[h]=this.monthsShort(k,"").toLocaleLowerCase(),this._longMonthsParse[h]=this.months(k,"").toLocaleLowerCase();return c?a==="MMM"?(w=xe.call(this._shortMonthsParse,v),w!==-1?w:null):(w=xe.call(this._longMonthsParse,v),w!==-1?w:null):a==="MMM"?(w=xe.call(this._shortMonthsParse,v),w!==-1?w:(w=xe.call(this._longMonthsParse,v),w!==-1?w:null)):(w=xe.call(this._longMonthsParse,v),w!==-1?w:(w=xe.call(this._shortMonthsParse,v),w!==-1?w:null))}function lg(i,a,c){var h,w,k;if(this._monthsParseExact)return ag.call(this,i,a,c);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),h=0;h<12;h++){if(w=y([2e3,h]),c&&!this._longMonthsParse[h]&&(this._longMonthsParse[h]=new RegExp("^"+this.months(w,"").replace(".","")+"$","i"),this._shortMonthsParse[h]=new RegExp("^"+this.monthsShort(w,"").replace(".","")+"$","i")),!c&&!this._monthsParse[h]&&(k="^"+this.months(w,"")+"|^"+this.monthsShort(w,""),this._monthsParse[h]=new RegExp(k.replace(".",""),"i")),c&&a==="MMMM"&&this._longMonthsParse[h].test(i))return h;if(c&&a==="MMM"&&this._shortMonthsParse[h].test(i))return h;if(!c&&this._monthsParse[h].test(i))return h}}function Nu(i,a){if(!i.isValid())return i;if(typeof a=="string"){if(/^\d+$/.test(a))a=Q(a);else if(a=i.localeData().monthsParse(a),!d(a))return i}var c=a,h=i.date();return h=h<29?h:Math.min(h,ea(i.year(),c)),i._isUTC?i._d.setUTCMonth(c,h):i._d.setMonth(c,h),i}function Bu(i){return i!=null?(Nu(this,i),e.updateOffset(this,!0),this):Or(this,"Month")}function ug(){return ea(this.year(),this.month())}function cg(i){return this._monthsParseExact?(o(this,"_monthsRegex")||$u.call(this),i?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=rg),this._monthsShortStrictRegex&&i?this._monthsShortStrictRegex:this._monthsShortRegex)}function dg(i){return this._monthsParseExact?(o(this,"_monthsRegex")||$u.call(this),i?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=ig),this._monthsStrictRegex&&i?this._monthsStrictRegex:this._monthsRegex)}function $u(){function i(H,ee){return ee.length-H.length}var a=[],c=[],h=[],w,k,v,O;for(w=0;w<12;w++)k=y([2e3,w]),v=$t(this.monthsShort(k,"")),O=$t(this.months(k,"")),a.push(v),c.push(O),h.push(O),h.push(v);a.sort(i),c.sort(i),h.sort(i),this._monthsRegex=new RegExp("^("+h.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+c.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function fg(i,a,c,h,w,k,v){var O;return i<100&&i>=0?(O=new Date(i+400,a,c,h,w,k,v),isFinite(O.getFullYear())&&O.setFullYear(i)):O=new Date(i,a,c,h,w,k,v),O}function Lr(i){var a,c;return i<100&&i>=0?(c=Array.prototype.slice.call(arguments),c[0]=i+400,a=new Date(Date.UTC.apply(null,c)),isFinite(a.getUTCFullYear())&&a.setUTCFullYear(i)):a=new Date(Date.UTC.apply(null,arguments)),a}function _i(i,a,c){var h=7+a-c,w=(7+Lr(i,0,h).getUTCDay()-a)%7;return-w+h-1}function Yu(i,a,c,h,w){var k=(7+c-h)%7,v=_i(i,h,w),O=1+7*(a-1)+k+v,H,ee;return O<=0?(H=i-1,ee=Dr(H)+O):O>Dr(i)?(H=i+1,ee=O-Dr(i)):(H=i,ee=O),{year:H,dayOfYear:ee}}function Mr(i,a,c){var h=_i(i.year(),a,c),w=Math.floor((i.dayOfYear()-h-1)/7)+1,k,v;return w<1?(v=i.year()-1,k=w+zt(v,a,c)):w>zt(i.year(),a,c)?(k=w-zt(i.year(),a,c),v=i.year()+1):(v=i.year(),k=w),{week:k,year:v}}function zt(i,a,c){var h=_i(i,a,c),w=_i(i+1,a,c);return(Dr(i)-h+w)/7}b("w",["ww",2],"wo","week"),b("W",["WW",2],"Wo","isoWeek"),N("w",D,Hn),N("ww",D,Ue),N("W",D,Hn),N("WW",D,Ue),Rr(["w","ww","W","WW"],function(i,a,c,h){a[h.substr(0,1)]=Q(i)});function hg(i){return Mr(i,this._week.dow,this._week.doy).week}var mg={dow:0,doy:6};function pg(){return this._week.dow}function gg(){return this._week.doy}function wg(i){var a=this.localeData().week(this);return i==null?a:this.add((i-a)*7,"d")}function yg(i){var a=Mr(this,1,4).week;return i==null?a:this.add((i-a)*7,"d")}b("d",0,"do","day"),b("dd",0,0,function(i){return this.localeData().weekdaysMin(this,i)}),b("ddd",0,0,function(i){return this.localeData().weekdaysShort(this,i)}),b("dddd",0,0,function(i){return this.localeData().weekdays(this,i)}),b("e",0,0,"weekday"),b("E",0,0,"isoWeekday"),N("d",D),N("e",D),N("E",D),N("dd",function(i,a){return a.weekdaysMinRegex(i)}),N("ddd",function(i,a){return a.weekdaysShortRegex(i)}),N("dddd",function(i,a){return a.weekdaysRegex(i)}),Rr(["dd","ddd","dddd"],function(i,a,c,h){var w=c._locale.weekdaysParse(i,h,c._strict);w!=null?a.d=w:A(c).invalidWeekday=i}),Rr(["d","e","E"],function(i,a,c,h){a[h]=Q(i)});function bg(i,a){return typeof i!="string"?i:isNaN(i)?(i=a.weekdaysParse(i),typeof i=="number"?i:null):parseInt(i,10)}function kg(i,a){return typeof i=="string"?a.weekdaysParse(i)%7||7:isNaN(i)?null:i}function ta(i,a){return i.slice(a,7).concat(i.slice(0,a))}var xg="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Vu="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ag="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Eg=Ir,vg=Ir,Cg=Ir;function Sg(i,a){var c=r(this._weekdays)?this._weekdays:this._weekdays[i&&i!==!0&&this._weekdays.isFormat.test(a)?"format":"standalone"];return i===!0?ta(c,this._week.dow):i?c[i.day()]:c}function Tg(i){return i===!0?ta(this._weekdaysShort,this._week.dow):i?this._weekdaysShort[i.day()]:this._weekdaysShort}function _g(i){return i===!0?ta(this._weekdaysMin,this._week.dow):i?this._weekdaysMin[i.day()]:this._weekdaysMin}function Fg(i,a,c){var h,w,k,v=i.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],h=0;h<7;++h)k=y([2e3,1]).day(h),this._minWeekdaysParse[h]=this.weekdaysMin(k,"").toLocaleLowerCase(),this._shortWeekdaysParse[h]=this.weekdaysShort(k,"").toLocaleLowerCase(),this._weekdaysParse[h]=this.weekdays(k,"").toLocaleLowerCase();return c?a==="dddd"?(w=xe.call(this._weekdaysParse,v),w!==-1?w:null):a==="ddd"?(w=xe.call(this._shortWeekdaysParse,v),w!==-1?w:null):(w=xe.call(this._minWeekdaysParse,v),w!==-1?w:null):a==="dddd"?(w=xe.call(this._weekdaysParse,v),w!==-1||(w=xe.call(this._shortWeekdaysParse,v),w!==-1)?w:(w=xe.call(this._minWeekdaysParse,v),w!==-1?w:null)):a==="ddd"?(w=xe.call(this._shortWeekdaysParse,v),w!==-1||(w=xe.call(this._weekdaysParse,v),w!==-1)?w:(w=xe.call(this._minWeekdaysParse,v),w!==-1?w:null)):(w=xe.call(this._minWeekdaysParse,v),w!==-1||(w=xe.call(this._weekdaysParse,v),w!==-1)?w:(w=xe.call(this._shortWeekdaysParse,v),w!==-1?w:null))}function Pg(i,a,c){var h,w,k;if(this._weekdaysParseExact)return Fg.call(this,i,a,c);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),h=0;h<7;h++){if(w=y([2e3,1]).day(h),c&&!this._fullWeekdaysParse[h]&&(this._fullWeekdaysParse[h]=new RegExp("^"+this.weekdays(w,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[h]=new RegExp("^"+this.weekdaysShort(w,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[h]=new RegExp("^"+this.weekdaysMin(w,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[h]||(k="^"+this.weekdays(w,"")+"|^"+this.weekdaysShort(w,"")+"|^"+this.weekdaysMin(w,""),this._weekdaysParse[h]=new RegExp(k.replace(".",""),"i")),c&&a==="dddd"&&this._fullWeekdaysParse[h].test(i))return h;if(c&&a==="ddd"&&this._shortWeekdaysParse[h].test(i))return h;if(c&&a==="dd"&&this._minWeekdaysParse[h].test(i))return h;if(!c&&this._weekdaysParse[h].test(i))return h}}function Ig(i){if(!this.isValid())return i!=null?this:NaN;var a=Or(this,"Day");return i!=null?(i=bg(i,this.localeData()),this.add(i-a,"d")):a}function Rg(i){if(!this.isValid())return i!=null?this:NaN;var a=(this.day()+7-this.localeData()._week.dow)%7;return i==null?a:this.add(i-a,"d")}function Dg(i){if(!this.isValid())return i!=null?this:NaN;if(i!=null){var a=kg(i,this.localeData());return this.day(this.day()%7?a:a-7)}else return this.day()||7}function Og(i){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||na.call(this),i?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Eg),this._weekdaysStrictRegex&&i?this._weekdaysStrictRegex:this._weekdaysRegex)}function Lg(i){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||na.call(this),i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=vg),this._weekdaysShortStrictRegex&&i?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Mg(i){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||na.call(this),i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Cg),this._weekdaysMinStrictRegex&&i?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function na(){function i(qe,Gt){return Gt.length-qe.length}var a=[],c=[],h=[],w=[],k,v,O,H,ee;for(k=0;k<7;k++)v=y([2e3,1]).day(k),O=$t(this.weekdaysMin(v,"")),H=$t(this.weekdaysShort(v,"")),ee=$t(this.weekdays(v,"")),a.push(O),c.push(H),h.push(ee),w.push(O),w.push(H),w.push(ee);a.sort(i),c.sort(i),h.sort(i),w.sort(i),this._weekdaysRegex=new RegExp("^("+w.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+h.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+a.join("|")+")","i")}function ra(){return this.hours()%12||12}function Ng(){return this.hours()||24}b("H",["HH",2],0,"hour"),b("h",["hh",2],0,ra),b("k",["kk",2],0,Ng),b("hmm",0,0,function(){return""+ra.apply(this)+ye(this.minutes(),2)}),b("hmmss",0,0,function(){return""+ra.apply(this)+ye(this.minutes(),2)+ye(this.seconds(),2)}),b("Hmm",0,0,function(){return""+this.hours()+ye(this.minutes(),2)}),b("Hmmss",0,0,function(){return""+this.hours()+ye(this.minutes(),2)+ye(this.seconds(),2)});function zu(i,a){b(i,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),a)})}zu("a",!0),zu("A",!1);function Wu(i,a){return a._meridiemParse}N("a",Wu),N("A",Wu),N("H",D,Jo),N("h",D,Hn),N("k",D,Hn),N("HH",D,Ue),N("hh",D,Ue),N("kk",D,Ue),N("hmm",J),N("hmmss",ae),N("Hmm",J),N("Hmmss",ae),le(["H","HH"],Se),le(["k","kk"],function(i,a,c){var h=Q(i);a[Se]=h===24?0:h}),le(["a","A"],function(i,a,c){c._isPm=c._locale.isPM(i),c._meridiem=i}),le(["h","hh"],function(i,a,c){a[Se]=Q(i),A(c).bigHour=!0}),le("hmm",function(i,a,c){var h=i.length-2;a[Se]=Q(i.substr(0,h)),a[vt]=Q(i.substr(h)),A(c).bigHour=!0}),le("hmmss",function(i,a,c){var h=i.length-4,w=i.length-2;a[Se]=Q(i.substr(0,h)),a[vt]=Q(i.substr(h,2)),a[Vt]=Q(i.substr(w)),A(c).bigHour=!0}),le("Hmm",function(i,a,c){var h=i.length-2;a[Se]=Q(i.substr(0,h)),a[vt]=Q(i.substr(h))}),le("Hmmss",function(i,a,c){var h=i.length-4,w=i.length-2;a[Se]=Q(i.substr(0,h)),a[vt]=Q(i.substr(h,2)),a[Vt]=Q(i.substr(w))});function Bg(i){return(i+"").toLowerCase().charAt(0)==="p"}var $g=/[ap]\.?m?\.?/i,Yg=qn("Hours",!0);function Vg(i,a,c){return i>11?c?"pm":"PM":c?"am":"AM"}var Uu={calendar:ie,longDateFormat:Ce,invalidDate:kt,ordinal:on,dayOfMonthOrdinalParse:Pr,relativeTime:ki,months:ng,monthsShort:Lu,week:mg,weekdays:xg,weekdaysMin:Ag,weekdaysShort:Vu,meridiemParse:$g},me={},Nr={},Br;function zg(i,a){var c,h=Math.min(i.length,a.length);for(c=0;c<h;c+=1)if(i[c]!==a[c])return c;return h}function Hu(i){return i&&i.toLowerCase().replace("_","-")}function Wg(i){for(var a=0,c,h,w,k;a<i.length;){for(k=Hu(i[a]).split("-"),c=k.length,h=Hu(i[a+1]),h=h?h.split("-"):null;c>0;){if(w=Fi(k.slice(0,c).join("-")),w)return w;if(h&&h.length>=c&&zg(k,h)>=c-1)break;c--}a++}return Br}function Ug(i){return!!(i&&i.match("^[^/\\\\]*$"))}function Fi(i){var a=null,c;if(me[i]===void 0&&typeof Jn<"u"&&Jn&&Jn.exports&&Ug(i))try{a=Br._abbr,c=require,c("./locale/"+i),an(a)}catch{me[i]=null}return me[i]}function an(i,a){var c;return i&&(u(a)?c=Wt(i):c=ia(i,a),c?Br=c:typeof console<"u"&&console.warn&&console.warn("Locale "+i+" not found. Did you forget to load it?")),Br._abbr}function ia(i,a){if(a!==null){var c,h=Uu;if(a.abbr=i,me[i]!=null)oe("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),h=me[i]._config;else if(a.parentLocale!=null)if(me[a.parentLocale]!=null)h=me[a.parentLocale]._config;else if(c=Fi(a.parentLocale),c!=null)h=c._config;else return Nr[a.parentLocale]||(Nr[a.parentLocale]=[]),Nr[a.parentLocale].push({name:i,config:a}),null;return me[i]=new q(Y(h,a)),Nr[i]&&Nr[i].forEach(function(w){ia(w.name,w.config)}),an(i),me[i]}else return delete me[i],null}function Hg(i,a){if(a!=null){var c,h,w=Uu;me[i]!=null&&me[i].parentLocale!=null?me[i].set(Y(me[i]._config,a)):(h=Fi(i),h!=null&&(w=h._config),a=Y(w,a),h==null&&(a.abbr=i),c=new q(a),c.parentLocale=me[i],me[i]=c),an(i)}else me[i]!=null&&(me[i].parentLocale!=null?(me[i]=me[i].parentLocale,i===an()&&an(i)):me[i]!=null&&delete me[i]);return me[i]}function Wt(i){var a;if(i&&i._locale&&i._locale._abbr&&(i=i._locale._abbr),!i)return Br;if(!r(i)){if(a=Fi(i),a)return a;i=[i]}return Wg(i)}function qg(){return U(me)}function sa(i){var a,c=i._a;return c&&A(i).overflow===-2&&(a=c[Yt]<0||c[Yt]>11?Yt:c[Ft]<1||c[Ft]>ea(c[Me],c[Yt])?Ft:c[Se]<0||c[Se]>24||c[Se]===24&&(c[vt]!==0||c[Vt]!==0||c[vn]!==0)?Se:c[vt]<0||c[vt]>59?vt:c[Vt]<0||c[Vt]>59?Vt:c[vn]<0||c[vn]>999?vn:-1,A(i)._overflowDayOfYear&&(a<Me||a>Ft)&&(a=Ft),A(i)._overflowWeeks&&a===-1&&(a=Xp),A(i)._overflowWeekday&&a===-1&&(a=Zp),A(i).overflow=a),i}var Gg=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,jg=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Kg=/Z|[+-]\d\d(?::?\d\d)?/,Pi=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],oa=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Xg=/^\/?Date\((-?\d+)/i,Zg=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Jg={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function qu(i){var a,c,h=i._i,w=Gg.exec(h)||jg.exec(h),k,v,O,H,ee=Pi.length,qe=oa.length;if(w){for(A(i).iso=!0,a=0,c=ee;a<c;a++)if(Pi[a][1].exec(w[1])){v=Pi[a][0],k=Pi[a][2]!==!1;break}if(v==null){i._isValid=!1;return}if(w[3]){for(a=0,c=qe;a<c;a++)if(oa[a][1].exec(w[3])){O=(w[2]||" ")+oa[a][0];break}if(O==null){i._isValid=!1;return}}if(!k&&O!=null){i._isValid=!1;return}if(w[4])if(Kg.exec(w[4]))H="Z";else{i._isValid=!1;return}i._f=v+(O||"")+(H||""),la(i)}else i._isValid=!1}function Qg(i,a,c,h,w,k){var v=[e0(i),Lu.indexOf(a),parseInt(c,10),parseInt(h,10),parseInt(w,10)];return k&&v.push(parseInt(k,10)),v}function e0(i){var a=parseInt(i,10);return a<=49?2e3+a:a<=999?1900+a:a}function t0(i){return i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function n0(i,a,c){if(i){var h=Vu.indexOf(i),w=new Date(a[0],a[1],a[2]).getDay();if(h!==w)return A(c).weekdayMismatch=!0,c._isValid=!1,!1}return!0}function r0(i,a,c){if(i)return Jg[i];if(a)return 0;var h=parseInt(c,10),w=h%100,k=(h-w)/100;return k*60+w}function Gu(i){var a=Zg.exec(t0(i._i)),c;if(a){if(c=Qg(a[4],a[3],a[2],a[5],a[6],a[7]),!n0(a[1],c,i))return;i._a=c,i._tzm=r0(a[8],a[9],a[10]),i._d=Lr.apply(null,i._a),i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),A(i).rfc2822=!0}else i._isValid=!1}function i0(i){var a=Xg.exec(i._i);if(a!==null){i._d=new Date(+a[1]);return}if(qu(i),i._isValid===!1)delete i._isValid;else return;if(Gu(i),i._isValid===!1)delete i._isValid;else return;i._strict?i._isValid=!1:e.createFromInputFallback(i)}e.createFromInputFallback=X("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(i){i._d=new Date(i._i+(i._useUTC?" UTC":""))});function Gn(i,a,c){return i??a??c}function s0(i){var a=new Date(e.now());return i._useUTC?[a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate()]:[a.getFullYear(),a.getMonth(),a.getDate()]}function aa(i){var a,c,h=[],w,k,v;if(!i._d){for(w=s0(i),i._w&&i._a[Ft]==null&&i._a[Yt]==null&&o0(i),i._dayOfYear!=null&&(v=Gn(i._a[Me],w[Me]),(i._dayOfYear>Dr(v)||i._dayOfYear===0)&&(A(i)._overflowDayOfYear=!0),c=Lr(v,0,i._dayOfYear),i._a[Yt]=c.getUTCMonth(),i._a[Ft]=c.getUTCDate()),a=0;a<3&&i._a[a]==null;++a)i._a[a]=h[a]=w[a];for(;a<7;a++)i._a[a]=h[a]=i._a[a]==null?a===2?1:0:i._a[a];i._a[Se]===24&&i._a[vt]===0&&i._a[Vt]===0&&i._a[vn]===0&&(i._nextDay=!0,i._a[Se]=0),i._d=(i._useUTC?Lr:fg).apply(null,h),k=i._useUTC?i._d.getUTCDay():i._d.getDay(),i._tzm!=null&&i._d.setUTCMinutes(i._d.getUTCMinutes()-i._tzm),i._nextDay&&(i._a[Se]=24),i._w&&typeof i._w.d<"u"&&i._w.d!==k&&(A(i).weekdayMismatch=!0)}}function o0(i){var a,c,h,w,k,v,O,H,ee;a=i._w,a.GG!=null||a.W!=null||a.E!=null?(k=1,v=4,c=Gn(a.GG,i._a[Me],Mr(fe(),1,4).year),h=Gn(a.W,1),w=Gn(a.E,1),(w<1||w>7)&&(H=!0)):(k=i._locale._week.dow,v=i._locale._week.doy,ee=Mr(fe(),k,v),c=Gn(a.gg,i._a[Me],ee.year),h=Gn(a.w,ee.week),a.d!=null?(w=a.d,(w<0||w>6)&&(H=!0)):a.e!=null?(w=a.e+k,(a.e<0||a.e>6)&&(H=!0)):w=k),h<1||h>zt(c,k,v)?A(i)._overflowWeeks=!0:H!=null?A(i)._overflowWeekday=!0:(O=Yu(c,h,w,k,v),i._a[Me]=O.year,i._dayOfYear=O.dayOfYear)}e.ISO_8601=function(){},e.RFC_2822=function(){};function la(i){if(i._f===e.ISO_8601){qu(i);return}if(i._f===e.RFC_2822){Gu(i);return}i._a=[],A(i).empty=!0;var a=""+i._i,c,h,w,k,v,O=a.length,H=0,ee,qe;for(w=An(i._f,i._locale).match(Ze)||[],qe=w.length,c=0;c<qe;c++)k=w[c],h=(a.match(Gp(k,i))||[])[0],h&&(v=a.substr(0,a.indexOf(h)),v.length>0&&A(i).unusedInput.push(v),a=a.slice(a.indexOf(h)+h.length),H+=h.length),Oe[k]?(h?A(i).empty=!1:A(i).unusedTokens.push(k),Kp(k,h,i)):i._strict&&!h&&A(i).unusedTokens.push(k);A(i).charsLeftOver=O-H,a.length>0&&A(i).unusedInput.push(a),i._a[Se]<=12&&A(i).bigHour===!0&&i._a[Se]>0&&(A(i).bigHour=void 0),A(i).parsedDateParts=i._a.slice(0),A(i).meridiem=i._meridiem,i._a[Se]=a0(i._locale,i._a[Se],i._meridiem),ee=A(i).era,ee!==null&&(i._a[Me]=i._locale.erasConvertYear(ee,i._a[Me])),aa(i),sa(i)}function a0(i,a,c){var h;return c==null?a:i.meridiemHour!=null?i.meridiemHour(a,c):(i.isPM!=null&&(h=i.isPM(c),h&&a<12&&(a+=12),!h&&a===12&&(a=0)),a)}function l0(i){var a,c,h,w,k,v,O=!1,H=i._f.length;if(H===0){A(i).invalidFormat=!0,i._d=new Date(NaN);return}for(w=0;w<H;w++)k=0,v=!1,a=z({},i),i._useUTC!=null&&(a._useUTC=i._useUTC),a._f=i._f[w],la(a),S(a)&&(v=!0),k+=A(a).charsLeftOver,k+=A(a).unusedTokens.length*10,A(a).score=k,O?k<h&&(h=k,c=a):(h==null||k<h||v)&&(h=k,c=a,v&&(O=!0));p(i,c||a)}function u0(i){if(!i._d){var a=Un(i._i),c=a.day===void 0?a.date:a.day;i._a=m([a.year,a.month,c,a.hour,a.minute,a.second,a.millisecond],function(h){return h&&parseInt(h,10)}),aa(i)}}function c0(i){var a=new W(sa(ju(i)));return a._nextDay&&(a.add(1,"d"),a._nextDay=void 0),a}function ju(i){var a=i._i,c=i._f;return i._locale=i._locale||Wt(i._l),a===null||c===void 0&&a===""?E({nullInput:!0}):(typeof a=="string"&&(i._i=a=i._locale.preparse(a)),C(a)?new W(sa(a)):(f(a)?i._d=a:r(c)?l0(i):c?la(i):d0(i),S(i)||(i._d=null),i))}function d0(i){var a=i._i;u(a)?i._d=new Date(e.now()):f(a)?i._d=new Date(a.valueOf()):typeof a=="string"?i0(i):r(a)?(i._a=m(a.slice(0),function(c){return parseInt(c,10)}),aa(i)):s(a)?u0(i):d(a)?i._d=new Date(a):e.createFromInputFallback(i)}function Ku(i,a,c,h,w){var k={};return(a===!0||a===!1)&&(h=a,a=void 0),(c===!0||c===!1)&&(h=c,c=void 0),(s(i)&&l(i)||r(i)&&i.length===0)&&(i=void 0),k._isAMomentObject=!0,k._useUTC=k._isUTC=w,k._l=c,k._i=i,k._f=a,k._strict=h,c0(k)}function fe(i,a,c,h){return Ku(i,a,c,h,!1)}var f0=X("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var i=fe.apply(null,arguments);return this.isValid()&&i.isValid()?i<this?this:i:E()}),h0=X("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var i=fe.apply(null,arguments);return this.isValid()&&i.isValid()?i>this?this:i:E()});function Xu(i,a){var c,h;if(a.length===1&&r(a[0])&&(a=a[0]),!a.length)return fe();for(c=a[0],h=1;h<a.length;++h)(!a[h].isValid()||a[h][i](c))&&(c=a[h]);return c}function m0(){var i=[].slice.call(arguments,0);return Xu("isBefore",i)}function p0(){var i=[].slice.call(arguments,0);return Xu("isAfter",i)}var g0=function(){return Date.now?Date.now():+new Date},$r=["year","quarter","month","week","day","hour","minute","second","millisecond"];function w0(i){var a,c=!1,h,w=$r.length;for(a in i)if(o(i,a)&&!(xe.call($r,a)!==-1&&(i[a]==null||!isNaN(i[a]))))return!1;for(h=0;h<w;++h)if(i[$r[h]]){if(c)return!1;parseFloat(i[$r[h]])!==Q(i[$r[h]])&&(c=!0)}return!0}function y0(){return this._isValid}function b0(){return Ct(NaN)}function Ii(i){var a=Un(i),c=a.year||0,h=a.quarter||0,w=a.month||0,k=a.week||a.isoWeek||0,v=a.day||0,O=a.hour||0,H=a.minute||0,ee=a.second||0,qe=a.millisecond||0;this._isValid=w0(a),this._milliseconds=+qe+ee*1e3+H*6e4+O*1e3*60*60,this._days=+v+k*7,this._months=+w+h*3+c*12,this._data={},this._locale=Wt(),this._bubble()}function Ri(i){return i instanceof Ii}function ua(i){return i<0?Math.round(-1*i)*-1:Math.round(i)}function k0(i,a,c){var h=Math.min(i.length,a.length),w=Math.abs(i.length-a.length),k=0,v;for(v=0;v<h;v++)(c&&i[v]!==a[v]||!c&&Q(i[v])!==Q(a[v]))&&k++;return k+w}function Zu(i,a){b(i,0,0,function(){var c=this.utcOffset(),h="+";return c<0&&(c=-c,h="-"),h+ye(~~(c/60),2)+a+ye(~~c%60,2)})}Zu("Z",":"),Zu("ZZ",""),N("Z",At),N("ZZ",At),le(["Z","ZZ"],function(i,a,c){c._useUTC=!0,c._tzm=ca(At,i)});var x0=/([\+\-]|\d\d)/gi;function ca(i,a){var c=(a||"").match(i),h,w,k;return c===null?null:(h=c[c.length-1]||[],w=(h+"").match(x0)||["-",0,0],k=+(w[1]*60)+Q(w[2]),k===0?0:w[0]==="+"?k:-k)}function da(i,a){var c,h;return a._isUTC?(c=a.clone(),h=(C(i)||f(i)?i.valueOf():fe(i).valueOf())-c.valueOf(),c._d.setTime(c._d.valueOf()+h),e.updateOffset(c,!1),c):fe(i).local()}function fa(i){return-Math.round(i._d.getTimezoneOffset())}e.updateOffset=function(){};function A0(i,a,c){var h=this._offset||0,w;if(!this.isValid())return i!=null?this:NaN;if(i!=null){if(typeof i=="string"){if(i=ca(At,i),i===null)return this}else Math.abs(i)<16&&!c&&(i=i*60);return!this._isUTC&&a&&(w=fa(this)),this._offset=i,this._isUTC=!0,w!=null&&this.add(w,"m"),h!==i&&(!a||this._changeInProgress?tc(this,Ct(i-h,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,e.updateOffset(this,!0),this._changeInProgress=null)),this}else return this._isUTC?h:fa(this)}function E0(i,a){return i!=null?(typeof i!="string"&&(i=-i),this.utcOffset(i,a),this):-this.utcOffset()}function v0(i){return this.utcOffset(0,i)}function C0(i){return this._isUTC&&(this.utcOffset(0,i),this._isUTC=!1,i&&this.subtract(fa(this),"m")),this}function S0(){if(this._tzm!=null)this.utcOffset(this._tzm,!1,!0);else if(typeof this._i=="string"){var i=ca(He,this._i);i!=null?this.utcOffset(i):this.utcOffset(0,!0)}return this}function T0(i){return this.isValid()?(i=i?fe(i).utcOffset():0,(this.utcOffset()-i)%60===0):!1}function _0(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function F0(){if(!u(this._isDSTShifted))return this._isDSTShifted;var i={},a;return z(i,this),i=ju(i),i._a?(a=i._isUTC?y(i._a):fe(i._a),this._isDSTShifted=this.isValid()&&k0(i._a,a.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function P0(){return this.isValid()?!this._isUTC:!1}function I0(){return this.isValid()?this._isUTC:!1}function Ju(){return this.isValid()?this._isUTC&&this._offset===0:!1}var R0=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,D0=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ct(i,a){var c=i,h=null,w,k,v;return Ri(i)?c={ms:i._milliseconds,d:i._days,M:i._months}:d(i)||!isNaN(+i)?(c={},a?c[a]=+i:c.milliseconds=+i):(h=R0.exec(i))?(w=h[1]==="-"?-1:1,c={y:0,d:Q(h[Ft])*w,h:Q(h[Se])*w,m:Q(h[vt])*w,s:Q(h[Vt])*w,ms:Q(ua(h[vn]*1e3))*w}):(h=D0.exec(i))?(w=h[1]==="-"?-1:1,c={y:Cn(h[2],w),M:Cn(h[3],w),w:Cn(h[4],w),d:Cn(h[5],w),h:Cn(h[6],w),m:Cn(h[7],w),s:Cn(h[8],w)}):c==null?c={}:typeof c=="object"&&("from"in c||"to"in c)&&(v=O0(fe(c.from),fe(c.to)),c={},c.ms=v.milliseconds,c.M=v.months),k=new Ii(c),Ri(i)&&o(i,"_locale")&&(k._locale=i._locale),Ri(i)&&o(i,"_isValid")&&(k._isValid=i._isValid),k}Ct.fn=Ii.prototype,Ct.invalid=b0;function Cn(i,a){var c=i&&parseFloat(i.replace(",","."));return(isNaN(c)?0:c)*a}function Qu(i,a){var c={};return c.months=a.month()-i.month()+(a.year()-i.year())*12,i.clone().add(c.months,"M").isAfter(a)&&--c.months,c.milliseconds=+a-+i.clone().add(c.months,"M"),c}function O0(i,a){var c;return i.isValid()&&a.isValid()?(a=da(a,i),i.isBefore(a)?c=Qu(i,a):(c=Qu(a,i),c.milliseconds=-c.milliseconds,c.months=-c.months),c):{milliseconds:0,months:0}}function ec(i,a){return function(c,h){var w,k;return h!==null&&!isNaN(+h)&&(oe(a,"moment()."+a+"(period, number) is deprecated. Please use moment()."+a+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),k=c,c=h,h=k),w=Ct(c,h),tc(this,w,i),this}}function tc(i,a,c,h){var w=a._milliseconds,k=ua(a._days),v=ua(a._months);i.isValid()&&(h=h??!0,v&&Nu(i,Or(i,"Month")+v*c),k&&Ou(i,"Date",Or(i,"Date")+k*c),w&&i._d.setTime(i._d.valueOf()+w*c),h&&e.updateOffset(i,k||v))}var L0=ec(1,"add"),M0=ec(-1,"subtract");function nc(i){return typeof i=="string"||i instanceof String}function N0(i){return C(i)||f(i)||nc(i)||d(i)||$0(i)||B0(i)||i===null||i===void 0}function B0(i){var a=s(i)&&!l(i),c=!1,h=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],w,k,v=h.length;for(w=0;w<v;w+=1)k=h[w],c=c||o(i,k);return a&&c}function $0(i){var a=r(i),c=!1;return a&&(c=i.filter(function(h){return!d(h)&&nc(i)}).length===0),a&&c}function Y0(i){var a=s(i)&&!l(i),c=!1,h=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],w,k;for(w=0;w<h.length;w+=1)k=h[w],c=c||o(i,k);return a&&c}function V0(i,a){var c=i.diff(a,"days",!0);return c<-6?"sameElse":c<-1?"lastWeek":c<0?"lastDay":c<1?"sameDay":c<2?"nextDay":c<7?"nextWeek":"sameElse"}function z0(i,a){arguments.length===1&&(arguments[0]?N0(arguments[0])?(i=arguments[0],a=void 0):Y0(arguments[0])&&(a=arguments[0],i=void 0):(i=void 0,a=void 0));var c=i||fe(),h=da(c,this).startOf("day"),w=e.calendarFormat(this,h)||"sameElse",k=a&&(de(a[w])?a[w].call(this,c):a[w]);return this.format(k||this.localeData().calendar(w,this,fe(c)))}function W0(){return new W(this)}function U0(i,a){var c=C(i)?i:fe(i);return this.isValid()&&c.isValid()?(a=We(a)||"millisecond",a==="millisecond"?this.valueOf()>c.valueOf():c.valueOf()<this.clone().startOf(a).valueOf()):!1}function H0(i,a){var c=C(i)?i:fe(i);return this.isValid()&&c.isValid()?(a=We(a)||"millisecond",a==="millisecond"?this.valueOf()<c.valueOf():this.clone().endOf(a).valueOf()<c.valueOf()):!1}function q0(i,a,c,h){var w=C(i)?i:fe(i),k=C(a)?a:fe(a);return this.isValid()&&w.isValid()&&k.isValid()?(h=h||"()",(h[0]==="("?this.isAfter(w,c):!this.isBefore(w,c))&&(h[1]===")"?this.isBefore(k,c):!this.isAfter(k,c))):!1}function G0(i,a){var c=C(i)?i:fe(i),h;return this.isValid()&&c.isValid()?(a=We(a)||"millisecond",a==="millisecond"?this.valueOf()===c.valueOf():(h=c.valueOf(),this.clone().startOf(a).valueOf()<=h&&h<=this.clone().endOf(a).valueOf())):!1}function j0(i,a){return this.isSame(i,a)||this.isAfter(i,a)}function K0(i,a){return this.isSame(i,a)||this.isBefore(i,a)}function X0(i,a,c){var h,w,k;if(!this.isValid())return NaN;if(h=da(i,this),!h.isValid())return NaN;switch(w=(h.utcOffset()-this.utcOffset())*6e4,a=We(a),a){case"year":k=Di(this,h)/12;break;case"month":k=Di(this,h);break;case"quarter":k=Di(this,h)/3;break;case"second":k=(this-h)/1e3;break;case"minute":k=(this-h)/6e4;break;case"hour":k=(this-h)/36e5;break;case"day":k=(this-h-w)/864e5;break;case"week":k=(this-h-w)/6048e5;break;default:k=this-h}return c?k:ut(k)}function Di(i,a){if(i.date()<a.date())return-Di(a,i);var c=(a.year()-i.year())*12+(a.month()-i.month()),h=i.clone().add(c,"months"),w,k;return a-h<0?(w=i.clone().add(c-1,"months"),k=(a-h)/(h-w)):(w=i.clone().add(c+1,"months"),k=(a-h)/(w-h)),-(c+k)||0}e.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",e.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function Z0(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function J0(i){if(!this.isValid())return null;var a=i!==!0,c=a?this.clone().utc():this;return c.year()<0||c.year()>9999?sn(c,a?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):de(Date.prototype.toISOString)?a?this.toDate().toISOString():new Date(this.valueOf()+this.utcOffset()*60*1e3).toISOString().replace("Z",sn(c,"Z")):sn(c,a?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function Q0(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var i="moment",a="",c,h,w,k;return this.isLocal()||(i=this.utcOffset()===0?"moment.utc":"moment.parseZone",a="Z"),c="["+i+'("]',h=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",w="-MM-DD[T]HH:mm:ss.SSS",k=a+'[")]',this.format(c+h+w+k)}function ew(i){i||(i=this.isUtc()?e.defaultFormatUtc:e.defaultFormat);var a=sn(this,i);return this.localeData().postformat(a)}function tw(i,a){return this.isValid()&&(C(i)&&i.isValid()||fe(i).isValid())?Ct({to:this,from:i}).locale(this.locale()).humanize(!a):this.localeData().invalidDate()}function nw(i){return this.from(fe(),i)}function rw(i,a){return this.isValid()&&(C(i)&&i.isValid()||fe(i).isValid())?Ct({from:this,to:i}).locale(this.locale()).humanize(!a):this.localeData().invalidDate()}function iw(i){return this.to(fe(),i)}function rc(i){var a;return i===void 0?this._locale._abbr:(a=Wt(i),a!=null&&(this._locale=a),this)}var ic=X("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(i){return i===void 0?this.localeData():this.locale(i)});function sc(){return this._locale}var Oi=1e3,jn=60*Oi,Li=60*jn,oc=(365*400+97)*24*Li;function Kn(i,a){return(i%a+a)%a}function ac(i,a,c){return i<100&&i>=0?new Date(i+400,a,c)-oc:new Date(i,a,c).valueOf()}function lc(i,a,c){return i<100&&i>=0?Date.UTC(i+400,a,c)-oc:Date.UTC(i,a,c)}function sw(i){var a,c;if(i=We(i),i===void 0||i==="millisecond"||!this.isValid())return this;switch(c=this._isUTC?lc:ac,i){case"year":a=c(this.year(),0,1);break;case"quarter":a=c(this.year(),this.month()-this.month()%3,1);break;case"month":a=c(this.year(),this.month(),1);break;case"week":a=c(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":a=c(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":a=c(this.year(),this.month(),this.date());break;case"hour":a=this._d.valueOf(),a-=Kn(a+(this._isUTC?0:this.utcOffset()*jn),Li);break;case"minute":a=this._d.valueOf(),a-=Kn(a,jn);break;case"second":a=this._d.valueOf(),a-=Kn(a,Oi);break}return this._d.setTime(a),e.updateOffset(this,!0),this}function ow(i){var a,c;if(i=We(i),i===void 0||i==="millisecond"||!this.isValid())return this;switch(c=this._isUTC?lc:ac,i){case"year":a=c(this.year()+1,0,1)-1;break;case"quarter":a=c(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":a=c(this.year(),this.month()+1,1)-1;break;case"week":a=c(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":a=c(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":a=c(this.year(),this.month(),this.date()+1)-1;break;case"hour":a=this._d.valueOf(),a+=Li-Kn(a+(this._isUTC?0:this.utcOffset()*jn),Li)-1;break;case"minute":a=this._d.valueOf(),a+=jn-Kn(a,jn)-1;break;case"second":a=this._d.valueOf(),a+=Oi-Kn(a,Oi)-1;break}return this._d.setTime(a),e.updateOffset(this,!0),this}function aw(){return this._d.valueOf()-(this._offset||0)*6e4}function lw(){return Math.floor(this.valueOf()/1e3)}function uw(){return new Date(this.valueOf())}function cw(){var i=this;return[i.year(),i.month(),i.date(),i.hour(),i.minute(),i.second(),i.millisecond()]}function dw(){var i=this;return{years:i.year(),months:i.month(),date:i.date(),hours:i.hours(),minutes:i.minutes(),seconds:i.seconds(),milliseconds:i.milliseconds()}}function fw(){return this.isValid()?this.toISOString():null}function hw(){return S(this)}function mw(){return p({},A(this))}function pw(){return A(this).overflow}function gw(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}b("N",0,0,"eraAbbr"),b("NN",0,0,"eraAbbr"),b("NNN",0,0,"eraAbbr"),b("NNNN",0,0,"eraName"),b("NNNNN",0,0,"eraNarrow"),b("y",["y",1],"yo","eraYear"),b("y",["yy",2],0,"eraYear"),b("y",["yyy",3],0,"eraYear"),b("y",["yyyy",4],0,"eraYear"),N("N",ha),N("NN",ha),N("NNN",ha),N("NNNN",Tw),N("NNNNN",_w),le(["N","NN","NNN","NNNN","NNNNN"],function(i,a,c,h){var w=c._locale.erasParse(i,h,c._strict);w?A(c).era=w:A(c).invalidEra=i}),N("y",lt),N("yy",lt),N("yyy",lt),N("yyyy",lt),N("yo",Fw),le(["y","yy","yyy","yyyy"],Me),le(["yo"],function(i,a,c,h){var w;c._locale._eraYearOrdinalRegex&&(w=i.match(c._locale._eraYearOrdinalRegex)),c._locale.eraYearOrdinalParse?a[Me]=c._locale.eraYearOrdinalParse(i,w):a[Me]=parseInt(i,10)});function ww(i,a){var c,h,w,k=this._eras||Wt("en")._eras;for(c=0,h=k.length;c<h;++c){switch(typeof k[c].since){case"string":w=e(k[c].since).startOf("day"),k[c].since=w.valueOf();break}switch(typeof k[c].until){case"undefined":k[c].until=1/0;break;case"string":w=e(k[c].until).startOf("day").valueOf(),k[c].until=w.valueOf();break}}return k}function yw(i,a,c){var h,w,k=this.eras(),v,O,H;for(i=i.toUpperCase(),h=0,w=k.length;h<w;++h)if(v=k[h].name.toUpperCase(),O=k[h].abbr.toUpperCase(),H=k[h].narrow.toUpperCase(),c)switch(a){case"N":case"NN":case"NNN":if(O===i)return k[h];break;case"NNNN":if(v===i)return k[h];break;case"NNNNN":if(H===i)return k[h];break}else if([v,O,H].indexOf(i)>=0)return k[h]}function bw(i,a){var c=i.since<=i.until?1:-1;return a===void 0?e(i.since).year():e(i.since).year()+(a-i.offset)*c}function kw(){var i,a,c,h=this.localeData().eras();for(i=0,a=h.length;i<a;++i)if(c=this.clone().startOf("day").valueOf(),h[i].since<=c&&c<=h[i].until||h[i].until<=c&&c<=h[i].since)return h[i].name;return""}function xw(){var i,a,c,h=this.localeData().eras();for(i=0,a=h.length;i<a;++i)if(c=this.clone().startOf("day").valueOf(),h[i].since<=c&&c<=h[i].until||h[i].until<=c&&c<=h[i].since)return h[i].narrow;return""}function Aw(){var i,a,c,h=this.localeData().eras();for(i=0,a=h.length;i<a;++i)if(c=this.clone().startOf("day").valueOf(),h[i].since<=c&&c<=h[i].until||h[i].until<=c&&c<=h[i].since)return h[i].abbr;return""}function Ew(){var i,a,c,h,w=this.localeData().eras();for(i=0,a=w.length;i<a;++i)if(c=w[i].since<=w[i].until?1:-1,h=this.clone().startOf("day").valueOf(),w[i].since<=h&&h<=w[i].until||w[i].until<=h&&h<=w[i].since)return(this.year()-e(w[i].since).year())*c+w[i].offset;return this.year()}function vw(i){return o(this,"_erasNameRegex")||ma.call(this),i?this._erasNameRegex:this._erasRegex}function Cw(i){return o(this,"_erasAbbrRegex")||ma.call(this),i?this._erasAbbrRegex:this._erasRegex}function Sw(i){return o(this,"_erasNarrowRegex")||ma.call(this),i?this._erasNarrowRegex:this._erasRegex}function ha(i,a){return a.erasAbbrRegex(i)}function Tw(i,a){return a.erasNameRegex(i)}function _w(i,a){return a.erasNarrowRegex(i)}function Fw(i,a){return a._eraYearOrdinalRegex||lt}function ma(){var i=[],a=[],c=[],h=[],w,k,v,O,H,ee=this.eras();for(w=0,k=ee.length;w<k;++w)v=$t(ee[w].name),O=$t(ee[w].abbr),H=$t(ee[w].narrow),a.push(v),i.push(O),c.push(H),h.push(v),h.push(O),h.push(H);this._erasRegex=new RegExp("^("+h.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+a.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+i.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+c.join("|")+")","i")}b(0,["gg",2],0,function(){return this.weekYear()%100}),b(0,["GG",2],0,function(){return this.isoWeekYear()%100});function Mi(i,a){b(0,[i,i.length],0,a)}Mi("gggg","weekYear"),Mi("ggggg","weekYear"),Mi("GGGG","isoWeekYear"),Mi("GGGGG","isoWeekYear"),N("G",_t),N("g",_t),N("GG",D,Ue),N("gg",D,Ue),N("GGGG",xt,T),N("gggg",xt,T),N("GGGGG",Je,R),N("ggggg",Je,R),Rr(["gggg","ggggg","GGGG","GGGGG"],function(i,a,c,h){a[h.substr(0,2)]=Q(i)}),Rr(["gg","GG"],function(i,a,c,h){a[h]=e.parseTwoDigitYear(i)});function Pw(i){return uc.call(this,i,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function Iw(i){return uc.call(this,i,this.isoWeek(),this.isoWeekday(),1,4)}function Rw(){return zt(this.year(),1,4)}function Dw(){return zt(this.isoWeekYear(),1,4)}function Ow(){var i=this.localeData()._week;return zt(this.year(),i.dow,i.doy)}function Lw(){var i=this.localeData()._week;return zt(this.weekYear(),i.dow,i.doy)}function uc(i,a,c,h,w){var k;return i==null?Mr(this,h,w).year:(k=zt(i,h,w),a>k&&(a=k),Mw.call(this,i,a,c,h,w))}function Mw(i,a,c,h,w){var k=Yu(i,a,c,h,w),v=Lr(k.year,0,k.dayOfYear);return this.year(v.getUTCFullYear()),this.month(v.getUTCMonth()),this.date(v.getUTCDate()),this}b("Q",0,"Qo","quarter"),N("Q",vi),le("Q",function(i,a){a[Yt]=(Q(i)-1)*3});function Nw(i){return i==null?Math.ceil((this.month()+1)/3):this.month((i-1)*3+this.month()%3)}b("D",["DD",2],"Do","date"),N("D",D,Hn),N("DD",D,Ue),N("Do",function(i,a){return i?a._dayOfMonthOrdinalParse||a._ordinalParse:a._dayOfMonthOrdinalParseLenient}),le(["D","DD"],Ft),le("Do",function(i,a){a[Ft]=Q(i.match(D)[0])});var cc=qn("Date",!0);b("DDD",["DDDD",3],"DDDo","dayOfYear"),N("DDD",Le),N("DDDD",Ci),le(["DDD","DDDD"],function(i,a,c){c._dayOfYear=Q(i)});function Bw(i){var a=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return i==null?a:this.add(i-a,"d")}b("m",["mm",2],0,"minute"),N("m",D,Jo),N("mm",D,Ue),le(["m","mm"],vt);var $w=qn("Minutes",!1);b("s",["ss",2],0,"second"),N("s",D,Jo),N("ss",D,Ue),le(["s","ss"],Vt);var Yw=qn("Seconds",!1);b("S",0,0,function(){return~~(this.millisecond()/100)}),b(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),b(0,["SSS",3],0,"millisecond"),b(0,["SSSS",4],0,function(){return this.millisecond()*10}),b(0,["SSSSS",5],0,function(){return this.millisecond()*100}),b(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3}),b(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4}),b(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5}),b(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6}),N("S",Le,vi),N("SS",Le,Ue),N("SSS",Le,Ci);var ln,dc;for(ln="SSSS";ln.length<=9;ln+="S")N(ln,lt);function Vw(i,a){a[vn]=Q(("0."+i)*1e3)}for(ln="S";ln.length<=9;ln+="S")le(ln,Vw);dc=qn("Milliseconds",!1),b("z",0,0,"zoneAbbr"),b("zz",0,0,"zoneName");function zw(){return this._isUTC?"UTC":""}function Ww(){return this._isUTC?"Coordinated Universal Time":""}var I=W.prototype;I.add=L0,I.calendar=z0,I.clone=W0,I.diff=X0,I.endOf=ow,I.format=ew,I.from=tw,I.fromNow=nw,I.to=rw,I.toNow=iw,I.get=Qp,I.invalidAt=pw,I.isAfter=U0,I.isBefore=H0,I.isBetween=q0,I.isSame=G0,I.isSameOrAfter=j0,I.isSameOrBefore=K0,I.isValid=hw,I.lang=ic,I.locale=rc,I.localeData=sc,I.max=h0,I.min=f0,I.parsingFlags=mw,I.set=eg,I.startOf=sw,I.subtract=M0,I.toArray=cw,I.toObject=dw,I.toDate=uw,I.toISOString=J0,I.inspect=Q0,typeof Symbol<"u"&&Symbol.for!=null&&(I[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),I.toJSON=fw,I.toString=Z0,I.unix=lw,I.valueOf=aw,I.creationData=gw,I.eraName=kw,I.eraNarrow=xw,I.eraAbbr=Aw,I.eraYear=Ew,I.year=Du,I.isLeapYear=Jp,I.weekYear=Pw,I.isoWeekYear=Iw,I.quarter=I.quarters=Nw,I.month=Bu,I.daysInMonth=ug,I.week=I.weeks=wg,I.isoWeek=I.isoWeeks=yg,I.weeksInYear=Ow,I.weeksInWeekYear=Lw,I.isoWeeksInYear=Rw,I.isoWeeksInISOWeekYear=Dw,I.date=cc,I.day=I.days=Ig,I.weekday=Rg,I.isoWeekday=Dg,I.dayOfYear=Bw,I.hour=I.hours=Yg,I.minute=I.minutes=$w,I.second=I.seconds=Yw,I.millisecond=I.milliseconds=dc,I.utcOffset=A0,I.utc=v0,I.local=C0,I.parseZone=S0,I.hasAlignedHourOffset=T0,I.isDST=_0,I.isLocal=P0,I.isUtcOffset=I0,I.isUtc=Ju,I.isUTC=Ju,I.zoneAbbr=zw,I.zoneName=Ww,I.dates=X("dates accessor is deprecated. Use date instead.",cc),I.months=X("months accessor is deprecated. Use month instead",Bu),I.years=X("years accessor is deprecated. Use year instead",Du),I.zone=X("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",E0),I.isDSTShifted=X("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",F0);function Uw(i){return fe(i*1e3)}function Hw(){return fe.apply(null,arguments).parseZone()}function fc(i){return i}var se=q.prototype;se.calendar=ge,se.longDateFormat=En,se.invalidDate=Bt,se.ordinal=Ko,se.preparse=fc,se.postformat=fc,se.relativeTime=xi,se.pastFuture=Ai,se.set=re,se.eras=ww,se.erasParse=yw,se.erasConvertYear=bw,se.erasAbbrRegex=Cw,se.erasNameRegex=vw,se.erasNarrowRegex=Sw,se.months=sg,se.monthsShort=og,se.monthsParse=lg,se.monthsRegex=dg,se.monthsShortRegex=cg,se.week=hg,se.firstDayOfYear=gg,se.firstDayOfWeek=pg,se.weekdays=Sg,se.weekdaysMin=_g,se.weekdaysShort=Tg,se.weekdaysParse=Pg,se.weekdaysRegex=Og,se.weekdaysShortRegex=Lg,se.weekdaysMinRegex=Mg,se.isPM=Bg,se.meridiem=Vg;function Ni(i,a,c,h){var w=Wt(),k=y().set(h,a);return w[c](k,i)}function hc(i,a,c){if(d(i)&&(a=i,i=void 0),i=i||"",a!=null)return Ni(i,a,c,"month");var h,w=[];for(h=0;h<12;h++)w[h]=Ni(i,h,c,"month");return w}function pa(i,a,c,h){typeof i=="boolean"?(d(a)&&(c=a,a=void 0),a=a||""):(a=i,c=a,i=!1,d(a)&&(c=a,a=void 0),a=a||"");var w=Wt(),k=i?w._week.dow:0,v,O=[];if(c!=null)return Ni(a,(c+k)%7,h,"day");for(v=0;v<7;v++)O[v]=Ni(a,(v+k)%7,h,"day");return O}function qw(i,a){return hc(i,a,"months")}function Gw(i,a){return hc(i,a,"monthsShort")}function jw(i,a,c){return pa(i,a,c,"weekdays")}function Kw(i,a,c){return pa(i,a,c,"weekdaysShort")}function Xw(i,a,c){return pa(i,a,c,"weekdaysMin")}an("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(i){var a=i%10,c=Q(i%100/10)===1?"th":a===1?"st":a===2?"nd":a===3?"rd":"th";return i+c}}),e.lang=X("moment.lang is deprecated. Use moment.locale instead.",an),e.langData=X("moment.langData is deprecated. Use moment.localeData instead.",Wt);var Ut=Math.abs;function Zw(){var i=this._data;return this._milliseconds=Ut(this._milliseconds),this._days=Ut(this._days),this._months=Ut(this._months),i.milliseconds=Ut(i.milliseconds),i.seconds=Ut(i.seconds),i.minutes=Ut(i.minutes),i.hours=Ut(i.hours),i.months=Ut(i.months),i.years=Ut(i.years),this}function mc(i,a,c,h){var w=Ct(a,c);return i._milliseconds+=h*w._milliseconds,i._days+=h*w._days,i._months+=h*w._months,i._bubble()}function Jw(i,a){return mc(this,i,a,1)}function Qw(i,a){return mc(this,i,a,-1)}function pc(i){return i<0?Math.floor(i):Math.ceil(i)}function ey(){var i=this._milliseconds,a=this._days,c=this._months,h=this._data,w,k,v,O,H;return i>=0&&a>=0&&c>=0||i<=0&&a<=0&&c<=0||(i+=pc(ga(c)+a)*864e5,a=0,c=0),h.milliseconds=i%1e3,w=ut(i/1e3),h.seconds=w%60,k=ut(w/60),h.minutes=k%60,v=ut(k/60),h.hours=v%24,a+=ut(v/24),H=ut(gc(a)),c+=H,a-=pc(ga(H)),O=ut(c/12),c%=12,h.days=a,h.months=c,h.years=O,this}function gc(i){return i*4800/146097}function ga(i){return i*146097/4800}function ty(i){if(!this.isValid())return NaN;var a,c,h=this._milliseconds;if(i=We(i),i==="month"||i==="quarter"||i==="year")switch(a=this._days+h/864e5,c=this._months+gc(a),i){case"month":return c;case"quarter":return c/3;case"year":return c/12}else switch(a=this._days+Math.round(ga(this._months)),i){case"week":return a/7+h/6048e5;case"day":return a+h/864e5;case"hour":return a*24+h/36e5;case"minute":return a*1440+h/6e4;case"second":return a*86400+h/1e3;case"millisecond":return Math.floor(a*864e5)+h;default:throw new Error("Unknown unit "+i)}}function Ht(i){return function(){return this.as(i)}}var wc=Ht("ms"),ny=Ht("s"),ry=Ht("m"),iy=Ht("h"),sy=Ht("d"),oy=Ht("w"),ay=Ht("M"),ly=Ht("Q"),uy=Ht("y"),cy=wc;function dy(){return Ct(this)}function fy(i){return i=We(i),this.isValid()?this[i+"s"]():NaN}function Sn(i){return function(){return this.isValid()?this._data[i]:NaN}}var hy=Sn("milliseconds"),my=Sn("seconds"),py=Sn("minutes"),gy=Sn("hours"),wy=Sn("days"),yy=Sn("months"),by=Sn("years");function ky(){return ut(this.days()/7)}var qt=Math.round,Xn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function xy(i,a,c,h,w){return w.relativeTime(a||1,!!c,i,h)}function Ay(i,a,c,h){var w=Ct(i).abs(),k=qt(w.as("s")),v=qt(w.as("m")),O=qt(w.as("h")),H=qt(w.as("d")),ee=qt(w.as("M")),qe=qt(w.as("w")),Gt=qt(w.as("y")),un=k<=c.ss&&["s",k]||k<c.s&&["ss",k]||v<=1&&["m"]||v<c.m&&["mm",v]||O<=1&&["h"]||O<c.h&&["hh",O]||H<=1&&["d"]||H<c.d&&["dd",H];return c.w!=null&&(un=un||qe<=1&&["w"]||qe<c.w&&["ww",qe]),un=un||ee<=1&&["M"]||ee<c.M&&["MM",ee]||Gt<=1&&["y"]||["yy",Gt],un[2]=a,un[3]=+i>0,un[4]=h,xy.apply(null,un)}function Ey(i){return i===void 0?qt:typeof i=="function"?(qt=i,!0):!1}function vy(i,a){return Xn[i]===void 0?!1:a===void 0?Xn[i]:(Xn[i]=a,i==="s"&&(Xn.ss=a-1),!0)}function Cy(i,a){if(!this.isValid())return this.localeData().invalidDate();var c=!1,h=Xn,w,k;return typeof i=="object"&&(a=i,i=!1),typeof i=="boolean"&&(c=i),typeof a=="object"&&(h=Object.assign({},Xn,a),a.s!=null&&a.ss==null&&(h.ss=a.s-1)),w=this.localeData(),k=Ay(this,!c,h,w),c&&(k=w.pastFuture(+this,k)),w.postformat(k)}var wa=Math.abs;function Zn(i){return(i>0)-(i<0)||+i}function Bi(){if(!this.isValid())return this.localeData().invalidDate();var i=wa(this._milliseconds)/1e3,a=wa(this._days),c=wa(this._months),h,w,k,v,O=this.asSeconds(),H,ee,qe,Gt;return O?(h=ut(i/60),w=ut(h/60),i%=60,h%=60,k=ut(c/12),c%=12,v=i?i.toFixed(3).replace(/\.?0+$/,""):"",H=O<0?"-":"",ee=Zn(this._months)!==Zn(O)?"-":"",qe=Zn(this._days)!==Zn(O)?"-":"",Gt=Zn(this._milliseconds)!==Zn(O)?"-":"",H+"P"+(k?ee+k+"Y":"")+(c?ee+c+"M":"")+(a?qe+a+"D":"")+(w||h||i?"T":"")+(w?Gt+w+"H":"")+(h?Gt+h+"M":"")+(i?Gt+v+"S":"")):"P0D"}var ne=Ii.prototype;ne.isValid=y0,ne.abs=Zw,ne.add=Jw,ne.subtract=Qw,ne.as=ty,ne.asMilliseconds=wc,ne.asSeconds=ny,ne.asMinutes=ry,ne.asHours=iy,ne.asDays=sy,ne.asWeeks=oy,ne.asMonths=ay,ne.asQuarters=ly,ne.asYears=uy,ne.valueOf=cy,ne._bubble=ey,ne.clone=dy,ne.get=fy,ne.milliseconds=hy,ne.seconds=my,ne.minutes=py,ne.hours=gy,ne.days=wy,ne.weeks=ky,ne.months=yy,ne.years=by,ne.humanize=Cy,ne.toISOString=Bi,ne.toString=Bi,ne.toJSON=Bi,ne.locale=rc,ne.localeData=sc,ne.toIsoString=X("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Bi),ne.lang=ic,b("X",0,0,"unix"),b("x",0,0,"valueOf"),N("x",_t),N("X",Et),le("X",function(i,a,c){c._d=new Date(parseFloat(i)*1e3)}),le("x",function(i,a,c){c._d=new Date(Q(i))});return e.version="2.30.1",n(fe),e.fn=I,e.min=m0,e.max=p0,e.now=g0,e.utc=y,e.unix=Uw,e.months=qw,e.isDate=f,e.locale=an,e.invalid=E,e.duration=Ct,e.isMoment=C,e.weekdays=jw,e.parseZone=Hw,e.localeData=Wt,e.isDuration=Ri,e.monthsShort=Gw,e.weekdaysMin=Xw,e.defineLocale=ia,e.updateLocale=Hg,e.locales=qg,e.weekdaysShort=Kw,e.normalizeUnits=We,e.relativeTimeRounding=Ey,e.relativeTimeThreshold=vy,e.calendarFormat=V0,e.prototype=I,e.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},e})});var xc=V((JC,kc)=>{var Qn=1e3,er=Qn*60,tr=er*60,Tn=tr*24,Ry=Tn*7,Dy=Tn*365.25;kc.exports=function(t,e){e=e||{};var n=typeof t;if(n==="string"&&t.length>0)return Oy(t);if(n==="number"&&isFinite(t))return e.long?My(t):Ly(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function Oy(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var n=parseFloat(e[1]),r=(e[2]||"ms").toLowerCase();switch(r){case"years":case"year":case"yrs":case"yr":case"y":return n*Dy;case"weeks":case"week":case"w":return n*Ry;case"days":case"day":case"d":return n*Tn;case"hours":case"hour":case"hrs":case"hr":case"h":return n*tr;case"minutes":case"minute":case"mins":case"min":case"m":return n*er;case"seconds":case"second":case"secs":case"sec":case"s":return n*Qn;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}function Ly(t){var e=Math.abs(t);return e>=Tn?Math.round(t/Tn)+"d":e>=tr?Math.round(t/tr)+"h":e>=er?Math.round(t/er)+"m":e>=Qn?Math.round(t/Qn)+"s":t+"ms"}function My(t){var e=Math.abs(t);return e>=Tn?Yi(t,e,Tn,"day"):e>=tr?Yi(t,e,tr,"hour"):e>=er?Yi(t,e,er,"minute"):e>=Qn?Yi(t,e,Qn,"second"):t+" ms"}function Yi(t,e,n,r){var s=e>=n*1.5;return Math.round(t/n)+" "+r+(s?"s":"")}});var ba=V((QC,Ac)=>{function Ny(t){n.debug=n,n.default=n,n.coerce=d,n.disable=l,n.enable=s,n.enabled=u,n.humanize=xc(),n.destroy=f,Object.keys(t).forEach(m=>{n[m]=t[m]}),n.names=[],n.skips=[],n.formatters={};function e(m){let p=0;for(let y=0;y<m.length;y++)p=(p<<5)-p+m.charCodeAt(y),p|=0;return n.colors[Math.abs(p)%n.colors.length]}n.selectColor=e;function n(m){let p,y=null,g,A;function P(...S){if(!P.enabled)return;let E=P,_=Number(new Date),F=_-(p||_);E.diff=F,E.prev=p,E.curr=_,p=_,S[0]=n.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let z=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(C,te)=>{if(C==="%%")return"%";z++;let X=n.formatters[te];if(typeof X=="function"){let Z=S[z];C=X.call(E,Z),S.splice(z,1),z--}return C}),n.formatArgs.call(E,S),(E.log||n.log).apply(E,S)}return P.namespace=m,P.useColors=n.useColors(),P.color=n.selectColor(m),P.extend=r,P.destroy=n.destroy,Object.defineProperty(P,"enabled",{enumerable:!0,configurable:!1,get:()=>y!==null?y:(g!==n.namespaces&&(g=n.namespaces,A=n.enabled(m)),A),set:S=>{y=S}}),typeof n.init=="function"&&n.init(P),P}function r(m,p){let y=n(this.namespace+(typeof p>"u"?":":p)+m);return y.log=this.log,y}function s(m){n.save(m),n.namespaces=m,n.names=[],n.skips=[];let p=(typeof m=="string"?m:"").trim().replace(/\s+/g,",").split(",").filter(Boolean);for(let y of p)y[0]==="-"?n.skips.push(y.slice(1)):n.names.push(y)}function o(m,p){let y=0,g=0,A=-1,P=0;for(;y<m.length;)if(g<p.length&&(p[g]===m[y]||p[g]==="*"))p[g]==="*"?(A=g,P=y,g++):(y++,g++);else if(A!==-1)g=A+1,P++,y=P;else return!1;for(;g<p.length&&p[g]==="*";)g++;return g===p.length}function l(){let m=[...n.names,...n.skips.map(p=>"-"+p)].join(",");return n.enable(""),m}function u(m){for(let p of n.skips)if(o(m,p))return!1;for(let p of n.names)if(o(m,p))return!0;return!1}function d(m){return m instanceof Error?m.stack||m.message:m}function f(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}Ac.exports=Ny});var Ec=V((Qe,Vi)=>{Qe.formatArgs=$y;Qe.save=Yy;Qe.load=Vy;Qe.useColors=By;Qe.storage=zy();Qe.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();Qe.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function By(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function $y(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+Vi.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let n=0,r=0;t[0].replace(/%[a-zA-Z%]/g,s=>{s!=="%%"&&(n++,s==="%c"&&(r=n))}),t.splice(r,0,e)}Qe.log=console.debug||console.log||(()=>{});function Yy(t){try{t?Qe.storage.setItem("debug",t):Qe.storage.removeItem("debug")}catch{}}function Vy(){let t;try{t=Qe.storage.getItem("debug")||Qe.storage.getItem("DEBUG")}catch{}return!t&&typeof process<"u"&&"env"in process&&(t=process.env.DEBUG),t}function zy(){try{return localStorage}catch{}}Vi.exports=ba()(Qe);var{formatters:Wy}=Vi.exports;Wy.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var Cc=V((e2,vc)=>{"use strict";vc.exports=(t,e=process.argv)=>{let n=t.startsWith("-")?"":t.length===1?"-":"--",r=e.indexOf(n+t),s=e.indexOf("--");return r!==-1&&(s===-1||r<s)}});var _c=V((t2,Tc)=>{"use strict";var Uy=require("os"),Sc=require("tty"),ct=Cc(),{env:Te}=process,cn;ct("no-color")||ct("no-colors")||ct("color=false")||ct("color=never")?cn=0:(ct("color")||ct("colors")||ct("color=true")||ct("color=always"))&&(cn=1);"FORCE_COLOR"in Te&&(Te.FORCE_COLOR==="true"?cn=1:Te.FORCE_COLOR==="false"?cn=0:cn=Te.FORCE_COLOR.length===0?1:Math.min(parseInt(Te.FORCE_COLOR,10),3));function ka(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function xa(t,e){if(cn===0)return 0;if(ct("color=16m")||ct("color=full")||ct("color=truecolor"))return 3;if(ct("color=256"))return 2;if(t&&!e&&cn===void 0)return 0;let n=cn||0;if(Te.TERM==="dumb")return n;if(process.platform==="win32"){let r=Uy.release().split(".");return Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in Te)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(r=>r in Te)||Te.CI_NAME==="codeship"?1:n;if("TEAMCITY_VERSION"in Te)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Te.TEAMCITY_VERSION)?1:0;if(Te.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in Te){let r=parseInt((Te.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Te.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Te.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(Te.TERM)||"COLORTERM"in Te?1:n}function Hy(t){let e=xa(t,t&&t.isTTY);return ka(e)}Tc.exports={supportsColor:Hy,stdout:ka(xa(!0,Sc.isatty(1))),stderr:ka(xa(!0,Sc.isatty(2)))}});var Pc=V((_e,Wi)=>{var qy=require("tty"),zi=require("util");_e.init=Qy;_e.log=Xy;_e.formatArgs=jy;_e.save=Zy;_e.load=Jy;_e.useColors=Gy;_e.destroy=zi.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");_e.colors=[6,2,3,4,5,1];try{let t=_c();t&&(t.stderr||t).level>=2&&(_e.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}_e.inspectOpts=Object.keys(process.env).filter(t=>/^debug_/i.test(t)).reduce((t,e)=>{let n=e.substring(6).toLowerCase().replace(/_([a-z])/g,(s,o)=>o.toUpperCase()),r=process.env[e];return/^(yes|on|true|enabled)$/i.test(r)?r=!0:/^(no|off|false|disabled)$/i.test(r)?r=!1:r==="null"?r=null:r=Number(r),t[n]=r,t},{});function Gy(){return"colors"in _e.inspectOpts?!!_e.inspectOpts.colors:qy.isatty(process.stderr.fd)}function jy(t){let{namespace:e,useColors:n}=this;if(n){let r=this.color,s="\x1B[3"+(r<8?r:"8;5;"+r),o=`  ${s};1m${e} \x1B[0m`;t[0]=o+t[0].split(`
`).join(`
`+o),t.push(s+"m+"+Wi.exports.humanize(this.diff)+"\x1B[0m")}else t[0]=Ky()+e+" "+t[0]}function Ky(){return _e.inspectOpts.hideDate?"":new Date().toISOString()+" "}function Xy(...t){return process.stderr.write(zi.formatWithOptions(_e.inspectOpts,...t)+`
`)}function Zy(t){t?process.env.DEBUG=t:delete process.env.DEBUG}function Jy(){return process.env.DEBUG}function Qy(t){t.inspectOpts={};let e=Object.keys(_e.inspectOpts);for(let n=0;n<e.length;n++)t.inspectOpts[e[n]]=_e.inspectOpts[e[n]]}Wi.exports=ba()(_e);var{formatters:Fc}=Wi.exports;Fc.o=function(t){return this.inspectOpts.colors=this.useColors,zi.inspect(t,this.inspectOpts).split(`
`).map(e=>e.trim()).join(" ")};Fc.O=function(t){return this.inspectOpts.colors=this.useColors,zi.inspect(t,this.inspectOpts)}});var Ic=V((n2,Aa)=>{typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?Aa.exports=Ec():Aa.exports=Pc()});var Jc=V((C2,Zc)=>{"use strict";function Rt(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function Xc(t,e){for(var n="",r=0,s=-1,o=0,l,u=0;u<=t.length;++u){if(u<t.length)l=t.charCodeAt(u);else{if(l===47)break;l=47}if(l===47){if(!(s===u-1||o===1))if(s!==u-1&&o===2){if(n.length<2||r!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){var d=n.lastIndexOf("/");if(d!==n.length-1){d===-1?(n="",r=0):(n=n.slice(0,d),r=n.length-1-n.lastIndexOf("/")),s=u,o=0;continue}}else if(n.length===2||n.length===1){n="",r=0,s=u,o=0;continue}}e&&(n.length>0?n+="/..":n="..",r=2)}else n.length>0?n+="/"+t.slice(s+1,u):n=t.slice(s+1,u),r=u-s-1;s=u,o=0}else l===46&&o!==-1?++o:o=-1}return n}function db(t,e){var n=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+r:n+t+r:r}var rr={resolve:function(){for(var e="",n=!1,r,s=arguments.length-1;s>=-1&&!n;s--){var o;s>=0?o=arguments[s]:(r===void 0&&(r=process.cwd()),o=r),Rt(o),o.length!==0&&(e=o+"/"+e,n=o.charCodeAt(0)===47)}return e=Xc(e,!n),n?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(e){if(Rt(e),e.length===0)return".";var n=e.charCodeAt(0)===47,r=e.charCodeAt(e.length-1)===47;return e=Xc(e,!n),e.length===0&&!n&&(e="."),e.length>0&&r&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return Rt(e),e.length>0&&e.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var e,n=0;n<arguments.length;++n){var r=arguments[n];Rt(r),r.length>0&&(e===void 0?e=r:e+="/"+r)}return e===void 0?".":rr.normalize(e)},relative:function(e,n){if(Rt(e),Rt(n),e===n||(e=rr.resolve(e),n=rr.resolve(n),e===n))return"";for(var r=1;r<e.length&&e.charCodeAt(r)===47;++r);for(var s=e.length,o=s-r,l=1;l<n.length&&n.charCodeAt(l)===47;++l);for(var u=n.length,d=u-l,f=o<d?o:d,m=-1,p=0;p<=f;++p){if(p===f){if(d>f){if(n.charCodeAt(l+p)===47)return n.slice(l+p+1);if(p===0)return n.slice(l+p)}else o>f&&(e.charCodeAt(r+p)===47?m=p:p===0&&(m=0));break}var y=e.charCodeAt(r+p),g=n.charCodeAt(l+p);if(y!==g)break;y===47&&(m=p)}var A="";for(p=r+m+1;p<=s;++p)(p===s||e.charCodeAt(p)===47)&&(A.length===0?A+="..":A+="/..");return A.length>0?A+n.slice(l+m):(l+=m,n.charCodeAt(l)===47&&++l,n.slice(l))},_makeLong:function(e){return e},dirname:function(e){if(Rt(e),e.length===0)return".";for(var n=e.charCodeAt(0),r=n===47,s=-1,o=!0,l=e.length-1;l>=1;--l)if(n=e.charCodeAt(l),n===47){if(!o){s=l;break}}else o=!1;return s===-1?r?"/":".":r&&s===1?"//":e.slice(0,s)},basename:function(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');Rt(e);var r=0,s=-1,o=!0,l;if(n!==void 0&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var u=n.length-1,d=-1;for(l=e.length-1;l>=0;--l){var f=e.charCodeAt(l);if(f===47){if(!o){r=l+1;break}}else d===-1&&(o=!1,d=l+1),u>=0&&(f===n.charCodeAt(u)?--u===-1&&(s=l):(u=-1,s=d))}return r===s?s=d:s===-1&&(s=e.length),e.slice(r,s)}else{for(l=e.length-1;l>=0;--l)if(e.charCodeAt(l)===47){if(!o){r=l+1;break}}else s===-1&&(o=!1,s=l+1);return s===-1?"":e.slice(r,s)}},extname:function(e){Rt(e);for(var n=-1,r=0,s=-1,o=!0,l=0,u=e.length-1;u>=0;--u){var d=e.charCodeAt(u);if(d===47){if(!o){r=u+1;break}continue}s===-1&&(o=!1,s=u+1),d===46?n===-1?n=u:l!==1&&(l=1):n!==-1&&(l=-1)}return n===-1||s===-1||l===0||l===1&&n===s-1&&n===r+1?"":e.slice(n,s)},format:function(e){if(e===null||typeof e!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return db("/",e)},parse:function(e){Rt(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return n;var r=e.charCodeAt(0),s=r===47,o;s?(n.root="/",o=1):o=0;for(var l=-1,u=0,d=-1,f=!0,m=e.length-1,p=0;m>=o;--m){if(r=e.charCodeAt(m),r===47){if(!f){u=m+1;break}continue}d===-1&&(f=!1,d=m+1),r===46?l===-1?l=m:p!==1&&(p=1):l!==-1&&(p=-1)}return l===-1||d===-1||p===0||p===1&&l===d-1&&l===u+1?d!==-1&&(u===0&&s?n.base=n.name=e.slice(1,d):n.base=n.name=e.slice(u,d)):(u===0&&s?(n.name=e.slice(1,l),n.base=e.slice(1,d)):(n.name=e.slice(u,l),n.base=e.slice(u,d)),n.ext=e.slice(l,d)),u>0?n.dir=e.slice(0,u-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};rr.posix=rr;Zc.exports=rr});var _f=V((jP,Tf)=>{"use strict";var xs=Object.prototype.hasOwnProperty,Sf=Object.prototype.toString,kf=Object.defineProperty,xf=Object.getOwnPropertyDescriptor,Af=function(e){return typeof Array.isArray=="function"?Array.isArray(e):Sf.call(e)==="[object Array]"},Ef=function(e){if(!e||Sf.call(e)!=="[object Object]")return!1;var n=xs.call(e,"constructor"),r=e.constructor&&e.constructor.prototype&&xs.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!r)return!1;var s;for(s in e);return typeof s>"u"||xs.call(e,s)},vf=function(e,n){kf&&n.name==="__proto__"?kf(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},Cf=function(e,n){if(n==="__proto__")if(xs.call(e,n)){if(xf)return xf(e,n).value}else return;return e[n]};Tf.exports=function t(){var e,n,r,s,o,l,u=arguments[0],d=1,f=arguments.length,m=!1;for(typeof u=="boolean"&&(m=u,u=arguments[1]||{},d=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});d<f;++d)if(e=arguments[d],e!=null)for(n in e)r=Cf(u,n),s=Cf(e,n),u!==s&&(m&&s&&(Ef(s)||(o=Af(s)))?(o?(o=!1,l=r&&Af(r)?r:[]):l=r&&Ef(r)?r:{},vf(u,{name:n,newValue:t(m,l,s)})):typeof s<"u"&&vf(u,{name:n,newValue:s}));return u}});var sh=V((Ls,ih)=>{(function(t,e){typeof Ls=="object"&&typeof ih<"u"?e(Ls):typeof define=="function"&&define.amd?define(["exports"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.compareVersions={}))})(Ls,function(t){"use strict";let e=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,n=S=>{if(typeof S!="string")throw new TypeError("Invalid argument expected string");let E=S.match(e);if(!E)throw new Error(`Invalid argument not valid semver ('${S}' received)`);return E.shift(),E},r=S=>S==="*"||S==="x"||S==="X",s=S=>{let E=parseInt(S,10);return isNaN(E)?S:E},o=(S,E)=>typeof S!=typeof E?[String(S),String(E)]:[S,E],l=(S,E)=>{if(r(S)||r(E))return 0;let[_,F]=o(s(S),s(E));return _>F?1:_<F?-1:0},u=(S,E)=>{for(let _=0;_<Math.max(S.length,E.length);_++){let F=l(S[_]||"0",E[_]||"0");if(F!==0)return F}return 0},d=(S,E)=>{let _=n(S),F=n(E),z=_.pop(),W=F.pop(),C=u(_,F);return C!==0?C:z&&W?u(z.split("."),W.split(".")):z||W?z?-1:1:0},f=(S,E,_)=>{y(_);let F=d(S,E);return m[_].includes(F)},m={">":[1],">=":[0,1],"=":[0],"<=":[-1,0],"<":[-1],"!=":[-1,1]},p=Object.keys(m),y=S=>{if(typeof S!="string")throw new TypeError(`Invalid operator type, expected string but got ${typeof S}`);if(p.indexOf(S)===-1)throw new Error(`Invalid operator, expected one of ${p.join("|")}`)},g=(S,E)=>{if(E=E.replace(/([><=]+)\s+/g,"$1"),E.includes("||"))return E.split("||").some(ie=>g(S,ie));if(E.includes(" - ")){let[ie,ge]=E.split(" - ",2);return g(S,`>=${ie} <=${ge}`)}else if(E.includes(" "))return E.trim().replace(/\s{2,}/g," ").split(" ").every(ie=>g(S,ie));let _=E.match(/^([<>=~^]+)/),F=_?_[1]:"=";if(F!=="^"&&F!=="~")return f(S,E,F);let[z,W,C,,te]=n(S),[X,Z,oe,,de]=n(E),re=[z,W,C],Y=[X,Z??"x",oe??"x"];if(de&&(!te||u(re,Y)!==0||u(te.split("."),de.split("."))===-1))return!1;let q=Y.findIndex(ie=>ie!=="0")+1,U=F==="~"?2:q>1?q:1;return!(u(re.slice(0,U),Y.slice(0,U))!==0||u(re.slice(U),Y.slice(U))===-1)},A=S=>typeof S=="string"&&/^[v\d]/.test(S)&&e.test(S),P=S=>typeof S=="string"&&/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/.test(S);t.compare=f,t.compareVersions=d,t.satisfies=g,t.validate=A,t.validateStrict=P})});var li=V((xD,Ch)=>{"use strict";var PA="2.0.0",IA=Number.MAX_SAFE_INTEGER||9007199254740991,RA=16,DA=250,OA=["major","premajor","minor","preminor","patch","prepatch","prerelease"];Ch.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:RA,MAX_SAFE_BUILD_LENGTH:DA,MAX_SAFE_INTEGER:IA,RELEASE_TYPES:OA,SEMVER_SPEC_VERSION:PA,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var ui=V((AD,Sh)=>{"use strict";var LA=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...t)=>console.error("SEMVER",...t):()=>{};Sh.exports=LA});var vr=V((Mt,Th)=>{"use strict";var{MAX_SAFE_COMPONENT_LENGTH:jl,MAX_SAFE_BUILD_LENGTH:MA,MAX_LENGTH:NA}=li(),BA=ui();Mt=Th.exports={};var $A=Mt.re=[],YA=Mt.safeRe=[],L=Mt.src=[],VA=Mt.safeSrc=[],M=Mt.t={},zA=0,Kl="[a-zA-Z0-9-]",WA=[["\\s",1],["\\d",NA],[Kl,MA]],UA=t=>{for(let[e,n]of WA)t=t.split(`${e}*`).join(`${e}{0,${n}}`).split(`${e}+`).join(`${e}{1,${n}}`);return t},K=(t,e,n)=>{let r=UA(e),s=zA++;BA(t,s,e),M[t]=s,L[s]=e,VA[s]=r,$A[s]=new RegExp(e,n?"g":void 0),YA[s]=new RegExp(r,n?"g":void 0)};K("NUMERICIDENTIFIER","0|[1-9]\\d*");K("NUMERICIDENTIFIERLOOSE","\\d+");K("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${Kl}*`);K("MAINVERSION",`(${L[M.NUMERICIDENTIFIER]})\\.(${L[M.NUMERICIDENTIFIER]})\\.(${L[M.NUMERICIDENTIFIER]})`);K("MAINVERSIONLOOSE",`(${L[M.NUMERICIDENTIFIERLOOSE]})\\.(${L[M.NUMERICIDENTIFIERLOOSE]})\\.(${L[M.NUMERICIDENTIFIERLOOSE]})`);K("PRERELEASEIDENTIFIER",`(?:${L[M.NONNUMERICIDENTIFIER]}|${L[M.NUMERICIDENTIFIER]})`);K("PRERELEASEIDENTIFIERLOOSE",`(?:${L[M.NONNUMERICIDENTIFIER]}|${L[M.NUMERICIDENTIFIERLOOSE]})`);K("PRERELEASE",`(?:-(${L[M.PRERELEASEIDENTIFIER]}(?:\\.${L[M.PRERELEASEIDENTIFIER]})*))`);K("PRERELEASELOOSE",`(?:-?(${L[M.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${L[M.PRERELEASEIDENTIFIERLOOSE]})*))`);K("BUILDIDENTIFIER",`${Kl}+`);K("BUILD",`(?:\\+(${L[M.BUILDIDENTIFIER]}(?:\\.${L[M.BUILDIDENTIFIER]})*))`);K("FULLPLAIN",`v?${L[M.MAINVERSION]}${L[M.PRERELEASE]}?${L[M.BUILD]}?`);K("FULL",`^${L[M.FULLPLAIN]}$`);K("LOOSEPLAIN",`[v=\\s]*${L[M.MAINVERSIONLOOSE]}${L[M.PRERELEASELOOSE]}?${L[M.BUILD]}?`);K("LOOSE",`^${L[M.LOOSEPLAIN]}$`);K("GTLT","((?:<|>)?=?)");K("XRANGEIDENTIFIERLOOSE",`${L[M.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);K("XRANGEIDENTIFIER",`${L[M.NUMERICIDENTIFIER]}|x|X|\\*`);K("XRANGEPLAIN",`[v=\\s]*(${L[M.XRANGEIDENTIFIER]})(?:\\.(${L[M.XRANGEIDENTIFIER]})(?:\\.(${L[M.XRANGEIDENTIFIER]})(?:${L[M.PRERELEASE]})?${L[M.BUILD]}?)?)?`);K("XRANGEPLAINLOOSE",`[v=\\s]*(${L[M.XRANGEIDENTIFIERLOOSE]})(?:\\.(${L[M.XRANGEIDENTIFIERLOOSE]})(?:\\.(${L[M.XRANGEIDENTIFIERLOOSE]})(?:${L[M.PRERELEASELOOSE]})?${L[M.BUILD]}?)?)?`);K("XRANGE",`^${L[M.GTLT]}\\s*${L[M.XRANGEPLAIN]}$`);K("XRANGELOOSE",`^${L[M.GTLT]}\\s*${L[M.XRANGEPLAINLOOSE]}$`);K("COERCEPLAIN",`(^|[^\\d])(\\d{1,${jl}})(?:\\.(\\d{1,${jl}}))?(?:\\.(\\d{1,${jl}}))?`);K("COERCE",`${L[M.COERCEPLAIN]}(?:$|[^\\d])`);K("COERCEFULL",L[M.COERCEPLAIN]+`(?:${L[M.PRERELEASE]})?(?:${L[M.BUILD]})?(?:$|[^\\d])`);K("COERCERTL",L[M.COERCE],!0);K("COERCERTLFULL",L[M.COERCEFULL],!0);K("LONETILDE","(?:~>?)");K("TILDETRIM",`(\\s*)${L[M.LONETILDE]}\\s+`,!0);Mt.tildeTrimReplace="$1~";K("TILDE",`^${L[M.LONETILDE]}${L[M.XRANGEPLAIN]}$`);K("TILDELOOSE",`^${L[M.LONETILDE]}${L[M.XRANGEPLAINLOOSE]}$`);K("LONECARET","(?:\\^)");K("CARETTRIM",`(\\s*)${L[M.LONECARET]}\\s+`,!0);Mt.caretTrimReplace="$1^";K("CARET",`^${L[M.LONECARET]}${L[M.XRANGEPLAIN]}$`);K("CARETLOOSE",`^${L[M.LONECARET]}${L[M.XRANGEPLAINLOOSE]}$`);K("COMPARATORLOOSE",`^${L[M.GTLT]}\\s*(${L[M.LOOSEPLAIN]})$|^$`);K("COMPARATOR",`^${L[M.GTLT]}\\s*(${L[M.FULLPLAIN]})$|^$`);K("COMPARATORTRIM",`(\\s*)${L[M.GTLT]}\\s*(${L[M.LOOSEPLAIN]}|${L[M.XRANGEPLAIN]})`,!0);Mt.comparatorTrimReplace="$1$2$3";K("HYPHENRANGE",`^\\s*(${L[M.XRANGEPLAIN]})\\s+-\\s+(${L[M.XRANGEPLAIN]})\\s*$`);K("HYPHENRANGELOOSE",`^\\s*(${L[M.XRANGEPLAINLOOSE]})\\s+-\\s+(${L[M.XRANGEPLAINLOOSE]})\\s*$`);K("STAR","(<|>)?=?\\s*\\*");K("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");K("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var Ws=V((ED,_h)=>{"use strict";var HA=Object.freeze({loose:!0}),qA=Object.freeze({}),GA=t=>t?typeof t!="object"?HA:t:qA;_h.exports=GA});var Xl=V((vD,Ih)=>{"use strict";var Fh=/^[0-9]+$/,Ph=(t,e)=>{let n=Fh.test(t),r=Fh.test(e);return n&&r&&(t=+t,e=+e),t===e?0:n&&!r?-1:r&&!n?1:t<e?-1:1},jA=(t,e)=>Ph(e,t);Ih.exports={compareIdentifiers:Ph,rcompareIdentifiers:jA}});var Ye=V((CD,Dh)=>{"use strict";var Us=ui(),{MAX_LENGTH:Rh,MAX_SAFE_INTEGER:Hs}=li(),{safeRe:qs,t:Gs}=vr(),KA=Ws(),{compareIdentifiers:Cr}=Xl(),Zl=class t{constructor(e,n){if(n=KA(n),e instanceof t){if(e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease)return e;e=e.version}else if(typeof e!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>Rh)throw new TypeError(`version is longer than ${Rh} characters`);Us("SemVer",e,n),this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease;let r=e.trim().match(n.loose?qs[Gs.LOOSE]:qs[Gs.FULL]);if(!r)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>Hs||this.major<0)throw new TypeError("Invalid major version");if(this.minor>Hs||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>Hs||this.patch<0)throw new TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(s=>{if(/^[0-9]+$/.test(s)){let o=+s;if(o>=0&&o<Hs)return o}return s}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(Us("SemVer.compare",this.version,this.options,e),!(e instanceof t)){if(typeof e=="string"&&e===this.version)return 0;e=new t(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof t||(e=new t(e,this.options)),Cr(this.major,e.major)||Cr(this.minor,e.minor)||Cr(this.patch,e.patch)}comparePre(e){if(e instanceof t||(e=new t(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let n=0;do{let r=this.prerelease[n],s=e.prerelease[n];if(Us("prerelease compare",n,r,s),r===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(r===void 0)return-1;if(r===s)continue;return Cr(r,s)}while(++n)}compareBuild(e){e instanceof t||(e=new t(e,this.options));let n=0;do{let r=this.build[n],s=e.build[n];if(Us("build compare",n,r,s),r===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(r===void 0)return-1;if(r===s)continue;return Cr(r,s)}while(++n)}inc(e,n,r){if(e.startsWith("pre")){if(!n&&r===!1)throw new Error("invalid increment argument: identifier is empty");if(n){let s=`-${n}`.match(this.options.loose?qs[Gs.PRERELEASELOOSE]:qs[Gs.PRERELEASE]);if(!s||s[1]!==n)throw new Error(`invalid identifier: ${n}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",n,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",n,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",n,r),this.inc("pre",n,r);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",n,r),this.inc("pre",n,r);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let s=Number(r)?1:0;if(this.prerelease.length===0)this.prerelease=[s];else{let o=this.prerelease.length;for(;--o>=0;)typeof this.prerelease[o]=="number"&&(this.prerelease[o]++,o=-2);if(o===-1){if(n===this.prerelease.join(".")&&r===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(s)}}if(n){let o=[n,s];r===!1&&(o=[n]),Cr(this.prerelease[0],n)===0?isNaN(this.prerelease[1])&&(this.prerelease=o):this.prerelease=o}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};Dh.exports=Zl});var Yn=V((SD,Lh)=>{"use strict";var Oh=Ye(),XA=(t,e,n=!1)=>{if(t instanceof Oh)return t;try{return new Oh(t,e)}catch(r){if(!n)return null;throw r}};Lh.exports=XA});var Nh=V((TD,Mh)=>{"use strict";var ZA=Yn(),JA=(t,e)=>{let n=ZA(t,e);return n?n.version:null};Mh.exports=JA});var $h=V((_D,Bh)=>{"use strict";var QA=Yn(),eE=(t,e)=>{let n=QA(t.trim().replace(/^[=v]+/,""),e);return n?n.version:null};Bh.exports=eE});var zh=V((FD,Vh)=>{"use strict";var Yh=Ye(),tE=(t,e,n,r,s)=>{typeof n=="string"&&(s=r,r=n,n=void 0);try{return new Yh(t instanceof Yh?t.version:t,n).inc(e,r,s).version}catch{return null}};Vh.exports=tE});var Hh=V((PD,Uh)=>{"use strict";var Wh=Yn(),nE=(t,e)=>{let n=Wh(t,null,!0),r=Wh(e,null,!0),s=n.compare(r);if(s===0)return null;let o=s>0,l=o?n:r,u=o?r:n,d=!!l.prerelease.length;if(!!u.prerelease.length&&!d){if(!u.patch&&!u.minor)return"major";if(u.compareMain(l)===0)return u.minor&&!u.patch?"minor":"patch"}let m=d?"pre":"";return n.major!==r.major?m+"major":n.minor!==r.minor?m+"minor":n.patch!==r.patch?m+"patch":"prerelease"};Uh.exports=nE});var Gh=V((ID,qh)=>{"use strict";var rE=Ye(),iE=(t,e)=>new rE(t,e).major;qh.exports=iE});var Kh=V((RD,jh)=>{"use strict";var sE=Ye(),oE=(t,e)=>new sE(t,e).minor;jh.exports=oE});var Zh=V((DD,Xh)=>{"use strict";var aE=Ye(),lE=(t,e)=>new aE(t,e).patch;Xh.exports=lE});var Qh=V((OD,Jh)=>{"use strict";var uE=Yn(),cE=(t,e)=>{let n=uE(t,e);return n&&n.prerelease.length?n.prerelease:null};Jh.exports=cE});var gt=V((LD,tm)=>{"use strict";var em=Ye(),dE=(t,e,n)=>new em(t,n).compare(new em(e,n));tm.exports=dE});var rm=V((MD,nm)=>{"use strict";var fE=gt(),hE=(t,e,n)=>fE(e,t,n);nm.exports=hE});var sm=V((ND,im)=>{"use strict";var mE=gt(),pE=(t,e)=>mE(t,e,!0);im.exports=pE});var js=V((BD,am)=>{"use strict";var om=Ye(),gE=(t,e,n)=>{let r=new om(t,n),s=new om(e,n);return r.compare(s)||r.compareBuild(s)};am.exports=gE});var um=V(($D,lm)=>{"use strict";var wE=js(),yE=(t,e)=>t.sort((n,r)=>wE(n,r,e));lm.exports=yE});var dm=V((YD,cm)=>{"use strict";var bE=js(),kE=(t,e)=>t.sort((n,r)=>bE(r,n,e));cm.exports=kE});var ci=V((VD,fm)=>{"use strict";var xE=gt(),AE=(t,e,n)=>xE(t,e,n)>0;fm.exports=AE});var Ks=V((zD,hm)=>{"use strict";var EE=gt(),vE=(t,e,n)=>EE(t,e,n)<0;hm.exports=vE});var Jl=V((WD,mm)=>{"use strict";var CE=gt(),SE=(t,e,n)=>CE(t,e,n)===0;mm.exports=SE});var Ql=V((UD,pm)=>{"use strict";var TE=gt(),_E=(t,e,n)=>TE(t,e,n)!==0;pm.exports=_E});var Xs=V((HD,gm)=>{"use strict";var FE=gt(),PE=(t,e,n)=>FE(t,e,n)>=0;gm.exports=PE});var Zs=V((qD,wm)=>{"use strict";var IE=gt(),RE=(t,e,n)=>IE(t,e,n)<=0;wm.exports=RE});var eu=V((GD,ym)=>{"use strict";var DE=Jl(),OE=Ql(),LE=ci(),ME=Xs(),NE=Ks(),BE=Zs(),$E=(t,e,n,r)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof n=="object"&&(n=n.version),t===n;case"!==":return typeof t=="object"&&(t=t.version),typeof n=="object"&&(n=n.version),t!==n;case"":case"=":case"==":return DE(t,n,r);case"!=":return OE(t,n,r);case">":return LE(t,n,r);case">=":return ME(t,n,r);case"<":return NE(t,n,r);case"<=":return BE(t,n,r);default:throw new TypeError(`Invalid operator: ${e}`)}};ym.exports=$E});var km=V((jD,bm)=>{"use strict";var YE=Ye(),VE=Yn(),{safeRe:Js,t:Qs}=vr(),zE=(t,e)=>{if(t instanceof YE)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let n=null;if(!e.rtl)n=t.match(e.includePrerelease?Js[Qs.COERCEFULL]:Js[Qs.COERCE]);else{let d=e.includePrerelease?Js[Qs.COERCERTLFULL]:Js[Qs.COERCERTL],f;for(;(f=d.exec(t))&&(!n||n.index+n[0].length!==t.length);)(!n||f.index+f[0].length!==n.index+n[0].length)&&(n=f),d.lastIndex=f.index+f[1].length+f[2].length;d.lastIndex=-1}if(n===null)return null;let r=n[2],s=n[3]||"0",o=n[4]||"0",l=e.includePrerelease&&n[5]?`-${n[5]}`:"",u=e.includePrerelease&&n[6]?`+${n[6]}`:"";return VE(`${r}.${s}.${o}${l}${u}`,e)};bm.exports=zE});var Am=V((KD,xm)=>{"use strict";var tu=class{constructor(){this.max=1e3,this.map=new Map}get(e){let n=this.map.get(e);if(n!==void 0)return this.map.delete(e),this.map.set(e,n),n}delete(e){return this.map.delete(e)}set(e,n){if(!this.delete(e)&&n!==void 0){if(this.map.size>=this.max){let s=this.map.keys().next().value;this.delete(s)}this.map.set(e,n)}return this}};xm.exports=tu});var wt=V((XD,Sm)=>{"use strict";var WE=/\s+/g,nu=class t{constructor(e,n){if(n=HE(n),e instanceof t)return e.loose===!!n.loose&&e.includePrerelease===!!n.includePrerelease?e:new t(e.raw,n);if(e instanceof ru)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=n,this.loose=!!n.loose,this.includePrerelease=!!n.includePrerelease,this.raw=e.trim().replace(WE," "),this.set=this.raw.split("||").map(r=>this.parseRange(r.trim())).filter(r=>r.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let r=this.set[0];if(this.set=this.set.filter(s=>!vm(s[0])),this.set.length===0)this.set=[r];else if(this.set.length>1){for(let s of this.set)if(s.length===1&&JE(s[0])){this.set=[s];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let n=this.set[e];for(let r=0;r<n.length;r++)r>0&&(this.formatted+=" "),this.formatted+=n[r].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let r=((this.options.includePrerelease&&XE)|(this.options.loose&&ZE))+":"+e,s=Em.get(r);if(s)return s;let o=this.options.loose,l=o?rt[Ke.HYPHENRANGELOOSE]:rt[Ke.HYPHENRANGE];e=e.replace(l,lv(this.options.includePrerelease)),he("hyphen replace",e),e=e.replace(rt[Ke.COMPARATORTRIM],GE),he("comparator trim",e),e=e.replace(rt[Ke.TILDETRIM],jE),he("tilde trim",e),e=e.replace(rt[Ke.CARETTRIM],KE),he("caret trim",e);let u=e.split(" ").map(p=>QE(p,this.options)).join(" ").split(/\s+/).map(p=>av(p,this.options));o&&(u=u.filter(p=>(he("loose invalid filter",p,this.options),!!p.match(rt[Ke.COMPARATORLOOSE])))),he("range list",u);let d=new Map,f=u.map(p=>new ru(p,this.options));for(let p of f){if(vm(p))return[p];d.set(p.value,p)}d.size>1&&d.has("")&&d.delete("");let m=[...d.values()];return Em.set(r,m),m}intersects(e,n){if(!(e instanceof t))throw new TypeError("a Range is required");return this.set.some(r=>Cm(r,n)&&e.set.some(s=>Cm(s,n)&&r.every(o=>s.every(l=>o.intersects(l,n)))))}test(e){if(!e)return!1;if(typeof e=="string")try{e=new qE(e,this.options)}catch{return!1}for(let n=0;n<this.set.length;n++)if(uv(this.set[n],e,this.options))return!0;return!1}};Sm.exports=nu;var UE=Am(),Em=new UE,HE=Ws(),ru=di(),he=ui(),qE=Ye(),{safeRe:rt,t:Ke,comparatorTrimReplace:GE,tildeTrimReplace:jE,caretTrimReplace:KE}=vr(),{FLAG_INCLUDE_PRERELEASE:XE,FLAG_LOOSE:ZE}=li(),vm=t=>t.value==="<0.0.0-0",JE=t=>t.value==="",Cm=(t,e)=>{let n=!0,r=t.slice(),s=r.pop();for(;n&&r.length;)n=r.every(o=>s.intersects(o,e)),s=r.pop();return n},QE=(t,e)=>(he("comp",t,e),t=nv(t,e),he("caret",t),t=ev(t,e),he("tildes",t),t=iv(t,e),he("xrange",t),t=ov(t,e),he("stars",t),t),Xe=t=>!t||t.toLowerCase()==="x"||t==="*",ev=(t,e)=>t.trim().split(/\s+/).map(n=>tv(n,e)).join(" "),tv=(t,e)=>{let n=e.loose?rt[Ke.TILDELOOSE]:rt[Ke.TILDE];return t.replace(n,(r,s,o,l,u)=>{he("tilde",t,r,s,o,l,u);let d;return Xe(s)?d="":Xe(o)?d=`>=${s}.0.0 <${+s+1}.0.0-0`:Xe(l)?d=`>=${s}.${o}.0 <${s}.${+o+1}.0-0`:u?(he("replaceTilde pr",u),d=`>=${s}.${o}.${l}-${u} <${s}.${+o+1}.0-0`):d=`>=${s}.${o}.${l} <${s}.${+o+1}.0-0`,he("tilde return",d),d})},nv=(t,e)=>t.trim().split(/\s+/).map(n=>rv(n,e)).join(" "),rv=(t,e)=>{he("caret",t,e);let n=e.loose?rt[Ke.CARETLOOSE]:rt[Ke.CARET],r=e.includePrerelease?"-0":"";return t.replace(n,(s,o,l,u,d)=>{he("caret",t,s,o,l,u,d);let f;return Xe(o)?f="":Xe(l)?f=`>=${o}.0.0${r} <${+o+1}.0.0-0`:Xe(u)?o==="0"?f=`>=${o}.${l}.0${r} <${o}.${+l+1}.0-0`:f=`>=${o}.${l}.0${r} <${+o+1}.0.0-0`:d?(he("replaceCaret pr",d),o==="0"?l==="0"?f=`>=${o}.${l}.${u}-${d} <${o}.${l}.${+u+1}-0`:f=`>=${o}.${l}.${u}-${d} <${o}.${+l+1}.0-0`:f=`>=${o}.${l}.${u}-${d} <${+o+1}.0.0-0`):(he("no pr"),o==="0"?l==="0"?f=`>=${o}.${l}.${u}${r} <${o}.${l}.${+u+1}-0`:f=`>=${o}.${l}.${u}${r} <${o}.${+l+1}.0-0`:f=`>=${o}.${l}.${u} <${+o+1}.0.0-0`),he("caret return",f),f})},iv=(t,e)=>(he("replaceXRanges",t,e),t.split(/\s+/).map(n=>sv(n,e)).join(" ")),sv=(t,e)=>{t=t.trim();let n=e.loose?rt[Ke.XRANGELOOSE]:rt[Ke.XRANGE];return t.replace(n,(r,s,o,l,u,d)=>{he("xRange",t,r,s,o,l,u,d);let f=Xe(o),m=f||Xe(l),p=m||Xe(u),y=p;return s==="="&&y&&(s=""),d=e.includePrerelease?"-0":"",f?s===">"||s==="<"?r="<0.0.0-0":r="*":s&&y?(m&&(l=0),u=0,s===">"?(s=">=",m?(o=+o+1,l=0,u=0):(l=+l+1,u=0)):s==="<="&&(s="<",m?o=+o+1:l=+l+1),s==="<"&&(d="-0"),r=`${s+o}.${l}.${u}${d}`):m?r=`>=${o}.0.0${d} <${+o+1}.0.0-0`:p&&(r=`>=${o}.${l}.0${d} <${o}.${+l+1}.0-0`),he("xRange return",r),r})},ov=(t,e)=>(he("replaceStars",t,e),t.trim().replace(rt[Ke.STAR],"")),av=(t,e)=>(he("replaceGTE0",t,e),t.trim().replace(rt[e.includePrerelease?Ke.GTE0PRE:Ke.GTE0],"")),lv=t=>(e,n,r,s,o,l,u,d,f,m,p,y)=>(Xe(r)?n="":Xe(s)?n=`>=${r}.0.0${t?"-0":""}`:Xe(o)?n=`>=${r}.${s}.0${t?"-0":""}`:l?n=`>=${n}`:n=`>=${n}${t?"-0":""}`,Xe(f)?d="":Xe(m)?d=`<${+f+1}.0.0-0`:Xe(p)?d=`<${f}.${+m+1}.0-0`:y?d=`<=${f}.${m}.${p}-${y}`:t?d=`<${f}.${m}.${+p+1}-0`:d=`<=${d}`,`${n} ${d}`.trim()),uv=(t,e,n)=>{for(let r=0;r<t.length;r++)if(!t[r].test(e))return!1;if(e.prerelease.length&&!n.includePrerelease){for(let r=0;r<t.length;r++)if(he(t[r].semver),t[r].semver!==ru.ANY&&t[r].semver.prerelease.length>0){let s=t[r].semver;if(s.major===e.major&&s.minor===e.minor&&s.patch===e.patch)return!0}return!1}return!0}});var di=V((ZD,Rm)=>{"use strict";var fi=Symbol("SemVer ANY"),ou=class t{static get ANY(){return fi}constructor(e,n){if(n=Tm(n),e instanceof t){if(e.loose===!!n.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),su("comparator",e,n),this.options=n,this.loose=!!n.loose,this.parse(e),this.semver===fi?this.value="":this.value=this.operator+this.semver.version,su("comp",this)}parse(e){let n=this.options.loose?_m[Fm.COMPARATORLOOSE]:_m[Fm.COMPARATOR],r=e.match(n);if(!r)throw new TypeError(`Invalid comparator: ${e}`);this.operator=r[1]!==void 0?r[1]:"",this.operator==="="&&(this.operator=""),r[2]?this.semver=new Pm(r[2],this.options.loose):this.semver=fi}toString(){return this.value}test(e){if(su("Comparator.test",e,this.options.loose),this.semver===fi||e===fi)return!0;if(typeof e=="string")try{e=new Pm(e,this.options)}catch{return!1}return iu(e,this.operator,this.semver,this.options)}intersects(e,n){if(!(e instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new Im(e.value,n).test(this.value):e.operator===""?e.value===""?!0:new Im(this.value,n).test(e.semver):(n=Tm(n),n.includePrerelease&&(this.value==="<0.0.0-0"||e.value==="<0.0.0-0")||!n.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||iu(this.semver,"<",e.semver,n)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||iu(this.semver,">",e.semver,n)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}};Rm.exports=ou;var Tm=Ws(),{safeRe:_m,t:Fm}=vr(),iu=eu(),su=ui(),Pm=Ye(),Im=wt()});var hi=V((JD,Dm)=>{"use strict";var cv=wt(),dv=(t,e,n)=>{try{e=new cv(e,n)}catch{return!1}return e.test(t)};Dm.exports=dv});var Lm=V((QD,Om)=>{"use strict";var fv=wt(),hv=(t,e)=>new fv(t,e).set.map(n=>n.map(r=>r.value).join(" ").trim().split(" "));Om.exports=hv});var Nm=V((eO,Mm)=>{"use strict";var mv=Ye(),pv=wt(),gv=(t,e,n)=>{let r=null,s=null,o=null;try{o=new pv(e,n)}catch{return null}return t.forEach(l=>{o.test(l)&&(!r||s.compare(l)===-1)&&(r=l,s=new mv(r,n))}),r};Mm.exports=gv});var $m=V((tO,Bm)=>{"use strict";var wv=Ye(),yv=wt(),bv=(t,e,n)=>{let r=null,s=null,o=null;try{o=new yv(e,n)}catch{return null}return t.forEach(l=>{o.test(l)&&(!r||s.compare(l)===1)&&(r=l,s=new wv(r,n))}),r};Bm.exports=bv});var zm=V((nO,Vm)=>{"use strict";var au=Ye(),kv=wt(),Ym=ci(),xv=(t,e)=>{t=new kv(t,e);let n=new au("0.0.0");if(t.test(n)||(n=new au("0.0.0-0"),t.test(n)))return n;n=null;for(let r=0;r<t.set.length;++r){let s=t.set[r],o=null;s.forEach(l=>{let u=new au(l.semver.version);switch(l.operator){case">":u.prerelease.length===0?u.patch++:u.prerelease.push(0),u.raw=u.format();case"":case">=":(!o||Ym(u,o))&&(o=u);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${l.operator}`)}}),o&&(!n||Ym(n,o))&&(n=o)}return n&&t.test(n)?n:null};Vm.exports=xv});var Um=V((rO,Wm)=>{"use strict";var Av=wt(),Ev=(t,e)=>{try{return new Av(t,e).range||"*"}catch{return null}};Wm.exports=Ev});var eo=V((iO,jm)=>{"use strict";var vv=Ye(),Gm=di(),{ANY:Cv}=Gm,Sv=wt(),Tv=hi(),Hm=ci(),qm=Ks(),_v=Zs(),Fv=Xs(),Pv=(t,e,n,r)=>{t=new vv(t,r),e=new Sv(e,r);let s,o,l,u,d;switch(n){case">":s=Hm,o=_v,l=qm,u=">",d=">=";break;case"<":s=qm,o=Fv,l=Hm,u="<",d="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Tv(t,e,r))return!1;for(let f=0;f<e.set.length;++f){let m=e.set[f],p=null,y=null;if(m.forEach(g=>{g.semver===Cv&&(g=new Gm(">=0.0.0")),p=p||g,y=y||g,s(g.semver,p.semver,r)?p=g:l(g.semver,y.semver,r)&&(y=g)}),p.operator===u||p.operator===d||(!y.operator||y.operator===u)&&o(t,y.semver))return!1;if(y.operator===d&&l(t,y.semver))return!1}return!0};jm.exports=Pv});var Xm=V((sO,Km)=>{"use strict";var Iv=eo(),Rv=(t,e,n)=>Iv(t,e,">",n);Km.exports=Rv});var Jm=V((oO,Zm)=>{"use strict";var Dv=eo(),Ov=(t,e,n)=>Dv(t,e,"<",n);Zm.exports=Ov});var tp=V((aO,ep)=>{"use strict";var Qm=wt(),Lv=(t,e,n)=>(t=new Qm(t,n),e=new Qm(e,n),t.intersects(e,n));ep.exports=Lv});var rp=V((lO,np)=>{"use strict";var Mv=hi(),Nv=gt();np.exports=(t,e,n)=>{let r=[],s=null,o=null,l=t.sort((m,p)=>Nv(m,p,n));for(let m of l)Mv(m,e,n)?(o=m,s||(s=m)):(o&&r.push([s,o]),o=null,s=null);s&&r.push([s,null]);let u=[];for(let[m,p]of r)m===p?u.push(m):!p&&m===l[0]?u.push("*"):p?m===l[0]?u.push(`<=${p}`):u.push(`${m} - ${p}`):u.push(`>=${m}`);let d=u.join(" || "),f=typeof e.raw=="string"?e.raw:String(e);return d.length<f.length?d:e}});var up=V((uO,lp)=>{"use strict";var ip=wt(),uu=di(),{ANY:lu}=uu,mi=hi(),cu=gt(),Bv=(t,e,n={})=>{if(t===e)return!0;t=new ip(t,n),e=new ip(e,n);let r=!1;e:for(let s of t.set){for(let o of e.set){let l=Yv(s,o,n);if(r=r||l!==null,l)continue e}if(r)return!1}return!0},$v=[new uu(">=0.0.0-0")],sp=[new uu(">=0.0.0")],Yv=(t,e,n)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===lu){if(e.length===1&&e[0].semver===lu)return!0;n.includePrerelease?t=$v:t=sp}if(e.length===1&&e[0].semver===lu){if(n.includePrerelease)return!0;e=sp}let r=new Set,s,o;for(let g of t)g.operator===">"||g.operator===">="?s=op(s,g,n):g.operator==="<"||g.operator==="<="?o=ap(o,g,n):r.add(g.semver);if(r.size>1)return null;let l;if(s&&o){if(l=cu(s.semver,o.semver,n),l>0)return null;if(l===0&&(s.operator!==">="||o.operator!=="<="))return null}for(let g of r){if(s&&!mi(g,String(s),n)||o&&!mi(g,String(o),n))return null;for(let A of e)if(!mi(g,String(A),n))return!1;return!0}let u,d,f,m,p=o&&!n.includePrerelease&&o.semver.prerelease.length?o.semver:!1,y=s&&!n.includePrerelease&&s.semver.prerelease.length?s.semver:!1;p&&p.prerelease.length===1&&o.operator==="<"&&p.prerelease[0]===0&&(p=!1);for(let g of e){if(m=m||g.operator===">"||g.operator===">=",f=f||g.operator==="<"||g.operator==="<=",s){if(y&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===y.major&&g.semver.minor===y.minor&&g.semver.patch===y.patch&&(y=!1),g.operator===">"||g.operator===">="){if(u=op(s,g,n),u===g&&u!==s)return!1}else if(s.operator===">="&&!mi(s.semver,String(g),n))return!1}if(o){if(p&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===p.major&&g.semver.minor===p.minor&&g.semver.patch===p.patch&&(p=!1),g.operator==="<"||g.operator==="<="){if(d=ap(o,g,n),d===g&&d!==o)return!1}else if(o.operator==="<="&&!mi(o.semver,String(g),n))return!1}if(!g.operator&&(o||s)&&l!==0)return!1}return!(s&&f&&!o&&l!==0||o&&m&&!s&&l!==0||y||p)},op=(t,e,n)=>{if(!t)return e;let r=cu(t.semver,e.semver,n);return r>0?t:r<0||e.operator===">"&&t.operator===">="?e:t},ap=(t,e,n)=>{if(!t)return e;let r=cu(t.semver,e.semver,n);return r<0?t:r>0||e.operator==="<"&&t.operator==="<="?e:t};lp.exports=Bv});var hp=V((cO,fp)=>{"use strict";var du=vr(),cp=li(),Vv=Ye(),dp=Xl(),zv=Yn(),Wv=Nh(),Uv=$h(),Hv=zh(),qv=Hh(),Gv=Gh(),jv=Kh(),Kv=Zh(),Xv=Qh(),Zv=gt(),Jv=rm(),Qv=sm(),eC=js(),tC=um(),nC=dm(),rC=ci(),iC=Ks(),sC=Jl(),oC=Ql(),aC=Xs(),lC=Zs(),uC=eu(),cC=km(),dC=di(),fC=wt(),hC=hi(),mC=Lm(),pC=Nm(),gC=$m(),wC=zm(),yC=Um(),bC=eo(),kC=Xm(),xC=Jm(),AC=tp(),EC=rp(),vC=up();fp.exports={parse:zv,valid:Wv,clean:Uv,inc:Hv,diff:qv,major:Gv,minor:jv,patch:Kv,prerelease:Xv,compare:Zv,rcompare:Jv,compareLoose:Qv,compareBuild:eC,sort:tC,rsort:nC,gt:rC,lt:iC,eq:sC,neq:oC,gte:aC,lte:lC,cmp:uC,coerce:cC,Comparator:dC,Range:fC,satisfies:hC,toComparators:mC,maxSatisfying:pC,minSatisfying:gC,minVersion:wC,validRange:yC,outside:bC,gtr:kC,ltr:xC,intersects:AC,simplifyRange:EC,subset:vC,SemVer:Vv,re:du.re,src:du.src,tokens:du.t,SEMVER_SPEC_VERSION:cp.SEMVER_SPEC_VERSION,RELEASE_TYPES:cp.RELEASE_TYPES,compareIdentifiers:dp.compareIdentifiers,rcompareIdentifiers:dp.rcompareIdentifiers}});var yp=V((pu,gu)=>{(function(t,e,n){typeof pu=="object"?(gu.exports=n(),gu.exports.default=n()):typeof define=="function"&&define.amd?define(n):e[t]=n()})("slugify",pu,function(){var t=JSON.parse(`{"$":"dollar","%":"percent","&":"and","<":"less",">":"greater","|":"or","\xA2":"cent","\xA3":"pound","\xA4":"currency","\xA5":"yen","\xA9":"(c)","\xAA":"a","\xAE":"(r)","\xBA":"o","\xC0":"A","\xC1":"A","\xC2":"A","\xC3":"A","\xC4":"A","\xC5":"A","\xC6":"AE","\xC7":"C","\xC8":"E","\xC9":"E","\xCA":"E","\xCB":"E","\xCC":"I","\xCD":"I","\xCE":"I","\xCF":"I","\xD0":"D","\xD1":"N","\xD2":"O","\xD3":"O","\xD4":"O","\xD5":"O","\xD6":"O","\xD8":"O","\xD9":"U","\xDA":"U","\xDB":"U","\xDC":"U","\xDD":"Y","\xDE":"TH","\xDF":"ss","\xE0":"a","\xE1":"a","\xE2":"a","\xE3":"a","\xE4":"a","\xE5":"a","\xE6":"ae","\xE7":"c","\xE8":"e","\xE9":"e","\xEA":"e","\xEB":"e","\xEC":"i","\xED":"i","\xEE":"i","\xEF":"i","\xF0":"d","\xF1":"n","\xF2":"o","\xF3":"o","\xF4":"o","\xF5":"o","\xF6":"o","\xF8":"o","\xF9":"u","\xFA":"u","\xFB":"u","\xFC":"u","\xFD":"y","\xFE":"th","\xFF":"y","\u0100":"A","\u0101":"a","\u0102":"A","\u0103":"a","\u0104":"A","\u0105":"a","\u0106":"C","\u0107":"c","\u010C":"C","\u010D":"c","\u010E":"D","\u010F":"d","\u0110":"DJ","\u0111":"dj","\u0112":"E","\u0113":"e","\u0116":"E","\u0117":"e","\u0118":"e","\u0119":"e","\u011A":"E","\u011B":"e","\u011E":"G","\u011F":"g","\u0122":"G","\u0123":"g","\u0128":"I","\u0129":"i","\u012A":"i","\u012B":"i","\u012E":"I","\u012F":"i","\u0130":"I","\u0131":"i","\u0136":"k","\u0137":"k","\u013B":"L","\u013C":"l","\u013D":"L","\u013E":"l","\u0141":"L","\u0142":"l","\u0143":"N","\u0144":"n","\u0145":"N","\u0146":"n","\u0147":"N","\u0148":"n","\u014C":"O","\u014D":"o","\u0150":"O","\u0151":"o","\u0152":"OE","\u0153":"oe","\u0154":"R","\u0155":"r","\u0158":"R","\u0159":"r","\u015A":"S","\u015B":"s","\u015E":"S","\u015F":"s","\u0160":"S","\u0161":"s","\u0162":"T","\u0163":"t","\u0164":"T","\u0165":"t","\u0168":"U","\u0169":"u","\u016A":"u","\u016B":"u","\u016E":"U","\u016F":"u","\u0170":"U","\u0171":"u","\u0172":"U","\u0173":"u","\u0174":"W","\u0175":"w","\u0176":"Y","\u0177":"y","\u0178":"Y","\u0179":"Z","\u017A":"z","\u017B":"Z","\u017C":"z","\u017D":"Z","\u017E":"z","\u018F":"E","\u0192":"f","\u01A0":"O","\u01A1":"o","\u01AF":"U","\u01B0":"u","\u01C8":"LJ","\u01C9":"lj","\u01CB":"NJ","\u01CC":"nj","\u0218":"S","\u0219":"s","\u021A":"T","\u021B":"t","\u0259":"e","\u02DA":"o","\u0386":"A","\u0388":"E","\u0389":"H","\u038A":"I","\u038C":"O","\u038E":"Y","\u038F":"W","\u0390":"i","\u0391":"A","\u0392":"B","\u0393":"G","\u0394":"D","\u0395":"E","\u0396":"Z","\u0397":"H","\u0398":"8","\u0399":"I","\u039A":"K","\u039B":"L","\u039C":"M","\u039D":"N","\u039E":"3","\u039F":"O","\u03A0":"P","\u03A1":"R","\u03A3":"S","\u03A4":"T","\u03A5":"Y","\u03A6":"F","\u03A7":"X","\u03A8":"PS","\u03A9":"W","\u03AA":"I","\u03AB":"Y","\u03AC":"a","\u03AD":"e","\u03AE":"h","\u03AF":"i","\u03B0":"y","\u03B1":"a","\u03B2":"b","\u03B3":"g","\u03B4":"d","\u03B5":"e","\u03B6":"z","\u03B7":"h","\u03B8":"8","\u03B9":"i","\u03BA":"k","\u03BB":"l","\u03BC":"m","\u03BD":"n","\u03BE":"3","\u03BF":"o","\u03C0":"p","\u03C1":"r","\u03C2":"s","\u03C3":"s","\u03C4":"t","\u03C5":"y","\u03C6":"f","\u03C7":"x","\u03C8":"ps","\u03C9":"w","\u03CA":"i","\u03CB":"y","\u03CC":"o","\u03CD":"y","\u03CE":"w","\u0401":"Yo","\u0402":"DJ","\u0404":"Ye","\u0406":"I","\u0407":"Yi","\u0408":"J","\u0409":"LJ","\u040A":"NJ","\u040B":"C","\u040F":"DZ","\u0410":"A","\u0411":"B","\u0412":"V","\u0413":"G","\u0414":"D","\u0415":"E","\u0416":"Zh","\u0417":"Z","\u0418":"I","\u0419":"J","\u041A":"K","\u041B":"L","\u041C":"M","\u041D":"N","\u041E":"O","\u041F":"P","\u0420":"R","\u0421":"S","\u0422":"T","\u0423":"U","\u0424":"F","\u0425":"H","\u0426":"C","\u0427":"Ch","\u0428":"Sh","\u0429":"Sh","\u042A":"U","\u042B":"Y","\u042C":"","\u042D":"E","\u042E":"Yu","\u042F":"Ya","\u0430":"a","\u0431":"b","\u0432":"v","\u0433":"g","\u0434":"d","\u0435":"e","\u0436":"zh","\u0437":"z","\u0438":"i","\u0439":"j","\u043A":"k","\u043B":"l","\u043C":"m","\u043D":"n","\u043E":"o","\u043F":"p","\u0440":"r","\u0441":"s","\u0442":"t","\u0443":"u","\u0444":"f","\u0445":"h","\u0446":"c","\u0447":"ch","\u0448":"sh","\u0449":"sh","\u044A":"u","\u044B":"y","\u044C":"","\u044D":"e","\u044E":"yu","\u044F":"ya","\u0451":"yo","\u0452":"dj","\u0454":"ye","\u0456":"i","\u0457":"yi","\u0458":"j","\u0459":"lj","\u045A":"nj","\u045B":"c","\u045D":"u","\u045F":"dz","\u0490":"G","\u0491":"g","\u0492":"GH","\u0493":"gh","\u049A":"KH","\u049B":"kh","\u04A2":"NG","\u04A3":"ng","\u04AE":"UE","\u04AF":"ue","\u04B0":"U","\u04B1":"u","\u04BA":"H","\u04BB":"h","\u04D8":"AE","\u04D9":"ae","\u04E8":"OE","\u04E9":"oe","\u0531":"A","\u0532":"B","\u0533":"G","\u0534":"D","\u0535":"E","\u0536":"Z","\u0537":"E'","\u0538":"Y'","\u0539":"T'","\u053A":"JH","\u053B":"I","\u053C":"L","\u053D":"X","\u053E":"C'","\u053F":"K","\u0540":"H","\u0541":"D'","\u0542":"GH","\u0543":"TW","\u0544":"M","\u0545":"Y","\u0546":"N","\u0547":"SH","\u0549":"CH","\u054A":"P","\u054B":"J","\u054C":"R'","\u054D":"S","\u054E":"V","\u054F":"T","\u0550":"R","\u0551":"C","\u0553":"P'","\u0554":"Q'","\u0555":"O''","\u0556":"F","\u0587":"EV","\u0621":"a","\u0622":"aa","\u0623":"a","\u0624":"u","\u0625":"i","\u0626":"e","\u0627":"a","\u0628":"b","\u0629":"h","\u062A":"t","\u062B":"th","\u062C":"j","\u062D":"h","\u062E":"kh","\u062F":"d","\u0630":"th","\u0631":"r","\u0632":"z","\u0633":"s","\u0634":"sh","\u0635":"s","\u0636":"dh","\u0637":"t","\u0638":"z","\u0639":"a","\u063A":"gh","\u0641":"f","\u0642":"q","\u0643":"k","\u0644":"l","\u0645":"m","\u0646":"n","\u0647":"h","\u0648":"w","\u0649":"a","\u064A":"y","\u064B":"an","\u064C":"on","\u064D":"en","\u064E":"a","\u064F":"u","\u0650":"e","\u0652":"","\u0660":"0","\u0661":"1","\u0662":"2","\u0663":"3","\u0664":"4","\u0665":"5","\u0666":"6","\u0667":"7","\u0668":"8","\u0669":"9","\u067E":"p","\u0686":"ch","\u0698":"zh","\u06A9":"k","\u06AF":"g","\u06CC":"y","\u06F0":"0","\u06F1":"1","\u06F2":"2","\u06F3":"3","\u06F4":"4","\u06F5":"5","\u06F6":"6","\u06F7":"7","\u06F8":"8","\u06F9":"9","\u0E3F":"baht","\u10D0":"a","\u10D1":"b","\u10D2":"g","\u10D3":"d","\u10D4":"e","\u10D5":"v","\u10D6":"z","\u10D7":"t","\u10D8":"i","\u10D9":"k","\u10DA":"l","\u10DB":"m","\u10DC":"n","\u10DD":"o","\u10DE":"p","\u10DF":"zh","\u10E0":"r","\u10E1":"s","\u10E2":"t","\u10E3":"u","\u10E4":"f","\u10E5":"k","\u10E6":"gh","\u10E7":"q","\u10E8":"sh","\u10E9":"ch","\u10EA":"ts","\u10EB":"dz","\u10EC":"ts","\u10ED":"ch","\u10EE":"kh","\u10EF":"j","\u10F0":"h","\u1E62":"S","\u1E63":"s","\u1E80":"W","\u1E81":"w","\u1E82":"W","\u1E83":"w","\u1E84":"W","\u1E85":"w","\u1E9E":"SS","\u1EA0":"A","\u1EA1":"a","\u1EA2":"A","\u1EA3":"a","\u1EA4":"A","\u1EA5":"a","\u1EA6":"A","\u1EA7":"a","\u1EA8":"A","\u1EA9":"a","\u1EAA":"A","\u1EAB":"a","\u1EAC":"A","\u1EAD":"a","\u1EAE":"A","\u1EAF":"a","\u1EB0":"A","\u1EB1":"a","\u1EB2":"A","\u1EB3":"a","\u1EB4":"A","\u1EB5":"a","\u1EB6":"A","\u1EB7":"a","\u1EB8":"E","\u1EB9":"e","\u1EBA":"E","\u1EBB":"e","\u1EBC":"E","\u1EBD":"e","\u1EBE":"E","\u1EBF":"e","\u1EC0":"E","\u1EC1":"e","\u1EC2":"E","\u1EC3":"e","\u1EC4":"E","\u1EC5":"e","\u1EC6":"E","\u1EC7":"e","\u1EC8":"I","\u1EC9":"i","\u1ECA":"I","\u1ECB":"i","\u1ECC":"O","\u1ECD":"o","\u1ECE":"O","\u1ECF":"o","\u1ED0":"O","\u1ED1":"o","\u1ED2":"O","\u1ED3":"o","\u1ED4":"O","\u1ED5":"o","\u1ED6":"O","\u1ED7":"o","\u1ED8":"O","\u1ED9":"o","\u1EDA":"O","\u1EDB":"o","\u1EDC":"O","\u1EDD":"o","\u1EDE":"O","\u1EDF":"o","\u1EE0":"O","\u1EE1":"o","\u1EE2":"O","\u1EE3":"o","\u1EE4":"U","\u1EE5":"u","\u1EE6":"U","\u1EE7":"u","\u1EE8":"U","\u1EE9":"u","\u1EEA":"U","\u1EEB":"u","\u1EEC":"U","\u1EED":"u","\u1EEE":"U","\u1EEF":"u","\u1EF0":"U","\u1EF1":"u","\u1EF2":"Y","\u1EF3":"y","\u1EF4":"Y","\u1EF5":"y","\u1EF6":"Y","\u1EF7":"y","\u1EF8":"Y","\u1EF9":"y","\u2013":"-","\u2018":"'","\u2019":"'","\u201C":"\\"","\u201D":"\\"","\u201E":"\\"","\u2020":"+","\u2022":"*","\u2026":"...","\u20A0":"ecu","\u20A2":"cruzeiro","\u20A3":"french franc","\u20A4":"lira","\u20A5":"mill","\u20A6":"naira","\u20A7":"peseta","\u20A8":"rupee","\u20A9":"won","\u20AA":"new shequel","\u20AB":"dong","\u20AC":"euro","\u20AD":"kip","\u20AE":"tugrik","\u20AF":"drachma","\u20B0":"penny","\u20B1":"peso","\u20B2":"guarani","\u20B3":"austral","\u20B4":"hryvnia","\u20B5":"cedi","\u20B8":"kazakhstani tenge","\u20B9":"indian rupee","\u20BA":"turkish lira","\u20BD":"russian ruble","\u20BF":"bitcoin","\u2120":"sm","\u2122":"tm","\u2202":"d","\u2206":"delta","\u2211":"sum","\u221E":"infinity","\u2665":"love","\u5143":"yuan","\u5186":"yen","\uFDFC":"rial","\uFEF5":"laa","\uFEF7":"laa","\uFEF9":"lai","\uFEFB":"la"}`),e=JSON.parse('{"bg":{"\u0419":"Y","\u0426":"Ts","\u0429":"Sht","\u042A":"A","\u042C":"Y","\u0439":"y","\u0446":"ts","\u0449":"sht","\u044A":"a","\u044C":"y"},"de":{"\xC4":"AE","\xE4":"ae","\xD6":"OE","\xF6":"oe","\xDC":"UE","\xFC":"ue","\xDF":"ss","%":"prozent","&":"und","|":"oder","\u2211":"summe","\u221E":"unendlich","\u2665":"liebe"},"es":{"%":"por ciento","&":"y","<":"menor que",">":"mayor que","|":"o","\xA2":"centavos","\xA3":"libras","\xA4":"moneda","\u20A3":"francos","\u2211":"suma","\u221E":"infinito","\u2665":"amor"},"fr":{"%":"pourcent","&":"et","<":"plus petit",">":"plus grand","|":"ou","\xA2":"centime","\xA3":"livre","\xA4":"devise","\u20A3":"franc","\u2211":"somme","\u221E":"infini","\u2665":"amour"},"pt":{"%":"porcento","&":"e","<":"menor",">":"maior","|":"ou","\xA2":"centavo","\u2211":"soma","\xA3":"libra","\u221E":"infinito","\u2665":"amor"},"uk":{"\u0418":"Y","\u0438":"y","\u0419":"Y","\u0439":"y","\u0426":"Ts","\u0446":"ts","\u0425":"Kh","\u0445":"kh","\u0429":"Shch","\u0449":"shch","\u0413":"H","\u0433":"h"},"vi":{"\u0110":"D","\u0111":"d"},"da":{"\xD8":"OE","\xF8":"oe","\xC5":"AA","\xE5":"aa","%":"procent","&":"og","|":"eller","$":"dollar","<":"mindre end",">":"st\xF8rre end"},"nb":{"&":"og","\xC5":"AA","\xC6":"AE","\xD8":"OE","\xE5":"aa","\xE6":"ae","\xF8":"oe"},"it":{"&":"e"},"nl":{"&":"en"},"sv":{"&":"och","\xC5":"AA","\xC4":"AE","\xD6":"OE","\xE5":"aa","\xE4":"ae","\xF6":"oe"}}');function n(r,s){if(typeof r!="string")throw new Error("slugify: string argument expected");s=typeof s=="string"?{replacement:s}:s||{};var o=e[s.locale]||{},l=s.replacement===void 0?"-":s.replacement,u=s.trim===void 0?!0:s.trim,d=r.normalize().split("").reduce(function(f,m){var p=o[m];return p===void 0&&(p=t[m]),p===void 0&&(p=m),p===l&&(p=" "),f+p.replace(s.remove||/[^\w\s$*_+~.()'"!\-:@]+/g,"")},"");return s.strict&&(d=d.replace(/[^A-Za-z0-9\s]/g,"")),u&&(d=d.trim()),d=d.replace(/\s+/g,l),s.lower&&(d=d.toLowerCase()),d}return n.extend=function(r){Object.assign(t,r)},n})});var jC={};yc(jC,{default:()=>GC});module.exports=Iy(jC);var Iu=require("electron"),Ru=Ne(jt(),1),Hp=require("obsidian");var Pa=Ne(Ic(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Ea="27.4.0",Ui="obsidian-dev-utils",Rc=`.obsidian-dev-utils.code-highlighter-component textarea, .obsidian-dev-utils.code-highlighter-component pre, .obsidian-dev-utils.code-highlighter-component code {
  font-family: var(--font-monospace);
  line-height: var(--line-height-normal);
  margin: 0;
}
.obsidian-dev-utils.code-highlighter-component textarea, .obsidian-dev-utils.code-highlighter-component code {
  font-size: var(--code-size);
}
.obsidian-dev-utils.code-highlighter-component textarea {
  background: transparent;
  color: transparent;
  z-index: 2;
  width: 20em;
  height: 10em;
}
.obsidian-dev-utils.code-highlighter-component pre {
  position: absolute;
  pointer-events: none;
  border: var(--input-border-width) solid transparent;
  overflow: auto;
  inset: 0;
  padding: var(--size-4-1) var(--size-4-2);
  z-index: 1;
}
.obsidian-dev-utils.code-highlighter-component pre.is-placeholder {
  opacity: 0.6;
}
.obsidian-dev-utils.code-highlighter-component code {
  display: block;
  padding: 0;
}

.obsidian-dev-utils input[type=url] {
  height: var(--input-height);
}
.obsidian-dev-utils input[type=month],
.obsidian-dev-utils input[type=tel],
.obsidian-dev-utils input[type=time],
.obsidian-dev-utils input[type=url],
.obsidian-dev-utils input[type=week] {
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
  outline: none;
}
@media (hover: hover) {
  .obsidian-dev-utils input[type=month]:hover,
  .obsidian-dev-utils input[type=tel]:hover,
  .obsidian-dev-utils input[type=time]:hover,
  .obsidian-dev-utils input[type=url]:hover,
  .obsidian-dev-utils input[type=week]:hover {
    border-color: var(--background-modifier-border-hover);
    transition: box-shadow 0.15s ease-in-out, border 0.15s ease-in-out;
  }
}
.obsidian-dev-utils input[type=month]:active, .obsidian-dev-utils input[type=month]:focus,
.obsidian-dev-utils input[type=tel]:active,
.obsidian-dev-utils input[type=tel]:focus,
.obsidian-dev-utils input[type=time]:active,
.obsidian-dev-utils input[type=time]:focus,
.obsidian-dev-utils input[type=url]:active,
.obsidian-dev-utils input[type=url]:focus,
.obsidian-dev-utils input[type=week]:active,
.obsidian-dev-utils input[type=week]:focus {
  border-color: var(--background-modifier-border-focus);
  transition: box-shadow 0.15s ease-in-out, border 0.15s ease-in-out;
}
.obsidian-dev-utils input[type=month]:active, .obsidian-dev-utils input[type=month]:focus, .obsidian-dev-utils input[type=month]:focus-visible,
.obsidian-dev-utils input[type=tel]:active,
.obsidian-dev-utils input[type=tel]:focus,
.obsidian-dev-utils input[type=tel]:focus-visible,
.obsidian-dev-utils input[type=time]:active,
.obsidian-dev-utils input[type=time]:focus,
.obsidian-dev-utils input[type=time]:focus-visible,
.obsidian-dev-utils input[type=url]:active,
.obsidian-dev-utils input[type=url]:focus,
.obsidian-dev-utils input[type=url]:focus-visible,
.obsidian-dev-utils input[type=week]:active,
.obsidian-dev-utils input[type=week]:focus,
.obsidian-dev-utils input[type=week]:focus-visible {
  box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
}
.obsidian-dev-utils input[type=month]::placeholder,
.obsidian-dev-utils input[type=tel]::placeholder,
.obsidian-dev-utils input[type=time]::placeholder,
.obsidian-dev-utils input[type=url]::placeholder,
.obsidian-dev-utils input[type=week]::placeholder {
  color: var(--text-faint);
}
.mod-rtl input[type=month],
.mod-rtl input[type=time],
.mod-rtl input[type=week],
.is-rtl input[type=month],
.is-rtl input[type=time],
.is-rtl input[type=week],
.rtl input[type=month],
.rtl input[type=time],
.rtl input[type=week] {
  direction: rtl;
}
.mod-rtl input[type=month]::-webkit-calendar-picker-indicator,
.mod-rtl input[type=time]::-webkit-calendar-picker-indicator,
.mod-rtl input[type=week]::-webkit-calendar-picker-indicator,
.is-rtl input[type=month]::-webkit-calendar-picker-indicator,
.is-rtl input[type=time]::-webkit-calendar-picker-indicator,
.is-rtl input[type=week]::-webkit-calendar-picker-indicator,
.rtl input[type=month]::-webkit-calendar-picker-indicator,
.rtl input[type=time]::-webkit-calendar-picker-indicator,
.rtl input[type=week]::-webkit-calendar-picker-indicator {
  right: var(--size-4-1);
  left: auto;
}

.obsidian-dev-utils input[type=month],
.obsidian-dev-utils input[type=time],
.obsidian-dev-utils input[type=week] {
  font-variant-numeric: tabular-nums;
  position: relative;
}
.obsidian-dev-utils input[type=month]::-webkit-datetime-edit-text,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-text,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-text {
  color: var(--text-faint);
  padding-inline-end: 0;
}
.obsidian-dev-utils input[type=month]::-webkit-calendar-picker-indicator,
.obsidian-dev-utils input[type=time]::-webkit-calendar-picker-indicator,
.obsidian-dev-utils input[type=week]::-webkit-calendar-picker-indicator {
  position: absolute;
  left: var(--size-4-1);
  right: auto;
  opacity: 0.5;
}
.obsidian-dev-utils input[type=month]::-webkit-datetime-edit-month-field:active, .obsidian-dev-utils input[type=month]::-webkit-datetime-edit-month-field:focus, .obsidian-dev-utils input[type=month]::-webkit-datetime-edit-day-field:active, .obsidian-dev-utils input[type=month]::-webkit-datetime-edit-day-field:focus, .obsidian-dev-utils input[type=month]::-webkit-datetime-edit-year-field:active, .obsidian-dev-utils input[type=month]::-webkit-datetime-edit-year-field:focus,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-month-field:active,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-month-field:focus,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-day-field:active,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-day-field:focus,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-year-field:active,
.obsidian-dev-utils input[type=time]::-webkit-datetime-edit-year-field:focus,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-month-field:active,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-month-field:focus,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-day-field:active,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-day-field:focus,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-year-field:active,
.obsidian-dev-utils input[type=week]::-webkit-datetime-edit-year-field:focus {
  background-color: var(--text-selection);
  color: var(--text-normal);
  cursor: text;
}
.mod-rtl .obsidian-dev-utils input[type=month], .is-rtl .obsidian-dev-utils input[type=month], .rtl .obsidian-dev-utils input[type=month],
.mod-rtl .obsidian-dev-utils input[type=time],
.is-rtl .obsidian-dev-utils input[type=time],
.rtl .obsidian-dev-utils input[type=time],
.mod-rtl .obsidian-dev-utils input[type=week],
.is-rtl .obsidian-dev-utils input[type=week],
.rtl .obsidian-dev-utils input[type=week] {
  direction: rtl;
}
.mod-rtl .obsidian-dev-utils input[type=month]::-webkit-calendar-picker-indicator, .is-rtl .obsidian-dev-utils input[type=month]::-webkit-calendar-picker-indicator, .rtl .obsidian-dev-utils input[type=month]::-webkit-calendar-picker-indicator,
.mod-rtl .obsidian-dev-utils input[type=time]::-webkit-calendar-picker-indicator,
.is-rtl .obsidian-dev-utils input[type=time]::-webkit-calendar-picker-indicator,
.rtl .obsidian-dev-utils input[type=time]::-webkit-calendar-picker-indicator,
.mod-rtl .obsidian-dev-utils input[type=week]::-webkit-calendar-picker-indicator,
.is-rtl .obsidian-dev-utils input[type=week]::-webkit-calendar-picker-indicator,
.rtl .obsidian-dev-utils input[type=week]::-webkit-calendar-picker-indicator {
  left: auto;
  right: var(--size-4-1);
}

body:not(.is-ios):not(.is-android) .obsidian-dev-utils input[type=month],
body:not(.is-ios):not(.is-android) .obsidian-dev-utils input[type=time],
body:not(.is-ios):not(.is-android) .obsidian-dev-utils input[type=week] {
  padding-inline-start: var(--size-4-6);
}

.obsidian-dev-utils input[type=time]::-webkit-calendar-picker-indicator {
  margin-inline-start: 0;
}

.obsidian-dev-utils.modal-container .ok-button {
  margin-right: 10px;
  margin-top: 20px;
}

.obsidian-dev-utils .multiple-dropdown-component select,
.obsidian-dev-utils .multiple-dropdown-component select:focus,
.obsidian-dev-utils .multiple-dropdown-component .dropdown {
  height: auto;
  padding-top: 3px;
}
.obsidian-dev-utils .multiple-dropdown-component select option:checked,
.obsidian-dev-utils .multiple-dropdown-component select:focus option:checked,
.obsidian-dev-utils .multiple-dropdown-component .dropdown option:checked {
  background-color: #1967d2;
  color: #fff;
}

.obsidian-dev-utils.prompt-modal .text-box {
  width: 100%;
}

.obsidian-dev-utils.tri-state-checkbox-component input[type=checkbox]:indeterminate {
  appearance: checkbox;
}

.obsidian-dev-utils :invalid {
  box-shadow: 0 0 0 2px var(--text-error);
}
.obsidian-dev-utils input.metadata-input-text:active:invalid, .obsidian-dev-utils input.metadata-input-text:focus-visible:invalid, .obsidian-dev-utils input.metadata-input-text:focus:invalid,
.obsidian-dev-utils input[type=date]:active:invalid,
.obsidian-dev-utils input[type=date]:focus-visible:invalid,
.obsidian-dev-utils input[type=date]:focus:invalid,
.obsidian-dev-utils input[type=datetime-local]:active:invalid,
.obsidian-dev-utils input[type=datetime-local]:focus-visible:invalid,
.obsidian-dev-utils input[type=datetime-local]:focus:invalid,
.obsidian-dev-utils input[type=email]:active:invalid,
.obsidian-dev-utils input[type=email]:focus-visible:invalid,
.obsidian-dev-utils input[type=email]:focus:invalid,
.obsidian-dev-utils input[type=number]:active:invalid,
.obsidian-dev-utils input[type=number]:focus-visible:invalid,
.obsidian-dev-utils input[type=number]:focus:invalid,
.obsidian-dev-utils input[type=password]:active:invalid,
.obsidian-dev-utils input[type=password]:focus-visible:invalid,
.obsidian-dev-utils input[type=password]:focus:invalid,
.obsidian-dev-utils input[type=search]:active:invalid,
.obsidian-dev-utils input[type=search]:focus-visible:invalid,
.obsidian-dev-utils input[type=search]:focus:invalid,
.obsidian-dev-utils input[type=text]:active:invalid,
.obsidian-dev-utils input[type=text]:focus-visible:invalid,
.obsidian-dev-utils input[type=text]:focus:invalid,
.obsidian-dev-utils textarea:active:invalid,
.obsidian-dev-utils textarea:focus-visible:invalid,
.obsidian-dev-utils textarea:focus:invalid {
  box-shadow: 0 0 0 2px var(--text-error);
}
.obsidian-dev-utils.setting-component-wrapper {
  position: relative;
  display: inline-flex;
}
.obsidian-dev-utils.overlay-validator {
  caret-color: transparent;
  cursor: default;
  position: absolute;
  background-color: transparent;
  border: none;
  outline: none;
  pointer-events: none;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.obsidian-dev-utils.tooltip.tooltip-validator {
  position: absolute;
  top: calc(100% + 8px);
  width: max-content;
}

/*# sourceMappingURL=data:application/json;charset=utf-8,%7B%22version%22:3,%22sourceRoot%22:%22%22,%22sources%22:%5B%22../src/styles/code-highlighter-component.scss%22,%22../src/styles/input.scss%22,%22../src/styles/input-time.scss%22,%22../src/styles/modal-container.scss%22,%22../src/styles/multiple-dropdown-component.scss%22,%22../src/styles/prompt-modal.scss%22,%22../src/styles/tri-state-checkbox-component.scss%22,%22../src/styles/validation.scss%22%5D,%22names%22:%5B%5D,%22mappings%22:%22AAEI;EACE;EACA;EACA;;AAGF;EACE;;AAGF;EACE;EACA;EACA;EACA;EACA;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF;EACE;;AAGF;EACE;EACA;;;ACnCJ;EACE;;AAGF;AAAA;AAAA;AAAA;AAAA;EAKE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGE;EACE;AAAA;AAAA;AAAA;AAAA;IACE;IACA,YACE;;;AAMR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA,YACE;;AAIJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGE;;AAGF;AAAA;AAAA;AAAA;AAAA;EACE;;AASE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGE;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACE;EACA;;;AC7DV;AAAA;AAAA;EAGE;EACA;;AAEA;AAAA;AAAA;EACE;EACA;;AAGF;AAAA;AAAA;EACE;EACA;EACA;EACA;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEE;EACA;EACA;;AAIK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAGP;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACE;EACA;;;AAKF;AAAA;AAAA;EACE;;;AAMJ;EACE;;;AChDF;EACE;EACA;;;ACFF;AAAA;AAAA;EAGE;EACA;;AAEA;AAAA;AAAA;EACE;EACA;;;ACRJ;EACE;;;ACDF;EACE;;;ACEJ;EAJA;;AAoBI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EApBJ;;AA0BA;EACE;EACA;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF;EACE;EACA;EACA%22,%22file%22:%22styles.css%22,%22sourcesContent%22:%5B%22.obsidian-dev-utils%20%7B%5Cn%20%20&.code-highlighter-component%20%7B%5Cn%20%20%20%20textarea,%20pre,%20code%20%7B%5Cn%20%20%20%20%20%20font-family:%20var(--font-monospace);%5Cn%20%20%20%20%20%20line-height:%20var(--line-height-normal);%5Cn%20%20%20%20%20%20margin:%200;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20textarea,%20code%20%7B%5Cn%20%20%20%20%20%20font-size:%20var(--code-size);%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20textarea%20%7B%5Cn%20%20%20%20%20%20background:%20transparent;%5Cn%20%20%20%20%20%20color:%20transparent;%5Cn%20%20%20%20%20%20z-index:%202;%5Cn%20%20%20%20%20%20width:%2020em;%5Cn%20%20%20%20%20%20height:%2010em;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20pre%20%7B%5Cn%20%20%20%20%20%20position:%20absolute;%5Cn%20%20%20%20%20%20pointer-events:%20none;%5Cn%20%20%20%20%20%20border:%20var(--input-border-width)%20solid%20transparent;%5Cn%20%20%20%20%20%20overflow:%20auto;%5Cn%20%20%20%20%20%20inset:%200;%5Cn%20%20%20%20%20%20padding:%20var(--size-4-1)%20var(--size-4-2);%5Cn%20%20%20%20%20%20z-index:%201;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20pre.is-placeholder%20%7B%5Cn%20%20%20%20%20%20opacity:%200.6;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20code%20%7B%5Cn%20%20%20%20%20%20display:%20block;%5Cn%20%20%20%20%20%20padding:%200;%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cn%20%20input%5Btype='url'%5D%20%7B%5Cn%20%20%20%20height:%20var(--input-height)%5Cn%20%20%7D%5Cn%5Cn%20%20input%5Btype='month'%5D,%5Cn%20%20input%5Btype='tel'%5D,%5Cn%20%20input%5Btype='time'%5D,%5Cn%20%20input%5Btype='url'%5D,%5Cn%20%20input%5Btype='week'%5D%20%7B%5Cn%20%20%20%20-webkit-app-region:%20no-drag;%5Cn%20%20%20%20background:%20var(--background-modifier-form-field);%5Cn%20%20%20%20border:%20var(--input-border-width)%20solid%20var(--background-modifier-border);%5Cn%20%20%20%20color:%20var(--text-normal);%5Cn%20%20%20%20font-family:%20inherit;%5Cn%20%20%20%20padding:%20var(--size-4-1)%20var(--size-4-2);%5Cn%20%20%20%20font-size:%20var(--font-ui-small);%5Cn%20%20%20%20border-radius:%20var(--input-radius);%5Cn%20%20%20%20outline:%20none;%5Cn%5Cn%20%20%20%20@at-root%20%7B%5Cn%20%20%20%20%20%20@media%20(hover:%20hover)%20%7B%5Cn%20%20%20%20%20%20%20%20&:hover%20%7B%5Cn%20%20%20%20%20%20%20%20%20%20border-color:%20var(--background-modifier-border-hover);%5Cn%20%20%20%20%20%20%20%20%20%20transition:%5Cn%20%20%20%20%20%20%20%20%20%20%20%20box-shadow%200.15s%20ease-in-out,%5Cn%20%20%20%20%20%20%20%20%20%20%20%20border%200.15s%20ease-in-out;%5Cn%20%20%20%20%20%20%20%20%7D%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20&:active,%5Cn%20%20%20%20&:focus%20%7B%5Cn%20%20%20%20%20%20border-color:%20var(--background-modifier-border-focus);%5Cn%20%20%20%20%20%20transition:%5Cn%20%20%20%20%20%20%20%20box-shadow%200.15s%20ease-in-out,%5Cn%20%20%20%20%20%20%20%20border%200.15s%20ease-in-out;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20&:active,%5Cn%20%20%20%20&:focus,%5Cn%20%20%20%20&:focus-visible%20%7B%5Cn%20%20%20%20%20%20box-shadow:%200%200%200%202px%20var(--background-modifier-border-focus);%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20&::placeholder%20%7B%5Cn%20%20%20%20%20%20color:%20var(--text-faint);%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%5Cn%20%20@at-root%20%7B%5Cn%20%20%20%20.mod-rtl,%5Cn%20%20%20%20.is-rtl,%5Cn%20%20%20%20.rtl%20%7B%5Cn%20%20%20%20%20%20&%20%7B%5Cn%20%20%20%20%20%20%20%20input%5Btype='month'%5D,%5Cn%20%20%20%20%20%20%20%20input%5Btype='time'%5D,%5Cn%20%20%20%20%20%20%20%20input%5Btype='week'%5D%20%7B%5Cn%20%20%20%20%20%20%20%20%20%20direction:%20rtl;%5Cn%5Cn%20%20%20%20%20%20%20%20%20%20&::-webkit-calendar-picker-indicator%20%7B%5Cn%20%20%20%20%20%20%20%20%20%20%20%20right:%20var(--size-4-1);%5Cn%20%20%20%20%20%20%20%20%20%20%20%20left:%20auto;%5Cn%20%20%20%20%20%20%20%20%20%20%7D%5Cn%20%20%20%20%20%20%20%20%7D%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cn%20%20input%5Btype='month'%5D,%5Cn%20%20input%5Btype='time'%5D,%5Cn%20%20input%5Btype='week'%5D%20%7B%5Cn%20%20%20%20font-variant-numeric:%20tabular-nums;%5Cn%20%20%20%20position:%20relative;%5Cn%5Cn%20%20%20%20&::-webkit-datetime-edit-text%20%7B%5Cn%20%20%20%20%20%20color:%20var(--text-faint);%5Cn%20%20%20%20%20%20padding-inline-end:%200;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20&::-webkit-calendar-picker-indicator%20%7B%5Cn%20%20%20%20%20%20position:%20absolute;%5Cn%20%20%20%20%20%20left:%20var(--size-4-1);%5Cn%20%20%20%20%20%20right:%20auto;%5Cn%20%20%20%20%20%20opacity:%200.5;%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20&::-webkit-datetime-edit-month-field,%5Cn%20%20%20%20&::-webkit-datetime-edit-day-field,%5Cn%20%20%20%20&::-webkit-datetime-edit-year-field%20%7B%5Cn%20%20%20%20%20%20&:active,%5Cn%20%20%20%20%20%20&:focus%20%7B%5Cn%20%20%20%20%20%20%20%20background-color:%20var(--text-selection);%5Cn%20%20%20%20%20%20%20%20color:%20var(--text-normal);%5Cn%20%20%20%20%20%20%20%20cursor:%20text;%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20@at-root%20.mod-rtl%20&,%5Cn%20%20%20%20%20%20.is-rtl%20&,%5Cn%20%20%20%20%20%20.rtl%20&%20%7B%5Cn%20%20%20%20%20%20direction:%20rtl;%5Cn%5Cn%20%20%20%20%20%20&::-webkit-calendar-picker-indicator%20%7B%5Cn%20%20%20%20%20%20%20%20left:%20auto;%5Cn%20%20%20%20%20%20%20%20right:%20var(--size-4-1);%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%5Cn%20%20%20%20@at-root%20%7B%5Cn%20%20%20%20%20%20body:not(.is-ios):not(.is-android)%20&%20%7B%5Cn%20%20%20%20%20%20%20%20padding-inline-start:%20var(--size-4-6);%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%5Cn%20%20input%5Btype='time'%5D%20%7B%5Cn%20%20%20%20&::-webkit-calendar-picker-indicator%20%7B%5Cn%20%20%20%20%20%20margin-inline-start:%200;%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cn%20%20&.modal-container%20%7B%5Cn%20%20%20%20.ok-button%20%7B%5Cn%20%20%20%20%20%20margin-right:%2010px;%5Cn%20%20%20%20%20%20margin-top:%2020px;%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cn%20%20.multiple-dropdown-component%20%7B%5Cn%20%20%20%20select,%5Cn%20%20%20%20select:focus,%5Cn%20%20%20%20.dropdown%20%7B%5Cn%20%20%20%20%20%20height:%20auto;%5Cn%20%20%20%20%20%20padding-top:%203px;%5Cn%5Cn%20%20%20%20%20%20option:checked%20%7B%5Cn%20%20%20%20%20%20%20%20background-color:%20%231967d2;%5Cn%20%20%20%20%20%20%20%20color:%20%23fff;%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cn%20%20&.prompt-modal%20%7B%5Cn%20%20%20%20.text-box%20%7B%5Cn%20%20%20%20%20%20width:%20100%25;%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%7D%5Cn%22,%22.obsidian-dev-utils%20%7B%5Cr%5Cn%20%20&.tri-state-checkbox-component%20%7B%5Cr%5Cn%20%20%20%20input%5Btype='checkbox'%5D:indeterminate%20%7B%5Cr%5Cn%20%20%20%20%20%20appearance:%20checkbox;%5Cr%5Cn%20%20%20%20%7D%5Cr%5Cn%20%20%7D%5Cr%5Cn%7D%5Cr%5Cn%22,%22@mixin%20invalid%20%7B%5Cn%20%20box-shadow:%200%200%200%202px%20var(--text-error);%5Cn%7D%5Cn%5Cn.obsidian-dev-utils%20%7B%5Cn%20%20:invalid%20%7B%5Cn%20%20%20%20@include%20invalid;%5Cn%20%20%7D%5Cn%5Cn%20%20input.metadata-input-text,%5Cn%20%20input%5Btype='date'%5D,%5Cn%20%20input%5Btype='datetime-local'%5D,%5Cn%20%20input%5Btype='email'%5D,%5Cn%20%20input%5Btype='number'%5D,%5Cn%20%20input%5Btype='password'%5D,%5Cn%20%20input%5Btype='search'%5D,%5Cn%20%20input%5Btype='text'%5D,%5Cn%20%20textarea%20%7B%5Cn%20%20%20%20&:active,%5Cn%20%20%20%20&:focus-visible,%5Cn%20%20%20%20&:focus%20%7B%5Cn%20%20%20%20%20%20&:invalid%20%7B%5Cn%20%20%20%20%20%20%20%20@include%20invalid;%5Cn%20%20%20%20%20%20%7D%5Cn%20%20%20%20%7D%5Cn%20%20%7D%5Cn%5Cn%20%20&.setting-component-wrapper%20%7B%5Cn%20%20%20%20position:%20relative;%5Cn%20%20%20%20display:%20inline-flex;%5Cn%20%20%7D%5Cn%5Cn%20%20&.overlay-validator%20%7B%5Cn%20%20%20%20caret-color:%20transparent;%5Cn%20%20%20%20cursor:%20default;%5Cn%20%20%20%20position:%20absolute;%5Cn%20%20%20%20background-color:%20transparent;%5Cn%20%20%20%20border:%20none;%5Cn%20%20%20%20outline:%20none;%5Cn%20%20%20%20pointer-events:%20none;%5Cn%20%20%20%20z-index:%209999;%5Cn%20%20%20%20left:%200;%5Cn%20%20%20%20top:%200;%5Cn%20%20%20%20width:%20100%25;%5Cn%20%20%20%20height:%20100%25;%5Cn%20%20%7D%5Cn%5Cn%20%20&.tooltip.tooltip-validator%20%7B%5Cn%20%20%20%20position:%20absolute;%5Cn%20%20%20%20top:%20calc(100%25%20+%208px);%5Cn%20%20%20%20width:%20max-content;%5Cn%20%20%7D%5Cn%7D%5Cn%22%5D%7D */
`;(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function va(t,e){let n=0;for(let r=0;r<t.length;r++){if(!(r in t))continue;let s=t[r];e(s,r,t)&&(t[n++]=s)}t.length=n}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Pt=class{eventRefsMap=new Map;off(e,n){let r=this.eventRefsMap.get(e);r&&(va(r,s=>s.callback!==n),r.length===0&&this.eventRefsMap.delete(e))}offref(e){let n=this.eventRefsMap.get(e.name);n&&(va(n,r=>r!==e),n.length===0&&this.eventRefsMap.delete(e.name))}on(e,n,r){let s=this.eventRefsMap.get(e);s||(s=[],this.eventRefsMap.set(e,s));let o={asyncEvents:this,callback:n,name:e,thisArg:r};return s.push(o),o}once(e,n,r){let s=this.on(e,n,r),o=this.on(e,()=>{this.offref(s),this.offref(o)});return s}trigger(e,...n){let r=this.eventRefsMap.get(e)??[];for(let s of r.slice())this.tryTrigger(s,n)}async triggerAsync(e,...n){let r=this.eventRefsMap.get(e)??[];for(let s of r.slice())await this.tryTriggerAsync(s,n)}tryTrigger(e,n){try{e.callback.apply(e.thisArg,n)}catch(r){setTimeout(()=>{throw r},0)}}async tryTriggerAsync(e,n){try{await e.callback.call(e.thisArg,...n)}catch(r){setTimeout(()=>{throw r},0)}}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Sa="asyncError",Ca=`Error stack:
`,qi="An unhandled error occurred executing async operation",Hi=new Pt;Hi.on(Sa,eb);function Gi(t){Hi.trigger(Sa,t)}function Dc(t){return Ta(t).map(e=>"  ".repeat(e.level)+(e.message.startsWith(Ca)?e.message.slice(Ca.length):e.message)).join(`
`)}function _n(t=0){return(new Error().stack??"").split(`
`).slice(t+2).join(`
`)}function Yr(t,e){e??=globalThis.console;let n=Ta(t);for(let r of n)r.shouldClearAnsiSequence?e.error(`\x1B[0m${r.message}\x1B[0m`):e.error(r.message)}function Oc(t){let e=Hi.on(Sa,t);return()=>{Hi.offref(e)}}function Kt(t){throw t}function eb(t){Yr(t)}function Ta(t,e=0,n=[]){if(t===void 0)return n;if(!(t instanceof Error)){let s;return t===null?s="(null)":typeof t=="string"?s=t:s=JSON.stringify(t)??"undefined",n.push({level:e,message:s}),n}let r=`${t.name}: ${t.message}`;if(n.push({level:e,message:r,shouldClearAnsiSequence:!0}),t.stack){let s=t.stack.startsWith(r)?t.stack.slice(r.length+1):t.stack;n.push({level:e,message:`${Ca}${s}`})}return t.cause!==void 0&&(n.push({level:e,message:"Caused by:"}),Ta(t.cause,e+1,n)),n}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function St(){}async function Tt(){}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var _a=class{constructor(e){this.value=e}};function tb(){try{return globalThis.require("obsidian/app")}catch{return globalThis.app??Kt(new Error("Obsidian app not found"))}}function Xt(t,e,n){let s=t??nb()??globalThis;return s.obsidianDevUtilsState??={},s.obsidianDevUtilsState[e]??=new _a(n)}function nb(){return typeof window>"u"?null:tb()}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Fa="__no-plugin-id-initialized__",Lc=Fa;function ji(){return Lc}function Mc(t){t&&(Lc=t)}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Nc=",",Ki="-";function Bc(){return{disable:rb,enable:ib,get:Zi,set:Vr}}function Xi(t,e=0){let n=`${t}:${e.toString()}`,r=Xt(null,"debuggers",new Map).value,s=r.get(n);return s||(s=nr()(t),s.log=(o,...l)=>{sb(t,e,o,...l)},s.printStackTrace=(o,l)=>{Vc(t,o,l)},r.set(n,s)),s}function dt(t){let e=ji(),n=e===Fa?"":`${e}:`;return Xi(`${n}${Ui}:${t}`)}function $c(t){let e=nr().enabled(t),n=e?"enabled":"disabled",r=e?"disable":"enable",s=Zi();Vr(t),Xi(t)(`Debug messages for plugin ${t} are ${n}. See https://github.com/mnaoumov/obsidian-dev-utils/blob/main/docs/debugging.md how to ${r} them.`),Vr(s)}function rb(t){let e=new Set(Zi());for(let n of zr(t)){if(n.startsWith(Ki))continue;let r=Ki+n;e.has(n)&&e.delete(n),e.add(r)}Vr(Array.from(e))}function ib(t){let e=new Set(Zi());for(let n of zr(t)){if(!n.startsWith(Ki)){let r=Ki+n;e.has(r)&&e.delete(r)}e.add(n)}Vr(Array.from(e))}function Zi(){return zr(nr().load()??"")}function nr(){return typeof window>"u"?Pa.default:Xt(null,"debug",Pa.default).value}function Yc(){return typeof window<"u"}function sb(t,e,n,...r){if(!nr().enabled(t))return;let l=(new Error().stack?.split(`
`)??[])[4+e]??"";console.debug(n,...r),Yc()&&Vc(t,l,"Debug message caller")}function Vc(t,e,n){let r=nr()(t);if(!r.enabled)return;e||(e="(unavailable)"),(n??"")||(n="Caller stack trace"),r(n);let s=Yc()?`StackTraceFakeError
`:"";console.debug(`${s}${e}`)}function Vr(t){nr().enable(zr(t).join(Nc))}function zr(t){return typeof t=="string"?t.split(Nc).filter(Boolean):t.flatMap(zr)}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function Ia(t){let e=new Error(qi);try{await t()}catch(n){e.cause=n,Gi(e)}}function st(t){return(...e)=>{et(()=>t(...e))}}function et(t){Ia(t)}function zc(t,e=0){setTimeout(st(async()=>await t()),e)}async function Wc(){return new Promise(t=>{requestAnimationFrame(()=>{t()})})}async function Ji(t,e,n){let r=dt("Async:retryWithTimeout");n??=_n(1);let o={...{retryDelayInMilliseconds:100,shouldRetryOnError:!1,timeoutInMilliseconds:5e3},...e};await Ra(o.timeoutInMilliseconds,async()=>{let l=0;for(;;){o.abortSignal?.throwIfAborted(),l++;let u;try{u=await t()}catch(d){if(!o.shouldRetryOnError||d.__terminateRetry)throw d;Yr(d),u=!1}if(u){l>1&&(r(`Retry completed successfully after ${l.toString()} attempts`),r.printStackTrace(n));return}r(`Retry attempt ${l.toString()} completed unsuccessfully. Trying again in ${o.retryDelayInMilliseconds.toString()} milliseconds`,{fn:t}),r.printStackTrace(n),await Uc(o.retryDelayInMilliseconds)}},{retryFn:t})}async function Ra(t,e,n){let r=!0,s=null,o=performance.now();if(await Promise.race([l(),u()]),r)throw new Error("Timed out");return s;async function l(){try{s=await e();let d=performance.now()-o;dt("Async:runWithTimeout")(`Execution time: ${d.toString()} milliseconds`,{context:n,fn:e})}finally{r=!1}}async function u(){if(!r||(await Uc(t),!r))return;let d=performance.now()-o;console.warn(`Timed out in ${d.toString()} milliseconds`,{context:n,fn:e});let f=dt("Async:runWithTimeout:timeout");f.enabled&&(f(`The execution is not terminated because debugger ${f.namespace} is enabled. See https://github.com/mnaoumov/obsidian-dev-utils/blob/main/docs/debugging.md for more information`),await u())}}async function Uc(t){await new Promise(e=>{setTimeout(e,t)})}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function ob(t){return await new Promise(e=>{let n=new FileReader;n.addEventListener("loadend",r),n.readAsDataURL(t);function r(){e(n.result)}})}async function Hc(t,e){let n=await ob(t);return new Promise(r=>{let s=new Image;s.addEventListener("load",o),s.src=n;function o(){let l=document.createElement("canvas"),u=l.getContext("2d");if(!u)throw new Error("Could not get 2D context.");let d=s.width,f=s.height;l.width=d,l.height=f,u.fillStyle="#fff",u.fillRect(0,0,d,f),u.save();let m=.5;u.translate(d*m,f*m),u.drawImage(s,0,0,d,f,-d*m,-f*m,d,f),u.restore();let p=l.toDataURL("image/jpeg",e),y=ab(p);r(y)}})}function ab(t){let n=t.split(";base64,")[1];if(!n)throw new Error("Invalid data URL");let r=window.atob(n),s=r.length,o=new Uint8Array(s);for(let l=0;l<s;l++)o[l]=r.charCodeAt(l);return o.buffer}var lb=require("obsidian"),Gc=require("obsidian"),jc=require("obsidian"),A2=require("obsidian"),E2=require("obsidian");var Zt={AudioRecorder:"audio-recorder",Backlink:"backlink",Bases:"bases",Bookmarks:"bookmarks",Canvas:"canvas",CommandPalette:"command-palette",DailyNotes:"daily-notes",EditorStatus:"editor-status",FileExplorer:"file-explorer",FileRecovery:"file-recovery",Footnotes:"footnotes",GlobalSearch:"global-search",Graph:"graph",MarkdownImporter:"markdown-importer",NoteComposer:"note-composer",OutgoingLink:"outgoing-link",Outline:"outline",PagePreview:"page-preview",Properties:"properties",Publish:"publish",RandomNote:"random-note",SlashCommand:"slash-command",Slides:"slides",Switcher:"switcher",Sync:"sync",TagPane:"tag-pane",Templates:"templates",Webviewer:"webviewer",WordCount:"word-count",Workspaces:"workspaces",ZkPrefixer:"zk-prefixer"},qc={AllProperties:"all-properties",Audio:"audio",Backlink:Zt.Backlink,Bookmarks:Zt.Bookmarks,Canvas:Zt.Canvas,Empty:"empty",FileExplorer:Zt.FileExplorer,FileProperties:"file-properties",Graph:Zt.Graph,Image:"image",LocalGraph:"localgraph",Markdown:"markdown",OutgoingLink:Zt.OutgoingLink,Outline:Zt.Outline,Pdf:"pdf",ReleaseNotes:"release-notes",Search:"search",Sync:"sync",Table:"table",Tag:"tag",Video:"video",Webviewer:"webviewer",WebviewerHistory:"webviewer-history"};function ub(){return Gc.TFile}function cb(){return jc.TFolder}function ft(t){return t.replace(/\/?[^\/]*$/,"")||"/"}function Qi(t,e){let n=t.vault.getFolderByPath(e);return n||(n=new(cb())(t.vault,e),n.parent=Qi(t,ft(e)),n.deleted=!0,n)}function Kc(t,e){let n=t.vault.getFileByPath(e);return n||(n=new(ub())(t.vault,e),n.parent=Qi(t,ft(e)),n.deleted=!0,n)}function dn(t){return!!t.position}function It(t){return!!t.key}var Da=Ne(Jc(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function es(t){return t.replaceAll(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Qc(t){try{return new RegExp(t),!0}catch{return!1}}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function Fn(t,...e){return fb(t)?await t(...e):t}function fb(t){return typeof t=="function"}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var hb={"\n":"\\n","\r":"\\r","	":"\\t","\b":"\\b","\f":"\\f","'":"\\'",'"':'\\"',"\\":"\\\\"},mb={};for(let[t,e]of Object.entries(hb))mb[e]=t;function ed(t,e,n,r){return r??=n,t.slice(0,n)+e+t.slice(r)}function Wr(t){return be(t,/\u00A0|\u202F/g," ").normalize("NFC")}function be(t,e,n){return typeof n>"u"?t:(e instanceof RegExp&&!e.global&&(e=new RegExp(e.source,`${e.flags}g`)),typeof n=="string"?t.replaceAll(e,n):t.replaceAll(e,(r,...s)=>{let l=typeof s.at(-1)=="object",u=l?s.length-2:s.length-1,d={groups:l?s.at(-1):void 0,missingGroupIndices:[],offset:s.at(u-1),source:s.at(u),substring:r},f=s.slice(0,u-1).map((m,p)=>{if(typeof m=="string")return m;if(typeof m>"u")return d.missingGroupIndices.push(p),"";throw new Error(`Unexpected argument type: ${typeof m}`)});return n(d,...f)??d.substring}))}async function td(t,e,n){if(typeof n=="string")return be(t,e,n);let r=[];be(t,e,(o,...l)=>(r.push(Fn(n,o,...l)),""));let s=await Promise.all(r);return be(t,e,o=>s.shift()??o.substring)}function ts(t,e,n){if(t.endsWith(e))return t.slice(0,-e.length);if(n)throw new Error(`String ${t} does not end with suffix ${e}`);return t}function ir(t,e,n){if(t.startsWith(e))return t.slice(e.length);if(n)throw new Error(`String ${t} does not start with prefix ${e}`);return t}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var pb=/[a-zA-Z]:\/[^:]*$/,Dt=Da.default.posix,O2=Dt.delimiter,L2=Da.default.posix.sep,ke=Dt.basename,Ee=Dt.dirname,Fe=Dt.extname,M2=Dt.format;var ve=Dt.join,N2=Dt.normalize,B2=Dt.parse,sr=Dt.relative;function fn(t,e){return e?`${t}.${e}`:t}function nd(...t){let e=Dt.resolve(...t);return e=gb(e),pb.exec(e)?.[0]??e}function gb(t){return be(t,"\\","/")}var Jt=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var wb="base",yb="canvas",id="md";function Oa(t,e,n){if(ht(e))return e.extension===n;if(typeof e=="string"){let r=tt(t,e);return r?r.extension===n:Fe(e).slice(1)===n}return!1}function Pn(t,e,n){if(e===null)return null;if(sd(e))return e;let r=rd(t,e,n);if(r)return r;let s=ad(e);return s===e?null:rd(t,s,n)}function Be(t,e,n,r){let s=tt(t,e,r);if(!s)if(n)s=Kc(t,e);else throw new Error(`File not found: ${e}`);return s}function tt(t,e,n){let r=Pn(t,e,n);return ht(r)?r:null}function Ur(t,e,n,r){let s=Ot(t,e,r);if(!s)if(n)s=Qi(t,e);else throw new Error(`Folder not found: ${e}`);return s}function Ot(t,e,n){let r=Pn(t,e,n);return La(r)?r:null}function we(t,e){if(sd(e))return e.path;let n=Pn(t,e);return n?n.path:ad(e)}function sd(t){return t instanceof Jt.TAbstractFile}function bb(t,e){return Oa(t,e,wb)}function Qt(t,e){return Oa(t,e,yb)}function ht(t){return t instanceof Jt.TFile}function La(t){return t instanceof Jt.TFolder}function en(t,e){return Oa(t,e,id)}function Hr(t,e){return en(t,e)||Qt(t,e)||bb(t,e)}function od(t,e){return en(t,e)?ts(e.path,`.${id}`):e.path}function rd(t,e,n){return n?t.vault.getAbstractFileByPathInsensitive(e):t.vault.getAbstractFileByPath(e)}function ad(t){return(0,Jt.normalizePath)(nd("/",t))}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function kb(t,e,n){let r=we(t,e),s=we(t,n),o=Be(t,s,!0),l=Fe(r),u=ke(r,l),d=t.vault.getAvailablePathForAttachments;return d.isExtended?d(u,l.slice(1),o,!0):await Ma(t,u,l.slice(1),o,!0)}async function or(t,e){return ft(await kb(t,"DUMMY_FILE.pdf",e))}async function Ma(t,e,n,r,s){let o=t.vault.getConfig("attachmentFolderPath"),l=o==="."||o==="./",u=null;o.startsWith("./")&&(u=ir(o,"./")),l?o=r?r.parent?.path??"":"":u&&(o=(r?r.parent?.getParentPrefix()??"":"")+u),o=Wr(ld(o)),e=Wr(ld(e));let d=Ot(t,o,!0);!d&&u&&(s?d=Ur(t,o,!0):d=await t.vault.createFolder(o));let f=d?.getParentPrefix()??"";return t.vault.getAvailablePath(f+e,n)}async function Na(t,e){let n=await or(t,e),r=await or(t,ve(Ee(e),"DUMMY_FILE.md"));return n!==r}function ld(t){return t=be(t,/(?:[\\/])+/g,"/"),t=be(t,/^\/+|\/+$/g,""),t||"/"}var Ds=require("obsidian");var xb={};function In(t,e){let n=e||xb,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,s=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return cd(t,r,s)}function cd(t,e,n){if(Ab(t)){if("value"in t)return t.type==="html"&&!n?"":t.value;if(e&&"alt"in t&&t.alt)return t.alt;if("children"in t)return ud(t.children,e,n)}return Array.isArray(t)?ud(t,e,n):""}function ud(t,e,n){let r=[],s=-1;for(;++s<t.length;)r[s]=cd(t[s],e,n);return r.join("")}function Ab(t){return!!(t&&typeof t=="object")}var dd=document.createElement("i");function ar(t){let e="&"+t+";";dd.innerHTML=e;let n=dd.textContent;return n.charCodeAt(n.length-1)===59&&t!=="semi"||n===e?!1:n}function Re(t,e,n,r){let s=t.length,o=0,l;if(e<0?e=-e>s?0:s+e:e=e>s?s:e,n=n>0?n:0,r.length<1e4)l=Array.from(r),l.unshift(e,n),t.splice(...l);else for(n&&t.splice(e,n);o<r.length;)l=r.slice(o,o+1e4),l.unshift(e,0),t.splice(...l),o+=1e4,e+=1e4}function Ge(t,e){return t.length>0?(Re(t,t.length,0,e),t):e}var fd={}.hasOwnProperty;function hd(t){let e={},n=-1;for(;++n<t.length;)Eb(e,t[n]);return e}function Eb(t,e){let n;for(n in e){let s=(fd.call(t,n)?t[n]:void 0)||(t[n]={}),o=e[n],l;if(o)for(l in o){fd.call(s,l)||(s[l]=[]);let u=o[l];vb(s[l],Array.isArray(u)?u:u?[u]:[])}}}function vb(t,e){let n=-1,r=[];for(;++n<e.length;)(e[n].add==="after"?t:r).push(e[n]);Re(t,0,0,r)}function ns(t,e){let n=Number.parseInt(t,e);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"\uFFFD":String.fromCodePoint(n)}function tn(t){return t.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var ot=hn(/[A-Za-z]/),je=hn(/[\dA-Za-z]/),md=hn(/[#-'*+\--9=?A-Z^-~]/);function qr(t){return t!==null&&(t<32||t===127)}var Gr=hn(/\d/),pd=hn(/[\dA-Fa-f]/),gd=hn(/[!-/:-@[-`{-~]/);function $(t){return t!==null&&t<-2}function Ae(t){return t!==null&&(t<0||t===32)}function G(t){return t===-2||t===-1||t===32}var wd=hn(/\p{P}|\p{S}/u),yd=hn(/\s/);function hn(t){return e;function e(n){return n!==null&&n>-1&&t.test(String.fromCharCode(n))}}function j(t,e,n,r){let s=r?r-1:Number.POSITIVE_INFINITY,o=0;return l;function l(d){return G(d)?(t.enter(n),u(d)):e(d)}function u(d){return G(d)&&o++<s?(t.consume(d),u):(t.exit(n),e(d))}}var bd={tokenize:Cb};function Cb(t){let e=t.attempt(this.parser.constructs.contentInitial,r,s),n;return e;function r(u){if(u===null){t.consume(u);return}return t.enter("lineEnding"),t.consume(u),t.exit("lineEnding"),j(t,e,"linePrefix")}function s(u){return t.enter("paragraph"),o(u)}function o(u){let d=t.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=d),n=d,l(u)}function l(u){if(u===null){t.exit("chunkText"),t.exit("paragraph"),t.consume(u);return}return $(u)?(t.consume(u),t.exit("chunkText"),o):(t.consume(u),l)}}var xd={tokenize:Sb},kd={tokenize:Tb};function Sb(t){let e=this,n=[],r=0,s,o,l;return u;function u(F){if(r<n.length){let z=n[r];return e.containerState=z[1],t.attempt(z[0].continuation,d,f)(F)}return f(F)}function d(F){if(r++,e.containerState._closeFlow){e.containerState._closeFlow=void 0,s&&_();let z=e.events.length,W=z,C;for(;W--;)if(e.events[W][0]==="exit"&&e.events[W][1].type==="chunkFlow"){C=e.events[W][1].end;break}E(r);let te=z;for(;te<e.events.length;)e.events[te][1].end={...C},te++;return Re(e.events,W+1,0,e.events.slice(z)),e.events.length=te,f(F)}return u(F)}function f(F){if(r===n.length){if(!s)return y(F);if(s.currentConstruct&&s.currentConstruct.concrete)return A(F);e.interrupt=!!(s.currentConstruct&&!s._gfmTableDynamicInterruptHack)}return e.containerState={},t.check(kd,m,p)(F)}function m(F){return s&&_(),E(r),y(F)}function p(F){return e.parser.lazy[e.now().line]=r!==n.length,l=e.now().offset,A(F)}function y(F){return e.containerState={},t.attempt(kd,g,A)(F)}function g(F){return r++,n.push([e.currentConstruct,e.containerState]),y(F)}function A(F){if(F===null){s&&_(),E(0),t.consume(F);return}return s=s||e.parser.flow(e.now()),t.enter("chunkFlow",{_tokenizer:s,contentType:"flow",previous:o}),P(F)}function P(F){if(F===null){S(t.exit("chunkFlow"),!0),E(0),t.consume(F);return}return $(F)?(t.consume(F),S(t.exit("chunkFlow")),r=0,e.interrupt=void 0,u):(t.consume(F),P)}function S(F,z){let W=e.sliceStream(F);if(z&&W.push(null),F.previous=o,o&&(o.next=F),o=F,s.defineSkip(F.start),s.write(W),e.parser.lazy[F.start.line]){let C=s.events.length;for(;C--;)if(s.events[C][1].start.offset<l&&(!s.events[C][1].end||s.events[C][1].end.offset>l))return;let te=e.events.length,X=te,Z,oe;for(;X--;)if(e.events[X][0]==="exit"&&e.events[X][1].type==="chunkFlow"){if(Z){oe=e.events[X][1].end;break}Z=!0}for(E(r),C=te;C<e.events.length;)e.events[C][1].end={...oe},C++;Re(e.events,X+1,0,e.events.slice(te)),e.events.length=C}}function E(F){let z=n.length;for(;z-- >F;){let W=n[z];e.containerState=W[1],W[0].exit.call(e,t)}n.length=F}function _(){s.write([null]),o=void 0,s=void 0,e.containerState._closeFlow=void 0}}function Tb(t,e,n){return j(t,t.attempt(this.parser.constructs.document,e,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function lr(t){if(t===null||Ae(t)||yd(t))return 1;if(wd(t))return 2}function ur(t,e,n){let r=[],s=-1;for(;++s<t.length;){let o=t[s].resolveAll;o&&!r.includes(o)&&(e=o(e,n),r.push(o))}return e}var jr={name:"attention",resolveAll:_b,tokenize:Fb};function _b(t,e){let n=-1,r,s,o,l,u,d,f,m;for(;++n<t.length;)if(t[n][0]==="enter"&&t[n][1].type==="attentionSequence"&&t[n][1]._close){for(r=n;r--;)if(t[r][0]==="exit"&&t[r][1].type==="attentionSequence"&&t[r][1]._open&&e.sliceSerialize(t[r][1]).charCodeAt(0)===e.sliceSerialize(t[n][1]).charCodeAt(0)){if((t[r][1]._close||t[n][1]._open)&&(t[n][1].end.offset-t[n][1].start.offset)%3&&!((t[r][1].end.offset-t[r][1].start.offset+t[n][1].end.offset-t[n][1].start.offset)%3))continue;d=t[r][1].end.offset-t[r][1].start.offset>1&&t[n][1].end.offset-t[n][1].start.offset>1?2:1;let p={...t[r][1].end},y={...t[n][1].start};Ad(p,-d),Ad(y,d),l={type:d>1?"strongSequence":"emphasisSequence",start:p,end:{...t[r][1].end}},u={type:d>1?"strongSequence":"emphasisSequence",start:{...t[n][1].start},end:y},o={type:d>1?"strongText":"emphasisText",start:{...t[r][1].end},end:{...t[n][1].start}},s={type:d>1?"strong":"emphasis",start:{...l.start},end:{...u.end}},t[r][1].end={...l.start},t[n][1].start={...u.end},f=[],t[r][1].end.offset-t[r][1].start.offset&&(f=Ge(f,[["enter",t[r][1],e],["exit",t[r][1],e]])),f=Ge(f,[["enter",s,e],["enter",l,e],["exit",l,e],["enter",o,e]]),f=Ge(f,ur(e.parser.constructs.insideSpan.null,t.slice(r+1,n),e)),f=Ge(f,[["exit",o,e],["enter",u,e],["exit",u,e],["exit",s,e]]),t[n][1].end.offset-t[n][1].start.offset?(m=2,f=Ge(f,[["enter",t[n][1],e],["exit",t[n][1],e]])):m=0,Re(t,r-1,n-r+3,f),n=r+f.length-m-2;break}}for(n=-1;++n<t.length;)t[n][1].type==="attentionSequence"&&(t[n][1].type="data");return t}function Fb(t,e){let n=this.parser.constructs.attentionMarkers.null,r=this.previous,s=lr(r),o;return l;function l(d){return o=d,t.enter("attentionSequence"),u(d)}function u(d){if(d===o)return t.consume(d),u;let f=t.exit("attentionSequence"),m=lr(d),p=!m||m===2&&s||n.includes(d),y=!s||s===2&&m||n.includes(r);return f._open=!!(o===42?p:p&&(s||!y)),f._close=!!(o===42?y:y&&(m||!p)),e(d)}}function Ad(t,e){t.column+=e,t.offset+=e,t._bufferIndex+=e}var Ba={name:"autolink",tokenize:Pb};function Pb(t,e,n){let r=0;return s;function s(g){return t.enter("autolink"),t.enter("autolinkMarker"),t.consume(g),t.exit("autolinkMarker"),t.enter("autolinkProtocol"),o}function o(g){return ot(g)?(t.consume(g),l):g===64?n(g):f(g)}function l(g){return g===43||g===45||g===46||je(g)?(r=1,u(g)):f(g)}function u(g){return g===58?(t.consume(g),r=0,d):(g===43||g===45||g===46||je(g))&&r++<32?(t.consume(g),u):(r=0,f(g))}function d(g){return g===62?(t.exit("autolinkProtocol"),t.enter("autolinkMarker"),t.consume(g),t.exit("autolinkMarker"),t.exit("autolink"),e):g===null||g===32||g===60||qr(g)?n(g):(t.consume(g),d)}function f(g){return g===64?(t.consume(g),m):md(g)?(t.consume(g),f):n(g)}function m(g){return je(g)?p(g):n(g)}function p(g){return g===46?(t.consume(g),r=0,m):g===62?(t.exit("autolinkProtocol").type="autolinkEmail",t.enter("autolinkMarker"),t.consume(g),t.exit("autolinkMarker"),t.exit("autolink"),e):y(g)}function y(g){if((g===45||je(g))&&r++<63){let A=g===45?y:p;return t.consume(g),A}return n(g)}}var mn={partial:!0,tokenize:Ib};function Ib(t,e,n){return r;function r(o){return G(o)?j(t,s,"linePrefix")(o):s(o)}function s(o){return o===null||$(o)?e(o):n(o)}}var rs={continuation:{tokenize:Db},exit:Ob,name:"blockQuote",tokenize:Rb};function Rb(t,e,n){let r=this;return s;function s(l){if(l===62){let u=r.containerState;return u.open||(t.enter("blockQuote",{_container:!0}),u.open=!0),t.enter("blockQuotePrefix"),t.enter("blockQuoteMarker"),t.consume(l),t.exit("blockQuoteMarker"),o}return n(l)}function o(l){return G(l)?(t.enter("blockQuotePrefixWhitespace"),t.consume(l),t.exit("blockQuotePrefixWhitespace"),t.exit("blockQuotePrefix"),e):(t.exit("blockQuotePrefix"),e(l))}}function Db(t,e,n){let r=this;return s;function s(l){return G(l)?j(t,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(l):o(l)}function o(l){return t.attempt(rs,e,n)(l)}}function Ob(t){t.exit("blockQuote")}var is={name:"characterEscape",tokenize:Lb};function Lb(t,e,n){return r;function r(o){return t.enter("characterEscape"),t.enter("escapeMarker"),t.consume(o),t.exit("escapeMarker"),s}function s(o){return gd(o)?(t.enter("characterEscapeValue"),t.consume(o),t.exit("characterEscapeValue"),t.exit("characterEscape"),e):n(o)}}var ss={name:"characterReference",tokenize:Mb};function Mb(t,e,n){let r=this,s=0,o,l;return u;function u(p){return t.enter("characterReference"),t.enter("characterReferenceMarker"),t.consume(p),t.exit("characterReferenceMarker"),d}function d(p){return p===35?(t.enter("characterReferenceMarkerNumeric"),t.consume(p),t.exit("characterReferenceMarkerNumeric"),f):(t.enter("characterReferenceValue"),o=31,l=je,m(p))}function f(p){return p===88||p===120?(t.enter("characterReferenceMarkerHexadecimal"),t.consume(p),t.exit("characterReferenceMarkerHexadecimal"),t.enter("characterReferenceValue"),o=6,l=pd,m):(t.enter("characterReferenceValue"),o=7,l=Gr,m(p))}function m(p){if(p===59&&s){let y=t.exit("characterReferenceValue");return l===je&&!ar(r.sliceSerialize(y))?n(p):(t.enter("characterReferenceMarker"),t.consume(p),t.exit("characterReferenceMarker"),t.exit("characterReference"),e)}return l(p)&&s++<o?(t.consume(p),m):n(p)}}var Ed={partial:!0,tokenize:Bb},os={concrete:!0,name:"codeFenced",tokenize:Nb};function Nb(t,e,n){let r=this,s={partial:!0,tokenize:W},o=0,l=0,u;return d;function d(C){return f(C)}function f(C){let te=r.events[r.events.length-1];return o=te&&te[1].type==="linePrefix"?te[2].sliceSerialize(te[1],!0).length:0,u=C,t.enter("codeFenced"),t.enter("codeFencedFence"),t.enter("codeFencedFenceSequence"),m(C)}function m(C){return C===u?(l++,t.consume(C),m):l<3?n(C):(t.exit("codeFencedFenceSequence"),G(C)?j(t,p,"whitespace")(C):p(C))}function p(C){return C===null||$(C)?(t.exit("codeFencedFence"),r.interrupt?e(C):t.check(Ed,P,z)(C)):(t.enter("codeFencedFenceInfo"),t.enter("chunkString",{contentType:"string"}),y(C))}function y(C){return C===null||$(C)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),p(C)):G(C)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),j(t,g,"whitespace")(C)):C===96&&C===u?n(C):(t.consume(C),y)}function g(C){return C===null||$(C)?p(C):(t.enter("codeFencedFenceMeta"),t.enter("chunkString",{contentType:"string"}),A(C))}function A(C){return C===null||$(C)?(t.exit("chunkString"),t.exit("codeFencedFenceMeta"),p(C)):C===96&&C===u?n(C):(t.consume(C),A)}function P(C){return t.attempt(s,z,S)(C)}function S(C){return t.enter("lineEnding"),t.consume(C),t.exit("lineEnding"),E}function E(C){return o>0&&G(C)?j(t,_,"linePrefix",o+1)(C):_(C)}function _(C){return C===null||$(C)?t.check(Ed,P,z)(C):(t.enter("codeFlowValue"),F(C))}function F(C){return C===null||$(C)?(t.exit("codeFlowValue"),_(C)):(t.consume(C),F)}function z(C){return t.exit("codeFenced"),e(C)}function W(C,te,X){let Z=0;return oe;function oe(U){return C.enter("lineEnding"),C.consume(U),C.exit("lineEnding"),de}function de(U){return C.enter("codeFencedFence"),G(U)?j(C,re,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(U):re(U)}function re(U){return U===u?(C.enter("codeFencedFenceSequence"),Y(U)):X(U)}function Y(U){return U===u?(Z++,C.consume(U),Y):Z>=l?(C.exit("codeFencedFenceSequence"),G(U)?j(C,q,"whitespace")(U):q(U)):X(U)}function q(U){return U===null||$(U)?(C.exit("codeFencedFence"),te(U)):X(U)}}}function Bb(t,e,n){let r=this;return s;function s(l){return l===null?n(l):(t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),o)}function o(l){return r.parser.lazy[r.now().line]?n(l):e(l)}}var Kr={name:"codeIndented",tokenize:Yb},$b={partial:!0,tokenize:Vb};function Yb(t,e,n){let r=this;return s;function s(f){return t.enter("codeIndented"),j(t,o,"linePrefix",5)(f)}function o(f){let m=r.events[r.events.length-1];return m&&m[1].type==="linePrefix"&&m[2].sliceSerialize(m[1],!0).length>=4?l(f):n(f)}function l(f){return f===null?d(f):$(f)?t.attempt($b,l,d)(f):(t.enter("codeFlowValue"),u(f))}function u(f){return f===null||$(f)?(t.exit("codeFlowValue"),l(f)):(t.consume(f),u)}function d(f){return t.exit("codeIndented"),e(f)}}function Vb(t,e,n){let r=this;return s;function s(l){return r.parser.lazy[r.now().line]?n(l):$(l)?(t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),s):j(t,o,"linePrefix",5)(l)}function o(l){let u=r.events[r.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(l):$(l)?s(l):n(l)}}var $a={name:"codeText",previous:Wb,resolve:zb,tokenize:Ub};function zb(t){let e=t.length-4,n=3,r,s;if((t[n][1].type==="lineEnding"||t[n][1].type==="space")&&(t[e][1].type==="lineEnding"||t[e][1].type==="space")){for(r=n;++r<e;)if(t[r][1].type==="codeTextData"){t[n][1].type="codeTextPadding",t[e][1].type="codeTextPadding",n+=2,e-=2;break}}for(r=n-1,e++;++r<=e;)s===void 0?r!==e&&t[r][1].type!=="lineEnding"&&(s=r):(r===e||t[r][1].type==="lineEnding")&&(t[s][1].type="codeTextData",r!==s+2&&(t[s][1].end=t[r-1][1].end,t.splice(s+2,r-s-2),e-=r-s-2,r=s+2),s=void 0);return t}function Wb(t){return t!==96||this.events[this.events.length-1][1].type==="characterEscape"}function Ub(t,e,n){let r=this,s=0,o,l;return u;function u(y){return t.enter("codeText"),t.enter("codeTextSequence"),d(y)}function d(y){return y===96?(t.consume(y),s++,d):(t.exit("codeTextSequence"),f(y))}function f(y){return y===null?n(y):y===32?(t.enter("space"),t.consume(y),t.exit("space"),f):y===96?(l=t.enter("codeTextSequence"),o=0,p(y)):$(y)?(t.enter("lineEnding"),t.consume(y),t.exit("lineEnding"),f):(t.enter("codeTextData"),m(y))}function m(y){return y===null||y===32||y===96||$(y)?(t.exit("codeTextData"),f(y)):(t.consume(y),m)}function p(y){return y===96?(t.consume(y),o++,p):o===s?(t.exit("codeTextSequence"),t.exit("codeText"),e(y)):(l.type="codeTextData",m(y))}}var as=class{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){let r=n??Number.POSITIVE_INFINITY;return r<this.left.length?this.left.slice(e,r):e>this.left.length?this.right.slice(this.right.length-r+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-r+this.left.length).reverse())}splice(e,n,r){let s=n||0;this.setCursor(Math.trunc(e));let o=this.right.splice(this.right.length-s,Number.POSITIVE_INFINITY);return r&&Xr(this.left,r),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Xr(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Xr(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&this.right.length===0||e<0&&this.left.length===0))if(e<this.left.length){let n=this.left.splice(e,Number.POSITIVE_INFINITY);Xr(this.right,n.reverse())}else{let n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Xr(this.left,n.reverse())}}};function Xr(t,e){let n=0;if(e.length<1e4)t.push(...e);else for(;n<e.length;)t.push(...e.slice(n,n+1e4)),n+=1e4}function ls(t){let e={},n=-1,r,s,o,l,u,d,f,m=new as(t);for(;++n<m.length;){for(;n in e;)n=e[n];if(r=m.get(n),n&&r[1].type==="chunkFlow"&&m.get(n-1)[1].type==="listItemPrefix"&&(d=r[1]._tokenizer.events,o=0,o<d.length&&d[o][1].type==="lineEndingBlank"&&(o+=2),o<d.length&&d[o][1].type==="content"))for(;++o<d.length&&d[o][1].type!=="content";)d[o][1].type==="chunkText"&&(d[o][1]._isInFirstContentOfListItem=!0,o++);if(r[0]==="enter")r[1].contentType&&(Object.assign(e,Hb(m,n)),n=e[n],f=!0);else if(r[1]._container){for(o=n,s=void 0;o--&&(l=m.get(o),l[1].type==="lineEnding"||l[1].type==="lineEndingBlank");)l[0]==="enter"&&(s&&(m.get(s)[1].type="lineEndingBlank"),l[1].type="lineEnding",s=o);s&&(r[1].end={...m.get(s)[1].start},u=m.slice(s,n),u.unshift(r),m.splice(s,n-s+1,u))}}return Re(t,0,Number.POSITIVE_INFINITY,m.slice(0)),!f}function Hb(t,e){let n=t.get(e)[1],r=t.get(e)[2],s=e-1,o=[],l=n._tokenizer||r.parser[n.contentType](n.start),u=l.events,d=[],f={},m,p,y=-1,g=n,A=0,P=0,S=[P];for(;g;){for(;t.get(++s)[1]!==g;);o.push(s),g._tokenizer||(m=r.sliceStream(g),g.next||m.push(null),p&&l.defineSkip(g.start),g._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(m),g._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),p=g,g=g.next}for(g=n;++y<u.length;)u[y][0]==="exit"&&u[y-1][0]==="enter"&&u[y][1].type===u[y-1][1].type&&u[y][1].start.line!==u[y][1].end.line&&(P=y+1,S.push(P),g._tokenizer=void 0,g.previous=void 0,g=g.next);for(l.events=[],g?(g._tokenizer=void 0,g.previous=void 0):S.pop(),y=S.length;y--;){let E=u.slice(S[y],S[y+1]),_=o.pop();d.push([_,_+E.length-1]),t.splice(_,2,E)}for(d.reverse(),y=-1;++y<d.length;)f[A+d[y][0]]=A+d[y][1],A+=d[y][1]-d[y][0]-1;return f}var Ya={resolve:Gb,tokenize:jb},qb={partial:!0,tokenize:Kb};function Gb(t){return ls(t),t}function jb(t,e){let n;return r;function r(u){return t.enter("content"),n=t.enter("chunkContent",{contentType:"content"}),s(u)}function s(u){return u===null?o(u):$(u)?t.check(qb,l,o)(u):(t.consume(u),s)}function o(u){return t.exit("chunkContent"),t.exit("content"),e(u)}function l(u){return t.consume(u),t.exit("chunkContent"),n.next=t.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,s}}function Kb(t,e,n){let r=this;return s;function s(l){return t.exit("chunkContent"),t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),j(t,o,"linePrefix")}function o(l){if(l===null||$(l))return n(l);let u=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(l):t.interrupt(r.parser.constructs.flow,n,e)(l)}}function us(t,e,n,r,s,o,l,u,d){let f=d||Number.POSITIVE_INFINITY,m=0;return p;function p(E){return E===60?(t.enter(r),t.enter(s),t.enter(o),t.consume(E),t.exit(o),y):E===null||E===32||E===41||qr(E)?n(E):(t.enter(r),t.enter(l),t.enter(u),t.enter("chunkString",{contentType:"string"}),P(E))}function y(E){return E===62?(t.enter(o),t.consume(E),t.exit(o),t.exit(s),t.exit(r),e):(t.enter(u),t.enter("chunkString",{contentType:"string"}),g(E))}function g(E){return E===62?(t.exit("chunkString"),t.exit(u),y(E)):E===null||E===60||$(E)?n(E):(t.consume(E),E===92?A:g)}function A(E){return E===60||E===62||E===92?(t.consume(E),g):g(E)}function P(E){return!m&&(E===null||E===41||Ae(E))?(t.exit("chunkString"),t.exit(u),t.exit(l),t.exit(r),e(E)):m<f&&E===40?(t.consume(E),m++,P):E===41?(t.consume(E),m--,P):E===null||E===32||E===40||qr(E)?n(E):(t.consume(E),E===92?S:P)}function S(E){return E===40||E===41||E===92?(t.consume(E),P):P(E)}}function cs(t,e,n,r,s,o){let l=this,u=0,d;return f;function f(g){return t.enter(r),t.enter(s),t.consume(g),t.exit(s),t.enter(o),m}function m(g){return u>999||g===null||g===91||g===93&&!d||g===94&&!u&&"_hiddenFootnoteSupport"in l.parser.constructs?n(g):g===93?(t.exit(o),t.enter(s),t.consume(g),t.exit(s),t.exit(r),e):$(g)?(t.enter("lineEnding"),t.consume(g),t.exit("lineEnding"),m):(t.enter("chunkString",{contentType:"string"}),p(g))}function p(g){return g===null||g===91||g===93||$(g)||u++>999?(t.exit("chunkString"),m(g)):(t.consume(g),d||(d=!G(g)),g===92?y:p)}function y(g){return g===91||g===92||g===93?(t.consume(g),u++,p):p(g)}}function ds(t,e,n,r,s,o){let l;return u;function u(y){return y===34||y===39||y===40?(t.enter(r),t.enter(s),t.consume(y),t.exit(s),l=y===40?41:y,d):n(y)}function d(y){return y===l?(t.enter(s),t.consume(y),t.exit(s),t.exit(r),e):(t.enter(o),f(y))}function f(y){return y===l?(t.exit(o),d(l)):y===null?n(y):$(y)?(t.enter("lineEnding"),t.consume(y),t.exit("lineEnding"),j(t,f,"linePrefix")):(t.enter("chunkString",{contentType:"string"}),m(y))}function m(y){return y===l||y===null||$(y)?(t.exit("chunkString"),f(y)):(t.consume(y),y===92?p:m)}function p(y){return y===l||y===92?(t.consume(y),m):m(y)}}function Rn(t,e){let n;return r;function r(s){return $(s)?(t.enter("lineEnding"),t.consume(s),t.exit("lineEnding"),n=!0,r):G(s)?j(t,r,n?"linePrefix":"lineSuffix")(s):e(s)}}var Va={name:"definition",tokenize:Zb},Xb={partial:!0,tokenize:Jb};function Zb(t,e,n){let r=this,s;return o;function o(g){return t.enter("definition"),l(g)}function l(g){return cs.call(r,t,u,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(g)}function u(g){return s=tn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),g===58?(t.enter("definitionMarker"),t.consume(g),t.exit("definitionMarker"),d):n(g)}function d(g){return Ae(g)?Rn(t,f)(g):f(g)}function f(g){return us(t,m,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(g)}function m(g){return t.attempt(Xb,p,p)(g)}function p(g){return G(g)?j(t,y,"whitespace")(g):y(g)}function y(g){return g===null||$(g)?(t.exit("definition"),r.parser.defined.push(s),e(g)):n(g)}}function Jb(t,e,n){return r;function r(u){return Ae(u)?Rn(t,s)(u):n(u)}function s(u){return ds(t,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(u)}function o(u){return G(u)?j(t,l,"whitespace")(u):l(u)}function l(u){return u===null||$(u)?e(u):n(u)}}var za={name:"hardBreakEscape",tokenize:Qb};function Qb(t,e,n){return r;function r(o){return t.enter("hardBreakEscape"),t.consume(o),s}function s(o){return $(o)?(t.exit("hardBreakEscape"),e(o)):n(o)}}var Wa={name:"headingAtx",resolve:ek,tokenize:tk};function ek(t,e){let n=t.length-2,r=3,s,o;return t[r][1].type==="whitespace"&&(r+=2),n-2>r&&t[n][1].type==="whitespace"&&(n-=2),t[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&t[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(s={type:"atxHeadingText",start:t[r][1].start,end:t[n][1].end},o={type:"chunkText",start:t[r][1].start,end:t[n][1].end,contentType:"text"},Re(t,r,n-r+1,[["enter",s,e],["enter",o,e],["exit",o,e],["exit",s,e]])),t}function tk(t,e,n){let r=0;return s;function s(m){return t.enter("atxHeading"),o(m)}function o(m){return t.enter("atxHeadingSequence"),l(m)}function l(m){return m===35&&r++<6?(t.consume(m),l):m===null||Ae(m)?(t.exit("atxHeadingSequence"),u(m)):n(m)}function u(m){return m===35?(t.enter("atxHeadingSequence"),d(m)):m===null||$(m)?(t.exit("atxHeading"),e(m)):G(m)?j(t,u,"whitespace")(m):(t.enter("atxHeadingText"),f(m))}function d(m){return m===35?(t.consume(m),d):(t.exit("atxHeadingSequence"),u(m))}function f(m){return m===null||m===35||Ae(m)?(t.exit("atxHeadingText"),u(m)):(t.consume(m),f)}}var vd=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ua=["pre","script","style","textarea"];var Ha={concrete:!0,name:"htmlFlow",resolveTo:ik,tokenize:sk},nk={partial:!0,tokenize:ak},rk={partial:!0,tokenize:ok};function ik(t){let e=t.length;for(;e--&&!(t[e][0]==="enter"&&t[e][1].type==="htmlFlow"););return e>1&&t[e-2][1].type==="linePrefix"&&(t[e][1].start=t[e-2][1].start,t[e+1][1].start=t[e-2][1].start,t.splice(e-2,2)),t}function sk(t,e,n){let r=this,s,o,l,u,d;return f;function f(b){return m(b)}function m(b){return t.enter("htmlFlow"),t.enter("htmlFlowData"),t.consume(b),p}function p(b){return b===33?(t.consume(b),y):b===47?(t.consume(b),o=!0,P):b===63?(t.consume(b),s=3,r.interrupt?e:x):ot(b)?(t.consume(b),l=String.fromCharCode(b),S):n(b)}function y(b){return b===45?(t.consume(b),s=2,g):b===91?(t.consume(b),s=5,u=0,A):ot(b)?(t.consume(b),s=4,r.interrupt?e:x):n(b)}function g(b){return b===45?(t.consume(b),r.interrupt?e:x):n(b)}function A(b){let ze="CDATA[";return b===ze.charCodeAt(u++)?(t.consume(b),u===ze.length?r.interrupt?e:re:A):n(b)}function P(b){return ot(b)?(t.consume(b),l=String.fromCharCode(b),S):n(b)}function S(b){if(b===null||b===47||b===62||Ae(b)){let ze=b===47,Nt=l.toLowerCase();return!ze&&!o&&Ua.includes(Nt)?(s=1,r.interrupt?e(b):re(b)):vd.includes(l.toLowerCase())?(s=6,ze?(t.consume(b),E):r.interrupt?e(b):re(b)):(s=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(b):o?_(b):F(b))}return b===45||je(b)?(t.consume(b),l+=String.fromCharCode(b),S):n(b)}function E(b){return b===62?(t.consume(b),r.interrupt?e:re):n(b)}function _(b){return G(b)?(t.consume(b),_):oe(b)}function F(b){return b===47?(t.consume(b),oe):b===58||b===95||ot(b)?(t.consume(b),z):G(b)?(t.consume(b),F):oe(b)}function z(b){return b===45||b===46||b===58||b===95||je(b)?(t.consume(b),z):W(b)}function W(b){return b===61?(t.consume(b),C):G(b)?(t.consume(b),W):F(b)}function C(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(t.consume(b),d=b,te):G(b)?(t.consume(b),C):X(b)}function te(b){return b===d?(t.consume(b),d=null,Z):b===null||$(b)?n(b):(t.consume(b),te)}function X(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||Ae(b)?W(b):(t.consume(b),X)}function Z(b){return b===47||b===62||G(b)?F(b):n(b)}function oe(b){return b===62?(t.consume(b),de):n(b)}function de(b){return b===null||$(b)?re(b):G(b)?(t.consume(b),de):n(b)}function re(b){return b===45&&s===2?(t.consume(b),ie):b===60&&s===1?(t.consume(b),ge):b===62&&s===4?(t.consume(b),De):b===63&&s===3?(t.consume(b),x):b===93&&s===5?(t.consume(b),Ze):$(b)&&(s===6||s===7)?(t.exit("htmlFlowData"),t.check(nk,Oe,Y)(b)):b===null||$(b)?(t.exit("htmlFlowData"),Y(b)):(t.consume(b),re)}function Y(b){return t.check(rk,q,Oe)(b)}function q(b){return t.enter("lineEnding"),t.consume(b),t.exit("lineEnding"),U}function U(b){return b===null||$(b)?Y(b):(t.enter("htmlFlowData"),re(b))}function ie(b){return b===45?(t.consume(b),x):re(b)}function ge(b){return b===47?(t.consume(b),l="",ye):re(b)}function ye(b){if(b===62){let ze=l.toLowerCase();return Ua.includes(ze)?(t.consume(b),De):re(b)}return ot(b)&&l.length<8?(t.consume(b),l+=String.fromCharCode(b),ye):re(b)}function Ze(b){return b===93?(t.consume(b),x):re(b)}function x(b){return b===62?(t.consume(b),De):b===45&&s===2?(t.consume(b),x):re(b)}function De(b){return b===null||$(b)?(t.exit("htmlFlowData"),Oe(b)):(t.consume(b),De)}function Oe(b){return t.exit("htmlFlow"),e(b)}}function ok(t,e,n){let r=this;return s;function s(l){return $(l)?(t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),o):n(l)}function o(l){return r.parser.lazy[r.now().line]?n(l):e(l)}}function ak(t,e,n){return r;function r(s){return t.enter("lineEnding"),t.consume(s),t.exit("lineEnding"),t.attempt(mn,e,n)}}var qa={name:"htmlText",tokenize:lk};function lk(t,e,n){let r=this,s,o,l;return u;function u(x){return t.enter("htmlText"),t.enter("htmlTextData"),t.consume(x),d}function d(x){return x===33?(t.consume(x),f):x===47?(t.consume(x),W):x===63?(t.consume(x),F):ot(x)?(t.consume(x),X):n(x)}function f(x){return x===45?(t.consume(x),m):x===91?(t.consume(x),o=0,A):ot(x)?(t.consume(x),_):n(x)}function m(x){return x===45?(t.consume(x),g):n(x)}function p(x){return x===null?n(x):x===45?(t.consume(x),y):$(x)?(l=p,ge(x)):(t.consume(x),p)}function y(x){return x===45?(t.consume(x),g):p(x)}function g(x){return x===62?ie(x):x===45?y(x):p(x)}function A(x){let De="CDATA[";return x===De.charCodeAt(o++)?(t.consume(x),o===De.length?P:A):n(x)}function P(x){return x===null?n(x):x===93?(t.consume(x),S):$(x)?(l=P,ge(x)):(t.consume(x),P)}function S(x){return x===93?(t.consume(x),E):P(x)}function E(x){return x===62?ie(x):x===93?(t.consume(x),E):P(x)}function _(x){return x===null||x===62?ie(x):$(x)?(l=_,ge(x)):(t.consume(x),_)}function F(x){return x===null?n(x):x===63?(t.consume(x),z):$(x)?(l=F,ge(x)):(t.consume(x),F)}function z(x){return x===62?ie(x):F(x)}function W(x){return ot(x)?(t.consume(x),C):n(x)}function C(x){return x===45||je(x)?(t.consume(x),C):te(x)}function te(x){return $(x)?(l=te,ge(x)):G(x)?(t.consume(x),te):ie(x)}function X(x){return x===45||je(x)?(t.consume(x),X):x===47||x===62||Ae(x)?Z(x):n(x)}function Z(x){return x===47?(t.consume(x),ie):x===58||x===95||ot(x)?(t.consume(x),oe):$(x)?(l=Z,ge(x)):G(x)?(t.consume(x),Z):ie(x)}function oe(x){return x===45||x===46||x===58||x===95||je(x)?(t.consume(x),oe):de(x)}function de(x){return x===61?(t.consume(x),re):$(x)?(l=de,ge(x)):G(x)?(t.consume(x),de):Z(x)}function re(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(t.consume(x),s=x,Y):$(x)?(l=re,ge(x)):G(x)?(t.consume(x),re):(t.consume(x),q)}function Y(x){return x===s?(t.consume(x),s=void 0,U):x===null?n(x):$(x)?(l=Y,ge(x)):(t.consume(x),Y)}function q(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||Ae(x)?Z(x):(t.consume(x),q)}function U(x){return x===47||x===62||Ae(x)?Z(x):n(x)}function ie(x){return x===62?(t.consume(x),t.exit("htmlTextData"),t.exit("htmlText"),e):n(x)}function ge(x){return t.exit("htmlTextData"),t.enter("lineEnding"),t.consume(x),t.exit("lineEnding"),ye}function ye(x){return G(x)?j(t,Ze,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):Ze(x)}function Ze(x){return t.enter("htmlTextData"),l(x)}}var Dn={name:"labelEnd",resolveAll:fk,resolveTo:hk,tokenize:mk},uk={tokenize:pk},ck={tokenize:gk},dk={tokenize:wk};function fk(t){let e=-1,n=[];for(;++e<t.length;){let r=t[e][1];if(n.push(t[e]),r.type==="labelImage"||r.type==="labelLink"||r.type==="labelEnd"){let s=r.type==="labelImage"?4:2;r.type="data",e+=s}}return t.length!==n.length&&Re(t,0,t.length,n),t}function hk(t,e){let n=t.length,r=0,s,o,l,u;for(;n--;)if(s=t[n][1],o){if(s.type==="link"||s.type==="labelLink"&&s._inactive)break;t[n][0]==="enter"&&s.type==="labelLink"&&(s._inactive=!0)}else if(l){if(t[n][0]==="enter"&&(s.type==="labelImage"||s.type==="labelLink")&&!s._balanced&&(o=n,s.type!=="labelLink")){r=2;break}}else s.type==="labelEnd"&&(l=n);let d={type:t[o][1].type==="labelLink"?"link":"image",start:{...t[o][1].start},end:{...t[t.length-1][1].end}},f={type:"label",start:{...t[o][1].start},end:{...t[l][1].end}},m={type:"labelText",start:{...t[o+r+2][1].end},end:{...t[l-2][1].start}};return u=[["enter",d,e],["enter",f,e]],u=Ge(u,t.slice(o+1,o+r+3)),u=Ge(u,[["enter",m,e]]),u=Ge(u,ur(e.parser.constructs.insideSpan.null,t.slice(o+r+4,l-3),e)),u=Ge(u,[["exit",m,e],t[l-2],t[l-1],["exit",f,e]]),u=Ge(u,t.slice(l+1)),u=Ge(u,[["exit",d,e]]),Re(t,o,t.length,u),t}function mk(t,e,n){let r=this,s=r.events.length,o,l;for(;s--;)if((r.events[s][1].type==="labelImage"||r.events[s][1].type==="labelLink")&&!r.events[s][1]._balanced){o=r.events[s][1];break}return u;function u(y){return o?o._inactive?p(y):(l=r.parser.defined.includes(tn(r.sliceSerialize({start:o.end,end:r.now()}))),t.enter("labelEnd"),t.enter("labelMarker"),t.consume(y),t.exit("labelMarker"),t.exit("labelEnd"),d):n(y)}function d(y){return y===40?t.attempt(uk,m,l?m:p)(y):y===91?t.attempt(ck,m,l?f:p)(y):l?m(y):p(y)}function f(y){return t.attempt(dk,m,p)(y)}function m(y){return e(y)}function p(y){return o._balanced=!0,n(y)}}function pk(t,e,n){return r;function r(p){return t.enter("resource"),t.enter("resourceMarker"),t.consume(p),t.exit("resourceMarker"),s}function s(p){return Ae(p)?Rn(t,o)(p):o(p)}function o(p){return p===41?m(p):us(t,l,u,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(p)}function l(p){return Ae(p)?Rn(t,d)(p):m(p)}function u(p){return n(p)}function d(p){return p===34||p===39||p===40?ds(t,f,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(p):m(p)}function f(p){return Ae(p)?Rn(t,m)(p):m(p)}function m(p){return p===41?(t.enter("resourceMarker"),t.consume(p),t.exit("resourceMarker"),t.exit("resource"),e):n(p)}}function gk(t,e,n){let r=this;return s;function s(u){return cs.call(r,t,o,l,"reference","referenceMarker","referenceString")(u)}function o(u){return r.parser.defined.includes(tn(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?e(u):n(u)}function l(u){return n(u)}}function wk(t,e,n){return r;function r(o){return t.enter("reference"),t.enter("referenceMarker"),t.consume(o),t.exit("referenceMarker"),s}function s(o){return o===93?(t.enter("referenceMarker"),t.consume(o),t.exit("referenceMarker"),t.exit("reference"),e):n(o)}}var Ga={name:"labelStartImage",resolveAll:Dn.resolveAll,tokenize:yk};function yk(t,e,n){let r=this;return s;function s(u){return t.enter("labelImage"),t.enter("labelImageMarker"),t.consume(u),t.exit("labelImageMarker"),o}function o(u){return u===91?(t.enter("labelMarker"),t.consume(u),t.exit("labelMarker"),t.exit("labelImage"),l):n(u)}function l(u){return u===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(u):e(u)}}var ja={name:"labelStartLink",resolveAll:Dn.resolveAll,tokenize:bk};function bk(t,e,n){let r=this;return s;function s(l){return t.enter("labelLink"),t.enter("labelMarker"),t.consume(l),t.exit("labelMarker"),t.exit("labelLink"),o}function o(l){return l===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(l):e(l)}}var Zr={name:"lineEnding",tokenize:kk};function kk(t,e){return n;function n(r){return t.enter("lineEnding"),t.consume(r),t.exit("lineEnding"),j(t,e,"linePrefix")}}var On={name:"thematicBreak",tokenize:xk};function xk(t,e,n){let r=0,s;return o;function o(f){return t.enter("thematicBreak"),l(f)}function l(f){return s=f,u(f)}function u(f){return f===s?(t.enter("thematicBreakSequence"),d(f)):r>=3&&(f===null||$(f))?(t.exit("thematicBreak"),e(f)):n(f)}function d(f){return f===s?(t.consume(f),r++,d):(t.exit("thematicBreakSequence"),G(f)?j(t,u,"whitespace")(f):u(f))}}var $e={continuation:{tokenize:Ck},exit:Tk,name:"list",tokenize:vk},Ak={partial:!0,tokenize:_k},Ek={partial:!0,tokenize:Sk};function vk(t,e,n){let r=this,s=r.events[r.events.length-1],o=s&&s[1].type==="linePrefix"?s[2].sliceSerialize(s[1],!0).length:0,l=0;return u;function u(g){let A=r.containerState.type||(g===42||g===43||g===45?"listUnordered":"listOrdered");if(A==="listUnordered"?!r.containerState.marker||g===r.containerState.marker:Gr(g)){if(r.containerState.type||(r.containerState.type=A,t.enter(A,{_container:!0})),A==="listUnordered")return t.enter("listItemPrefix"),g===42||g===45?t.check(On,n,f)(g):f(g);if(!r.interrupt||g===49)return t.enter("listItemPrefix"),t.enter("listItemValue"),d(g)}return n(g)}function d(g){return Gr(g)&&++l<10?(t.consume(g),d):(!r.interrupt||l<2)&&(r.containerState.marker?g===r.containerState.marker:g===41||g===46)?(t.exit("listItemValue"),f(g)):n(g)}function f(g){return t.enter("listItemMarker"),t.consume(g),t.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||g,t.check(mn,r.interrupt?n:m,t.attempt(Ak,y,p))}function m(g){return r.containerState.initialBlankLine=!0,o++,y(g)}function p(g){return G(g)?(t.enter("listItemPrefixWhitespace"),t.consume(g),t.exit("listItemPrefixWhitespace"),y):n(g)}function y(g){return r.containerState.size=o+r.sliceSerialize(t.exit("listItemPrefix"),!0).length,e(g)}}function Ck(t,e,n){let r=this;return r.containerState._closeFlow=void 0,t.check(mn,s,o);function s(u){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,j(t,e,"listItemIndent",r.containerState.size+1)(u)}function o(u){return r.containerState.furtherBlankLines||!G(u)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,l(u)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,t.attempt(Ek,e,l)(u))}function l(u){return r.containerState._closeFlow=!0,r.interrupt=void 0,j(t,t.attempt($e,e,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function Sk(t,e,n){let r=this;return j(t,s,"listItemIndent",r.containerState.size+1);function s(o){let l=r.events[r.events.length-1];return l&&l[1].type==="listItemIndent"&&l[2].sliceSerialize(l[1],!0).length===r.containerState.size?e(o):n(o)}}function Tk(t){t.exit(this.containerState.type)}function _k(t,e,n){let r=this;return j(t,s,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function s(o){let l=r.events[r.events.length-1];return!G(o)&&l&&l[1].type==="listItemPrefixWhitespace"?e(o):n(o)}}var fs={name:"setextUnderline",resolveTo:Fk,tokenize:Pk};function Fk(t,e){let n=t.length,r,s,o;for(;n--;)if(t[n][0]==="enter"){if(t[n][1].type==="content"){r=n;break}t[n][1].type==="paragraph"&&(s=n)}else t[n][1].type==="content"&&t.splice(n,1),!o&&t[n][1].type==="definition"&&(o=n);let l={type:"setextHeading",start:{...t[s][1].start},end:{...t[t.length-1][1].end}};return t[s][1].type="setextHeadingText",o?(t.splice(s,0,["enter",l,e]),t.splice(o+1,0,["exit",t[r][1],e]),t[r][1].end={...t[o][1].end}):t[r][1]=l,t.push(["exit",l,e]),t}function Pk(t,e,n){let r=this,s;return o;function o(f){let m=r.events.length,p;for(;m--;)if(r.events[m][1].type!=="lineEnding"&&r.events[m][1].type!=="linePrefix"&&r.events[m][1].type!=="content"){p=r.events[m][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||p)?(t.enter("setextHeadingLine"),s=f,l(f)):n(f)}function l(f){return t.enter("setextHeadingLineSequence"),u(f)}function u(f){return f===s?(t.consume(f),u):(t.exit("setextHeadingLineSequence"),G(f)?j(t,d,"lineSuffix")(f):d(f))}function d(f){return f===null||$(f)?(t.exit("setextHeadingLine"),e(f)):n(f)}}var Cd={tokenize:Ik};function Ik(t){let e=this,n=t.attempt(mn,r,t.attempt(this.parser.constructs.flowInitial,s,j(t,t.attempt(this.parser.constructs.flow,s,t.attempt(Ya,s)),"linePrefix")));return n;function r(o){if(o===null){t.consume(o);return}return t.enter("lineEndingBlank"),t.consume(o),t.exit("lineEndingBlank"),e.currentConstruct=void 0,n}function s(o){if(o===null){t.consume(o);return}return t.enter("lineEnding"),t.consume(o),t.exit("lineEnding"),e.currentConstruct=void 0,n}}var Sd={resolveAll:Pd()},Td=Fd("string"),_d=Fd("text");function Fd(t){return{resolveAll:Pd(t==="text"?Rk:void 0),tokenize:e};function e(n){let r=this,s=this.parser.constructs[t],o=n.attempt(s,l,u);return l;function l(m){return f(m)?o(m):u(m)}function u(m){if(m===null){n.consume(m);return}return n.enter("data"),n.consume(m),d}function d(m){return f(m)?(n.exit("data"),o(m)):(n.consume(m),d)}function f(m){if(m===null)return!0;let p=s[m],y=-1;if(p)for(;++y<p.length;){let g=p[y];if(!g.previous||g.previous.call(r,r.previous))return!0}return!1}}}function Pd(t){return e;function e(n,r){let s=-1,o;for(;++s<=n.length;)o===void 0?n[s]&&n[s][1].type==="data"&&(o=s,s++):(!n[s]||n[s][1].type!=="data")&&(s!==o+2&&(n[o][1].end=n[s-1][1].end,n.splice(o+2,s-o-2),s=o+2),o=void 0);return t?t(n,r):n}}function Rk(t,e){let n=0;for(;++n<=t.length;)if((n===t.length||t[n][1].type==="lineEnding")&&t[n-1][1].type==="data"){let r=t[n-1][1],s=e.sliceStream(r),o=s.length,l=-1,u=0,d;for(;o--;){let f=s[o];if(typeof f=="string"){for(l=f.length;f.charCodeAt(l-1)===32;)u++,l--;if(l)break;l=-1}else if(f===-2)d=!0,u++;else if(f!==-1){o++;break}}if(u){let f={type:n===t.length||d||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?l:r.start._bufferIndex+l,_index:r.start._index+o,line:r.end.line,column:r.end.column-u,offset:r.end.offset-u},end:{...r.end}};r.end={...f.start},r.start.offset===r.end.offset?Object.assign(r,f):(t.splice(n,0,["enter",f,e],["exit",f,e]),n+=2)}n++}return t}var Ka={};yc(Ka,{attentionMarkers:()=>Yk,contentInitial:()=>Ok,disable:()=>Vk,document:()=>Dk,flow:()=>Mk,flowInitial:()=>Lk,insideSpan:()=>$k,string:()=>Nk,text:()=>Bk});var Dk={42:$e,43:$e,45:$e,48:$e,49:$e,50:$e,51:$e,52:$e,53:$e,54:$e,55:$e,56:$e,57:$e,62:rs},Ok={91:Va},Lk={[-2]:Kr,[-1]:Kr,32:Kr},Mk={35:Wa,42:On,45:[fs,On],60:Ha,61:fs,95:On,96:os,126:os},Nk={38:ss,92:is},Bk={[-5]:Zr,[-4]:Zr,[-3]:Zr,33:Ga,38:ss,42:jr,60:[Ba,qa],91:ja,92:[za,is],93:Dn,95:jr,96:$a},$k={null:[jr,Sd]},Yk={null:[42,95]},Vk={null:[]};function Id(t,e,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},s={},o=[],l=[],u=[],d=!0,f={attempt:Z(te),check:Z(X),consume:z,enter:W,exit:C,interrupt:Z(X,{interrupt:!0})},m={code:null,containerState:{},defineSkip:E,events:[],now:S,parser:t,previous:null,sliceSerialize:A,sliceStream:P,write:g},p=e.tokenize.call(m,f),y;return e.resolveAll&&o.push(e),m;function g(Y){return l=Ge(l,Y),_(),l[l.length-1]!==null?[]:(oe(e,0),m.events=ur(o,m.events,m),m.events)}function A(Y,q){return Wk(P(Y),q)}function P(Y){return zk(l,Y)}function S(){let{_bufferIndex:Y,_index:q,line:U,column:ie,offset:ge}=r;return{_bufferIndex:Y,_index:q,line:U,column:ie,offset:ge}}function E(Y){s[Y.line]=Y.column,re()}function _(){let Y;for(;r._index<l.length;){let q=l[r._index];if(typeof q=="string")for(Y=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===Y&&r._bufferIndex<q.length;)F(q.charCodeAt(r._bufferIndex));else F(q)}}function F(Y){d=void 0,y=Y,p=p(Y)}function z(Y){$(Y)?(r.line++,r.column=1,r.offset+=Y===-3?2:1,re()):Y!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===l[r._index].length&&(r._bufferIndex=-1,r._index++)),m.previous=Y,d=!0}function W(Y,q){let U=q||{};return U.type=Y,U.start=S(),m.events.push(["enter",U,m]),u.push(U),U}function C(Y){let q=u.pop();return q.end=S(),m.events.push(["exit",q,m]),q}function te(Y,q){oe(Y,q.from)}function X(Y,q){q.restore()}function Z(Y,q){return U;function U(ie,ge,ye){let Ze,x,De,Oe;return Array.isArray(ie)?ze(ie):"tokenize"in ie?ze([ie]):b(ie);function b(Ce){return En;function En(kt){let Bt=kt!==null&&Ce[kt],on=kt!==null&&Ce.null,Pr=[...Array.isArray(Bt)?Bt:Bt?[Bt]:[],...Array.isArray(on)?on:on?[on]:[]];return ze(Pr)(kt)}}function ze(Ce){return Ze=Ce,x=0,Ce.length===0?ye:Nt(Ce[x])}function Nt(Ce){return En;function En(kt){return Oe=de(),De=Ce,Ce.partial||(m.currentConstruct=Ce),Ce.name&&m.parser.constructs.disable.null.includes(Ce.name)?An(kt):Ce.tokenize.call(q?Object.assign(Object.create(m),q):m,f,sn,An)(kt)}}function sn(Ce){return d=!0,Y(De,Oe),ge}function An(Ce){return d=!0,Oe.restore(),++x<Ze.length?Nt(Ze[x]):ye}}}function oe(Y,q){Y.resolveAll&&!o.includes(Y)&&o.push(Y),Y.resolve&&Re(m.events,q,m.events.length-q,Y.resolve(m.events.slice(q),m)),Y.resolveTo&&(m.events=Y.resolveTo(m.events,m))}function de(){let Y=S(),q=m.previous,U=m.currentConstruct,ie=m.events.length,ge=Array.from(u);return{from:ie,restore:ye};function ye(){r=Y,m.previous=q,m.currentConstruct=U,m.events.length=ie,u=ge,re()}}function re(){r.line in s&&r.column<2&&(r.column=s[r.line],r.offset+=s[r.line]-1)}}function zk(t,e){let n=e.start._index,r=e.start._bufferIndex,s=e.end._index,o=e.end._bufferIndex,l;if(n===s)l=[t[n].slice(r,o)];else{if(l=t.slice(n,s),r>-1){let u=l[0];typeof u=="string"?l[0]=u.slice(r):l.shift()}o>0&&l.push(t[s].slice(0,o))}return l}function Wk(t,e){let n=-1,r=[],s;for(;++n<t.length;){let o=t[n],l;if(typeof o=="string")l=o;else switch(o){case-5:{l="\r";break}case-4:{l=`
`;break}case-3:{l=`\r
`;break}case-2:{l=e?" ":"	";break}case-1:{if(!e&&s)continue;l=" ";break}default:l=String.fromCharCode(o)}s=o===-2,r.push(l)}return r.join("")}function Xa(t){let r={constructs:hd([Ka,...(t||{}).extensions||[]]),content:s(bd),defined:[],document:s(xd),flow:s(Cd),lazy:{},string:s(Td),text:s(_d)};return r;function s(o){return l;function l(u){return Id(r,o,u)}}}function Za(t){for(;!ls(t););return t}var Rd=/[\0\t\n\r]/g;function Ja(){let t=1,e="",n=!0,r;return s;function s(o,l,u){let d=[],f,m,p,y,g;for(o=e+(typeof o=="string"?o.toString():new TextDecoder(l||void 0).decode(o)),p=0,e="",n&&(o.charCodeAt(0)===65279&&p++,n=void 0);p<o.length;){if(Rd.lastIndex=p,f=Rd.exec(o),y=f&&f.index!==void 0?f.index:o.length,g=o.charCodeAt(y),!f){e=o.slice(p);break}if(g===10&&p===y&&r)d.push(-3),r=void 0;else switch(r&&(d.push(-5),r=void 0),p<y&&(d.push(o.slice(p,y)),t+=y-p),g){case 0:{d.push(65533),t++;break}case 9:{for(m=Math.ceil(t/4)*4,d.push(-2);t++<m;)d.push(-1);break}case 10:{d.push(-4),t=1;break}default:r=!0,t=1}p=y+1}return u&&(r&&d.push(-5),e&&d.push(e),d.push(null)),d}}var Uk=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function hs(t){return t.replace(Uk,Hk)}function Hk(t,e,n){if(e)return e;if(n.charCodeAt(0)===35){let s=n.charCodeAt(1),o=s===120||s===88;return ns(n.slice(o?2:1),o?16:10)}return ar(n)||t}function pn(t){return!t||typeof t!="object"?"":"position"in t||"type"in t?Dd(t.position):"start"in t||"end"in t?Dd(t):"line"in t||"column"in t?Qa(t):""}function Qa(t){return Od(t&&t.line)+":"+Od(t&&t.column)}function Dd(t){return Qa(t&&t.start)+"-"+Qa(t&&t.end)}function Od(t){return t&&typeof t=="number"?t:1}var Md={}.hasOwnProperty;function el(t,e,n){return typeof e!="string"&&(n=e,e=void 0),qk(n)(Za(Xa(n).document().write(Ja()(t,e,!0))))}function qk(t){let e={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(We),autolinkProtocol:Z,autolinkEmail:Z,atxHeading:o(ki),blockQuote:o(kt),characterEscape:Z,characterReference:Z,codeFenced:o(Bt),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:o(Bt,l),codeText:o(on,l),codeTextData:Z,data:Z,codeFlowValue:Z,definition:o(Pr),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:o(Ko),hardBreakEscape:o(xi),hardBreakTrailing:o(xi),htmlFlow:o(Ai,l),htmlFlowData:Z,htmlText:o(Ai,l),htmlTextData:Z,image:o(Ei),label:l,link:o(We),listItem:o(Xo),listItemValue:y,listOrdered:o(Un,p),listUnordered:o(Un),paragraph:o(Zo),reference:b,referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:o(ki),strong:o(vi),thematicBreak:o(Ci)},exit:{atxHeading:d(),atxHeadingSequence:W,autolink:d(),autolinkEmail:En,autolinkProtocol:Ce,blockQuote:d(),characterEscapeValue:oe,characterReferenceMarkerHexadecimal:Nt,characterReferenceMarkerNumeric:Nt,characterReferenceValue:sn,characterReference:An,codeFenced:d(S),codeFencedFence:P,codeFencedFenceInfo:g,codeFencedFenceMeta:A,codeFlowValue:oe,codeIndented:d(E),codeText:d(U),codeTextData:oe,data:oe,definition:d(),definitionDestinationString:z,definitionLabelString:_,definitionTitleString:F,emphasis:d(),hardBreakEscape:d(re),hardBreakTrailing:d(re),htmlFlow:d(Y),htmlFlowData:oe,htmlText:d(q),htmlTextData:oe,image:d(ge),label:Ze,labelText:ye,lineEnding:de,link:d(ie),listItem:d(),listOrdered:d(),listUnordered:d(),paragraph:d(),referenceString:ze,resourceDestinationString:x,resourceTitleString:De,resource:Oe,setextHeading:d(X),setextHeadingLineSequence:te,setextHeadingText:C,strong:d(),thematicBreak:d()}};Nd(e,(t||{}).mdastExtensions||[]);let n={};return r;function r(T){let R={type:"root",children:[]},D={stack:[R],tokenStack:[],config:e,enter:u,exit:f,buffer:l,resume:m,data:n},J=[],ae=-1;for(;++ae<T.length;)if(T[ae][1].type==="listOrdered"||T[ae][1].type==="listUnordered")if(T[ae][0]==="enter")J.push(ae);else{let Le=J.pop();ae=s(T,Le,ae)}for(ae=-1;++ae<T.length;){let Le=e[T[ae][0]];Md.call(Le,T[ae][1].type)&&Le[T[ae][1].type].call(Object.assign({sliceSerialize:T[ae][2].sliceSerialize},D),T[ae][1])}if(D.tokenStack.length>0){let Le=D.tokenStack[D.tokenStack.length-1];(Le[1]||Ld).call(D,void 0,Le[0])}for(R.position={start:gn(T.length>0?T[0][1].start:{line:1,column:1,offset:0}),end:gn(T.length>0?T[T.length-2][1].end:{line:1,column:1,offset:0})},ae=-1;++ae<e.transforms.length;)R=e.transforms[ae](R)||R;return R}function s(T,R,D){let J=R-1,ae=-1,Le=!1,xt,Je,lt,_t;for(;++J<=D;){let He=T[J];switch(He[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{He[0]==="enter"?ae++:ae--,_t=void 0;break}case"lineEndingBlank":{He[0]==="enter"&&(xt&&!_t&&!ae&&!lt&&(lt=J),_t=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:_t=void 0}if(!ae&&He[0]==="enter"&&He[1].type==="listItemPrefix"||ae===-1&&He[0]==="exit"&&(He[1].type==="listUnordered"||He[1].type==="listOrdered")){if(xt){let At=J;for(Je=void 0;At--;){let Et=T[At];if(Et[1].type==="lineEnding"||Et[1].type==="lineEndingBlank"){if(Et[0]==="exit")continue;Je&&(T[Je][1].type="lineEndingBlank",Le=!0),Et[1].type="lineEnding",Je=At}else if(!(Et[1].type==="linePrefix"||Et[1].type==="blockQuotePrefix"||Et[1].type==="blockQuotePrefixWhitespace"||Et[1].type==="blockQuoteMarker"||Et[1].type==="listItemIndent"))break}lt&&(!Je||lt<Je)&&(xt._spread=!0),xt.end=Object.assign({},Je?T[Je][1].start:He[1].end),T.splice(Je||J,0,["exit",xt,He[2]]),J++,D++}if(He[1].type==="listItemPrefix"){let At={type:"listItem",_spread:!1,start:Object.assign({},He[1].start),end:void 0};xt=At,T.splice(J,0,["enter",At,He[2]]),J++,D++,lt=void 0,_t=!0}}}return T[R][1]._spread=Le,D}function o(T,R){return D;function D(J){u.call(this,T(J),J),R&&R.call(this,J)}}function l(){this.stack.push({type:"fragment",children:[]})}function u(T,R,D){this.stack[this.stack.length-1].children.push(T),this.stack.push(T),this.tokenStack.push([R,D||void 0]),T.position={start:gn(R.start),end:void 0}}function d(T){return R;function R(D){T&&T.call(this,D),f.call(this,D)}}function f(T,R){let D=this.stack.pop(),J=this.tokenStack.pop();if(J)J[0].type!==T.type&&(R?R.call(this,T,J[0]):(J[1]||Ld).call(this,T,J[0]));else throw new Error("Cannot close `"+T.type+"` ("+pn({start:T.start,end:T.end})+"): it\u2019s not open");D.position.end=gn(T.end)}function m(){return In(this.stack.pop())}function p(){this.data.expectingFirstListItemValue=!0}function y(T){if(this.data.expectingFirstListItemValue){let R=this.stack[this.stack.length-2];R.start=Number.parseInt(this.sliceSerialize(T),10),this.data.expectingFirstListItemValue=void 0}}function g(){let T=this.resume(),R=this.stack[this.stack.length-1];R.lang=T}function A(){let T=this.resume(),R=this.stack[this.stack.length-1];R.meta=T}function P(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function S(){let T=this.resume(),R=this.stack[this.stack.length-1];R.value=T.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function E(){let T=this.resume(),R=this.stack[this.stack.length-1];R.value=T.replace(/(\r?\n|\r)$/g,"")}function _(T){let R=this.resume(),D=this.stack[this.stack.length-1];D.label=R,D.identifier=tn(this.sliceSerialize(T)).toLowerCase()}function F(){let T=this.resume(),R=this.stack[this.stack.length-1];R.title=T}function z(){let T=this.resume(),R=this.stack[this.stack.length-1];R.url=T}function W(T){let R=this.stack[this.stack.length-1];if(!R.depth){let D=this.sliceSerialize(T).length;R.depth=D}}function C(){this.data.setextHeadingSlurpLineEnding=!0}function te(T){let R=this.stack[this.stack.length-1];R.depth=this.sliceSerialize(T).codePointAt(0)===61?1:2}function X(){this.data.setextHeadingSlurpLineEnding=void 0}function Z(T){let D=this.stack[this.stack.length-1].children,J=D[D.length-1];(!J||J.type!=="text")&&(J=Ue(),J.position={start:gn(T.start),end:void 0},D.push(J)),this.stack.push(J)}function oe(T){let R=this.stack.pop();R.value+=this.sliceSerialize(T),R.position.end=gn(T.end)}function de(T){let R=this.stack[this.stack.length-1];if(this.data.atHardBreak){let D=R.children[R.children.length-1];D.position.end=gn(T.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&e.canContainEols.includes(R.type)&&(Z.call(this,T),oe.call(this,T))}function re(){this.data.atHardBreak=!0}function Y(){let T=this.resume(),R=this.stack[this.stack.length-1];R.value=T}function q(){let T=this.resume(),R=this.stack[this.stack.length-1];R.value=T}function U(){let T=this.resume(),R=this.stack[this.stack.length-1];R.value=T}function ie(){let T=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";T.type+="Reference",T.referenceType=R,delete T.url,delete T.title}else delete T.identifier,delete T.label;this.data.referenceType=void 0}function ge(){let T=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";T.type+="Reference",T.referenceType=R,delete T.url,delete T.title}else delete T.identifier,delete T.label;this.data.referenceType=void 0}function ye(T){let R=this.sliceSerialize(T),D=this.stack[this.stack.length-2];D.label=hs(R),D.identifier=tn(R).toLowerCase()}function Ze(){let T=this.stack[this.stack.length-1],R=this.resume(),D=this.stack[this.stack.length-1];if(this.data.inReference=!0,D.type==="link"){let J=T.children;D.children=J}else D.alt=R}function x(){let T=this.resume(),R=this.stack[this.stack.length-1];R.url=T}function De(){let T=this.resume(),R=this.stack[this.stack.length-1];R.title=T}function Oe(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function ze(T){let R=this.resume(),D=this.stack[this.stack.length-1];D.label=R,D.identifier=tn(this.sliceSerialize(T)).toLowerCase(),this.data.referenceType="full"}function Nt(T){this.data.characterReferenceType=T.type}function sn(T){let R=this.sliceSerialize(T),D=this.data.characterReferenceType,J;D?(J=ns(R,D==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):J=ar(R);let ae=this.stack[this.stack.length-1];ae.value+=J}function An(T){let R=this.stack.pop();R.position.end=gn(T.end)}function Ce(T){oe.call(this,T);let R=this.stack[this.stack.length-1];R.url=this.sliceSerialize(T)}function En(T){oe.call(this,T);let R=this.stack[this.stack.length-1];R.url="mailto:"+this.sliceSerialize(T)}function kt(){return{type:"blockquote",children:[]}}function Bt(){return{type:"code",lang:null,meta:null,value:""}}function on(){return{type:"inlineCode",value:""}}function Pr(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Ko(){return{type:"emphasis",children:[]}}function ki(){return{type:"heading",depth:0,children:[]}}function xi(){return{type:"break"}}function Ai(){return{type:"html",value:""}}function Ei(){return{type:"image",title:null,url:"",alt:null}}function We(){return{type:"link",title:null,url:"",children:[]}}function Un(T){return{type:"list",ordered:T.type==="listOrdered",start:null,spread:T._spread,children:[]}}function Xo(T){return{type:"listItem",spread:T._spread,checked:null,children:[]}}function Zo(){return{type:"paragraph",children:[]}}function vi(){return{type:"strong",children:[]}}function Ue(){return{type:"text",value:""}}function Ci(){return{type:"thematicBreak"}}}function gn(t){return{line:t.line,column:t.column,offset:t.offset}}function Nd(t,e){let n=-1;for(;++n<e.length;){let r=e[n];Array.isArray(r)?Nd(t,r):Gk(t,r)}}function Gk(t,e){let n;for(n in e)if(Md.call(e,n))switch(n){case"canContainEols":{let r=e[n];r&&t[n].push(...r);break}case"transforms":{let r=e[n];r&&t[n].push(...r);break}case"enter":case"exit":{let r=e[n];r&&Object.assign(t[n],r);break}}}function Ld(t,e){throw t?new Error("Cannot close `"+t.type+"` ("+pn({start:t.start,end:t.end})+"): a different token (`"+e.type+"`, "+pn({start:e.start,end:e.end})+") is open"):new Error("Cannot close document, a token (`"+e.type+"`, "+pn({start:e.start,end:e.end})+") is still open")}function cr(t){let e=this;e.parser=n;function n(r){return el(r,{...e.data("settings"),...t,extensions:e.data("micromarkExtensions")||[],mdastExtensions:e.data("fromMarkdownExtensions")||[]})}}var Bd={}.hasOwnProperty;function $d(t,e){let n=e||{};function r(s,...o){let l=r.invalid,u=r.handlers;if(s&&Bd.call(s,t)){let d=String(s[t]);l=Bd.call(u,d)?u[d]:r.unknown}if(l)return l.call(this,s,...o)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}var jk={}.hasOwnProperty;function tl(t,e){let n=-1,r;if(e.extensions)for(;++n<e.extensions.length;)tl(t,e.extensions[n]);for(r in e)if(jk.call(e,r))switch(r){case"extensions":break;case"unsafe":{Yd(t[r],e[r]);break}case"join":{Yd(t[r],e[r]);break}case"handlers":{Kk(t[r],e[r]);break}default:t.options[r]=e[r]}return t}function Yd(t,e){e&&t.push(...e)}function Kk(t,e){e&&Object.assign(t,e)}function Vd(t,e,n,r){let s=n.enter("blockquote"),o=n.createTracker(r);o.move("> "),o.shift(2);let l=n.indentLines(n.containerFlow(t,o.current()),Xk);return s(),l}function Xk(t,e,n){return">"+(n?"":" ")+t}function ms(t,e){return zd(t,e.inConstruct,!0)&&!zd(t,e.notInConstruct,!1)}function zd(t,e,n){if(typeof e=="string"&&(e=[e]),!e||e.length===0)return n;let r=-1;for(;++r<e.length;)if(t.includes(e[r]))return!0;return!1}function nl(t,e,n,r){let s=-1;for(;++s<n.unsafe.length;)if(n.unsafe[s].character===`
`&&ms(n.stack,n.unsafe[s]))return/[ \t]/.test(r.before)?"":" ";return`\\
`}function Wd(t,e){let n=String(t),r=n.indexOf(e),s=r,o=0,l=0;if(typeof e!="string")throw new TypeError("Expected substring");for(;r!==-1;)r===s?++o>l&&(l=o):o=1,s=r+e.length,r=n.indexOf(e,s);return l}function Jr(t,e){return!!(e.options.fences===!1&&t.value&&!t.lang&&/[^ \r\n]/.test(t.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(t.value))}function Ud(t){let e=t.options.fence||"`";if(e!=="`"&&e!=="~")throw new Error("Cannot serialize code with `"+e+"` for `options.fence`, expected `` ` `` or `~`");return e}function Hd(t,e,n,r){let s=Ud(n),o=t.value||"",l=s==="`"?"GraveAccent":"Tilde";if(Jr(t,n)){let p=n.enter("codeIndented"),y=n.indentLines(o,Zk);return p(),y}let u=n.createTracker(r),d=s.repeat(Math.max(Wd(o,s)+1,3)),f=n.enter("codeFenced"),m=u.move(d);if(t.lang){let p=n.enter(`codeFencedLang${l}`);m+=u.move(n.safe(t.lang,{before:m,after:" ",encode:["`"],...u.current()})),p()}if(t.lang&&t.meta){let p=n.enter(`codeFencedMeta${l}`);m+=u.move(" "),m+=u.move(n.safe(t.meta,{before:m,after:`
`,encode:["`"],...u.current()})),p()}return m+=u.move(`
`),o&&(m+=u.move(o+`
`)),m+=u.move(d),f(),m}function Zk(t,e,n){return(n?"":"    ")+t}function dr(t){let e=t.options.quote||'"';if(e!=='"'&&e!=="'")throw new Error("Cannot serialize title with `"+e+"` for `options.quote`, expected `\"`, or `'`");return e}function qd(t,e,n,r){let s=dr(n),o=s==='"'?"Quote":"Apostrophe",l=n.enter("definition"),u=n.enter("label"),d=n.createTracker(r),f=d.move("[");return f+=d.move(n.safe(n.associationId(t),{before:f,after:"]",...d.current()})),f+=d.move("]: "),u(),!t.url||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),f+=d.move("<"),f+=d.move(n.safe(t.url,{before:f,after:">",...d.current()})),f+=d.move(">")):(u=n.enter("destinationRaw"),f+=d.move(n.safe(t.url,{before:f,after:t.title?" ":`
`,...d.current()}))),u(),t.title&&(u=n.enter(`title${o}`),f+=d.move(" "+s),f+=d.move(n.safe(t.title,{before:f,after:s,...d.current()})),f+=d.move(s),u()),l(),f}function Gd(t){let e=t.options.emphasis||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize emphasis with `"+e+"` for `options.emphasis`, expected `*`, or `_`");return e}function at(t){return"&#x"+t.toString(16).toUpperCase()+";"}function fr(t,e,n){let r=lr(t),s=lr(e);return r===void 0?s===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:s===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:r===1?s===void 0?{inside:!1,outside:!1}:s===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:s===void 0?{inside:!1,outside:!1}:s===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}rl.peek=Jk;function rl(t,e,n,r){let s=Gd(n),o=n.enter("emphasis"),l=n.createTracker(r),u=l.move(s),d=l.move(n.containerPhrasing(t,{after:s,before:u,...l.current()})),f=d.charCodeAt(0),m=fr(r.before.charCodeAt(r.before.length-1),f,s);m.inside&&(d=at(f)+d.slice(1));let p=d.charCodeAt(d.length-1),y=fr(r.after.charCodeAt(0),p,s);y.inside&&(d=d.slice(0,-1)+at(p));let g=l.move(s);return o(),n.attentionEncodeSurroundingInfo={after:y.outside,before:m.outside},u+d+g}function Jk(t,e,n){return n.options.emphasis||"*"}var hr=function(t){if(t==null)return nx;if(typeof t=="function")return ps(t);if(typeof t=="object")return Array.isArray(t)?Qk(t):ex(t);if(typeof t=="string")return tx(t);throw new Error("Expected function, string, or object as test")};function Qk(t){let e=[],n=-1;for(;++n<t.length;)e[n]=hr(t[n]);return ps(r);function r(...s){let o=-1;for(;++o<e.length;)if(e[o].apply(this,s))return!0;return!1}}function ex(t){let e=t;return ps(n);function n(r){let s=r,o;for(o in t)if(s[o]!==e[o])return!1;return!0}}function tx(t){return ps(e);function e(n){return n&&n.type===t}}function ps(t){return e;function e(n,r,s){return!!(rx(n)&&t.call(this,n,typeof r=="number"?r:void 0,s||void 0))}}function nx(){return!0}function rx(t){return t!==null&&typeof t=="object"&&"type"in t}function jd(t){return"\x1B[33m"+t+"\x1B[39m"}var Kd=[],gs=!0,Ln=!1,ws="skip";function il(t,e,n,r){let s;typeof e=="function"&&typeof n!="function"?(r=n,n=e):s=e;let o=hr(s),l=r?-1:1;u(t,void 0,[])();function u(d,f,m){let p=d&&typeof d=="object"?d:{};if(typeof p.type=="string"){let g=typeof p.tagName=="string"?p.tagName:typeof p.name=="string"?p.name:void 0;Object.defineProperty(y,"name",{value:"node ("+jd(d.type+(g?"<"+g+">":""))+")"})}return y;function y(){let g=Kd,A,P,S;if((!e||o(d,f,m[m.length-1]||void 0))&&(g=ix(n(d,m)),g[0]===Ln))return g;if("children"in d&&d.children){let E=d;if(E.children&&g[0]!==ws)for(P=(r?E.children.length:-1)+l,S=m.concat(E);P>-1&&P<E.children.length;){let _=E.children[P];if(A=u(_,P,S)(),A[0]===Ln)return A;P=typeof A[1]=="number"?A[1]:P+l}}return g}}}function ix(t){return Array.isArray(t)?t:typeof t=="number"?[gs,t]:t==null?Kd:[t]}function sl(t,e,n,r){let s,o,l;typeof e=="function"&&typeof n!="function"?(o=void 0,l=e,s=n):(o=e,l=n,s=r),il(t,o,u,s);function u(d,f){let m=f[f.length-1],p=m?m.children.indexOf(d):void 0;return l(d,p,m)}}function ys(t,e){let n=!1;return sl(t,function(r){if("value"in r&&/\r?\n|\r/.test(r.value)||r.type==="break")return n=!0,Ln}),!!((!t.depth||t.depth<3)&&In(t)&&(e.options.setext||n))}function Xd(t,e,n,r){let s=Math.max(Math.min(6,t.depth||1),1),o=n.createTracker(r);if(ys(t,n)){let m=n.enter("headingSetext"),p=n.enter("phrasing"),y=n.containerPhrasing(t,{...o.current(),before:`
`,after:`
`});return p(),m(),y+`
`+(s===1?"=":"-").repeat(y.length-(Math.max(y.lastIndexOf("\r"),y.lastIndexOf(`
`))+1))}let l="#".repeat(s),u=n.enter("headingAtx"),d=n.enter("phrasing");o.move(l+" ");let f=n.containerPhrasing(t,{before:"# ",after:`
`,...o.current()});return/^[\t ]/.test(f)&&(f=at(f.charCodeAt(0))+f.slice(1)),f=f?l+" "+f:l,n.options.closeAtx&&(f+=" "+l),d(),u(),f}ol.peek=sx;function ol(t){return t.value||""}function sx(){return"<"}al.peek=ox;function al(t,e,n,r){let s=dr(n),o=s==='"'?"Quote":"Apostrophe",l=n.enter("image"),u=n.enter("label"),d=n.createTracker(r),f=d.move("![");return f+=d.move(n.safe(t.alt,{before:f,after:"]",...d.current()})),f+=d.move("]("),u(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),f+=d.move("<"),f+=d.move(n.safe(t.url,{before:f,after:">",...d.current()})),f+=d.move(">")):(u=n.enter("destinationRaw"),f+=d.move(n.safe(t.url,{before:f,after:t.title?" ":")",...d.current()}))),u(),t.title&&(u=n.enter(`title${o}`),f+=d.move(" "+s),f+=d.move(n.safe(t.title,{before:f,after:s,...d.current()})),f+=d.move(s),u()),f+=d.move(")"),l(),f}function ox(){return"!"}ll.peek=ax;function ll(t,e,n,r){let s=t.referenceType,o=n.enter("imageReference"),l=n.enter("label"),u=n.createTracker(r),d=u.move("!["),f=n.safe(t.alt,{before:d,after:"]",...u.current()});d+=u.move(f+"]["),l();let m=n.stack;n.stack=[],l=n.enter("reference");let p=n.safe(n.associationId(t),{before:d,after:"]",...u.current()});return l(),n.stack=m,o(),s==="full"||!f||f!==p?d+=u.move(p+"]"):s==="shortcut"?d=d.slice(0,-1):d+=u.move("]"),d}function ax(){return"!"}ul.peek=lx;function ul(t,e,n){let r=t.value||"",s="`",o=-1;for(;new RegExp("(^|[^`])"+s+"([^`]|$)").test(r);)s+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){let l=n.unsafe[o],u=n.compilePattern(l),d;if(l.atBreak)for(;d=u.exec(r);){let f=d.index;r.charCodeAt(f)===10&&r.charCodeAt(f-1)===13&&f--,r=r.slice(0,f)+" "+r.slice(d.index+1)}}return s+r+s}function lx(){return"`"}function cl(t,e){let n=In(t);return!!(!e.options.resourceLink&&t.url&&!t.title&&t.children&&t.children.length===1&&t.children[0].type==="text"&&(n===t.url||"mailto:"+n===t.url)&&/^[a-z][a-z+.-]+:/i.test(t.url)&&!/[\0- <>\u007F]/.test(t.url))}dl.peek=ux;function dl(t,e,n,r){let s=dr(n),o=s==='"'?"Quote":"Apostrophe",l=n.createTracker(r),u,d;if(cl(t,n)){let m=n.stack;n.stack=[],u=n.enter("autolink");let p=l.move("<");return p+=l.move(n.containerPhrasing(t,{before:p,after:">",...l.current()})),p+=l.move(">"),u(),n.stack=m,p}u=n.enter("link"),d=n.enter("label");let f=l.move("[");return f+=l.move(n.containerPhrasing(t,{before:f,after:"](",...l.current()})),f+=l.move("]("),d(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(d=n.enter("destinationLiteral"),f+=l.move("<"),f+=l.move(n.safe(t.url,{before:f,after:">",...l.current()})),f+=l.move(">")):(d=n.enter("destinationRaw"),f+=l.move(n.safe(t.url,{before:f,after:t.title?" ":")",...l.current()}))),d(),t.title&&(d=n.enter(`title${o}`),f+=l.move(" "+s),f+=l.move(n.safe(t.title,{before:f,after:s,...l.current()})),f+=l.move(s),d()),f+=l.move(")"),u(),f}function ux(t,e,n){return cl(t,n)?"<":"["}fl.peek=cx;function fl(t,e,n,r){let s=t.referenceType,o=n.enter("linkReference"),l=n.enter("label"),u=n.createTracker(r),d=u.move("["),f=n.containerPhrasing(t,{before:d,after:"]",...u.current()});d+=u.move(f+"]["),l();let m=n.stack;n.stack=[],l=n.enter("reference");let p=n.safe(n.associationId(t),{before:d,after:"]",...u.current()});return l(),n.stack=m,o(),s==="full"||!f||f!==p?d+=u.move(p+"]"):s==="shortcut"?d=d.slice(0,-1):d+=u.move("]"),d}function cx(){return"["}function mr(t){let e=t.options.bullet||"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bullet`, expected `*`, `+`, or `-`");return e}function Zd(t){let e=mr(t),n=t.options.bulletOther;if(!n)return e==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===e)throw new Error("Expected `bullet` (`"+e+"`) and `bulletOther` (`"+n+"`) to be different");return n}function Jd(t){let e=t.options.bulletOrdered||".";if(e!=="."&&e!==")")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOrdered`, expected `.` or `)`");return e}function bs(t){let e=t.options.rule||"*";if(e!=="*"&&e!=="-"&&e!=="_")throw new Error("Cannot serialize rules with `"+e+"` for `options.rule`, expected `*`, `-`, or `_`");return e}function Qd(t,e,n,r){let s=n.enter("list"),o=n.bulletCurrent,l=t.ordered?Jd(n):mr(n),u=t.ordered?l==="."?")":".":Zd(n),d=e&&n.bulletLastUsed?l===n.bulletLastUsed:!1;if(!t.ordered){let m=t.children?t.children[0]:void 0;if((l==="*"||l==="-")&&m&&(!m.children||!m.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(d=!0),bs(n)===l&&m){let p=-1;for(;++p<t.children.length;){let y=t.children[p];if(y&&y.type==="listItem"&&y.children&&y.children[0]&&y.children[0].type==="thematicBreak"){d=!0;break}}}}d&&(l=u),n.bulletCurrent=l;let f=n.containerFlow(t,r);return n.bulletLastUsed=l,n.bulletCurrent=o,s(),f}function ef(t){let e=t.options.listItemIndent||"one";if(e!=="tab"&&e!=="one"&&e!=="mixed")throw new Error("Cannot serialize items with `"+e+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return e}function tf(t,e,n,r){let s=ef(n),o=n.bulletCurrent||mr(n);e&&e.type==="list"&&e.ordered&&(o=(typeof e.start=="number"&&e.start>-1?e.start:1)+(n.options.incrementListMarker===!1?0:e.children.indexOf(t))+o);let l=o.length+1;(s==="tab"||s==="mixed"&&(e&&e.type==="list"&&e.spread||t.spread))&&(l=Math.ceil(l/4)*4);let u=n.createTracker(r);u.move(o+" ".repeat(l-o.length)),u.shift(l);let d=n.enter("listItem"),f=n.indentLines(n.containerFlow(t,u.current()),m);return d(),f;function m(p,y,g){return y?(g?"":" ".repeat(l))+p:(g?o:o+" ".repeat(l-o.length))+p}}function nf(t,e,n,r){let s=n.enter("paragraph"),o=n.enter("phrasing"),l=n.containerPhrasing(t,r);return o(),s(),l}var hl=hr(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function rf(t,e,n,r){return(t.children.some(function(l){return hl(l)})?n.containerPhrasing:n.containerFlow).call(n,t,r)}function sf(t){let e=t.options.strong||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize strong with `"+e+"` for `options.strong`, expected `*`, or `_`");return e}ml.peek=dx;function ml(t,e,n,r){let s=sf(n),o=n.enter("strong"),l=n.createTracker(r),u=l.move(s+s),d=l.move(n.containerPhrasing(t,{after:s,before:u,...l.current()})),f=d.charCodeAt(0),m=fr(r.before.charCodeAt(r.before.length-1),f,s);m.inside&&(d=at(f)+d.slice(1));let p=d.charCodeAt(d.length-1),y=fr(r.after.charCodeAt(0),p,s);y.inside&&(d=d.slice(0,-1)+at(p));let g=l.move(s+s);return o(),n.attentionEncodeSurroundingInfo={after:y.outside,before:m.outside},u+d+g}function dx(t,e,n){return n.options.strong||"*"}function of(t,e,n,r){return n.safe(t.value,r)}function af(t){let e=t.options.ruleRepetition||3;if(e<3)throw new Error("Cannot serialize rules with repetition `"+e+"` for `options.ruleRepetition`, expected `3` or more");return e}function lf(t,e,n){let r=(bs(n)+(n.options.ruleSpaces?" ":"")).repeat(af(n));return n.options.ruleSpaces?r.slice(0,-1):r}var uf={blockquote:Vd,break:nl,code:Hd,definition:qd,emphasis:rl,hardBreak:nl,heading:Xd,html:ol,image:al,imageReference:ll,inlineCode:ul,link:dl,linkReference:fl,list:Qd,listItem:tf,paragraph:nf,root:rf,strong:ml,text:of,thematicBreak:lf};var cf=[fx];function fx(t,e,n,r){if(e.type==="code"&&Jr(e,r)&&(t.type==="list"||t.type===e.type&&Jr(t,r)))return!1;if("spread"in n&&typeof n.spread=="boolean")return t.type==="paragraph"&&(t.type===e.type||e.type==="definition"||e.type==="heading"&&ys(e,r))?void 0:n.spread?1:0}var Mn=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"],df=[{character:"	",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",before:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"\r",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:`
`,inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:" ",after:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",before:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"!",after:"\\[",inConstruct:"phrasing",notInConstruct:Mn},{character:'"',inConstruct:"titleQuote"},{atBreak:!0,character:"#"},{character:"#",inConstruct:"headingAtx",after:`(?:[\r
]|$)`},{character:"&",after:"[#A-Za-z]",inConstruct:"phrasing"},{character:"'",inConstruct:"titleApostrophe"},{character:"(",inConstruct:"destinationRaw"},{before:"\\]",character:"(",inConstruct:"phrasing",notInConstruct:Mn},{atBreak:!0,before:"\\d+",character:")"},{character:")",inConstruct:"destinationRaw"},{atBreak:!0,character:"*",after:`(?:[ 	\r
*])`},{character:"*",inConstruct:"phrasing",notInConstruct:Mn},{atBreak:!0,character:"+",after:`(?:[ 	\r
])`},{atBreak:!0,character:"-",after:`(?:[ 	\r
-])`},{atBreak:!0,before:"\\d+",character:".",after:`(?:[ 	\r
]|$)`},{atBreak:!0,character:"<",after:"[!/?A-Za-z]"},{character:"<",after:"[!/?A-Za-z]",inConstruct:"phrasing",notInConstruct:Mn},{character:"<",inConstruct:"destinationLiteral"},{atBreak:!0,character:"="},{atBreak:!0,character:">"},{character:">",inConstruct:"destinationLiteral"},{atBreak:!0,character:"["},{character:"[",inConstruct:"phrasing",notInConstruct:Mn},{character:"[",inConstruct:["label","reference"]},{character:"\\",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"]",inConstruct:["label","reference"]},{atBreak:!0,character:"_"},{character:"_",inConstruct:"phrasing",notInConstruct:Mn},{atBreak:!0,character:"`"},{character:"`",inConstruct:["codeFencedLangGraveAccent","codeFencedMetaGraveAccent"]},{character:"`",inConstruct:"phrasing",notInConstruct:Mn},{atBreak:!0,character:"~"}];function ff(t){return t.label||!t.identifier?t.label||"":hs(t.identifier)}function hf(t){if(!t._compiled){let e=(t.atBreak?"[\\r\\n][\\t ]*":"")+(t.before?"(?:"+t.before+")":"");t._compiled=new RegExp((e?"("+e+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(t.character)?"\\":"")+t.character+(t.after?"(?:"+t.after+")":""),"g")}return t._compiled}function mf(t,e,n){let r=e.indexStack,s=t.children||[],o=[],l=-1,u=n.before,d;r.push(-1);let f=e.createTracker(n);for(;++l<s.length;){let m=s[l],p;if(r[r.length-1]=l,l+1<s.length){let A=e.handle.handlers[s[l+1].type];A&&A.peek&&(A=A.peek),p=A?A(s[l+1],t,e,{before:"",after:"",...f.current()}).charAt(0):""}else p=n.after;o.length>0&&(u==="\r"||u===`
`)&&m.type==="html"&&(o[o.length-1]=o[o.length-1].replace(/(\r?\n|\r)$/," "),u=" ",f=e.createTracker(n),f.move(o.join("")));let y=e.handle(m,t,e,{...f.current(),after:p,before:u});d&&d===y.slice(0,1)&&(y=at(d.charCodeAt(0))+y.slice(1));let g=e.attentionEncodeSurroundingInfo;e.attentionEncodeSurroundingInfo=void 0,d=void 0,g&&(o.length>0&&g.before&&u===o[o.length-1].slice(-1)&&(o[o.length-1]=o[o.length-1].slice(0,-1)+at(u.charCodeAt(0))),g.after&&(d=p)),f.move(y),o.push(y),u=y.slice(-1)}return r.pop(),o.join("")}function pf(t,e,n){let r=e.indexStack,s=t.children||[],o=e.createTracker(n),l=[],u=-1;for(r.push(-1);++u<s.length;){let d=s[u];r[r.length-1]=u,l.push(o.move(e.handle(d,t,e,{before:`
`,after:`
`,...o.current()}))),d.type!=="list"&&(e.bulletLastUsed=void 0),u<s.length-1&&l.push(o.move(hx(d,s[u+1],t,e)))}return r.pop(),l.join("")}function hx(t,e,n,r){let s=r.join.length;for(;s--;){let o=r.join[s](t,e,n,r);if(o===!0||o===1)break;if(typeof o=="number")return`
`.repeat(1+o);if(o===!1)return`

<!---->

`}return`

`}var mx=/\r?\n|\r/g;function gf(t,e){let n=[],r=0,s=0,o;for(;o=mx.exec(t);)l(t.slice(r,o.index)),n.push(o[0]),r=o.index+o[0].length,s++;return l(t.slice(r)),n.join("");function l(u){n.push(e(u,s,!u))}}function yf(t,e,n){let r=(n.before||"")+(e||"")+(n.after||""),s=[],o=[],l={},u=-1;for(;++u<t.unsafe.length;){let m=t.unsafe[u];if(!ms(t.stack,m))continue;let p=t.compilePattern(m),y;for(;y=p.exec(r);){let g="before"in m||!!m.atBreak,A="after"in m,P=y.index+(g?y[1].length:0);s.includes(P)?(l[P].before&&!g&&(l[P].before=!1),l[P].after&&!A&&(l[P].after=!1)):(s.push(P),l[P]={before:g,after:A})}}s.sort(px);let d=n.before?n.before.length:0,f=r.length-(n.after?n.after.length:0);for(u=-1;++u<s.length;){let m=s[u];m<d||m>=f||m+1<f&&s[u+1]===m+1&&l[m].after&&!l[m+1].before&&!l[m+1].after||s[u-1]===m-1&&l[m].before&&!l[m-1].before&&!l[m-1].after||(d!==m&&o.push(wf(r.slice(d,m),"\\")),d=m,/[!-/:-@[-`{-~]/.test(r.charAt(m))&&(!n.encode||!n.encode.includes(r.charAt(m)))?o.push("\\"):(o.push(at(r.charCodeAt(m))),d++))}return o.push(wf(r.slice(d,f),n.after)),o.join("")}function px(t,e){return t-e}function wf(t,e){let n=/\\(?=[!-/:-@[-`{-~])/g,r=[],s=[],o=t+e,l=-1,u=0,d;for(;d=n.exec(o);)r.push(d.index);for(;++l<r.length;)u!==r[l]&&s.push(t.slice(u,r[l])),s.push("\\"),u=r[l];return s.push(t.slice(u)),s.join("")}function bf(t){let e=t||{},n=e.now||{},r=e.lineShift||0,s=n.line||1,o=n.column||1;return{move:d,current:l,shift:u};function l(){return{now:{line:s,column:o},lineShift:r}}function u(f){r+=f}function d(f){let m=f||"",p=m.split(/\r?\n|\r/g),y=p[p.length-1];return s+=p.length-1,o=p.length===1?o+y.length:1+y.length+r,m}}function pl(t,e){let n=e||{},r={associationId:ff,containerPhrasing:bx,containerFlow:kx,createTracker:bf,compilePattern:hf,enter:o,handlers:{...uf},handle:void 0,indentLines:gf,indexStack:[],join:[...cf],options:{},safe:xx,stack:[],unsafe:[...df]};tl(r,n),r.options.tightDefinitions&&r.join.push(yx),r.handle=$d("type",{invalid:gx,unknown:wx,handlers:r.handlers});let s=r.handle(t,void 0,r,{before:`
`,after:`
`,now:{line:1,column:1},lineShift:0});return s&&s.charCodeAt(s.length-1)!==10&&s.charCodeAt(s.length-1)!==13&&(s+=`
`),s;function o(l){return r.stack.push(l),u;function u(){r.stack.pop()}}}function gx(t){throw new Error("Cannot handle value `"+t+"`, expected node")}function wx(t){let e=t;throw new Error("Cannot handle unknown node `"+e.type+"`")}function yx(t,e){if(t.type==="definition"&&t.type===e.type)return 0}function bx(t,e){return mf(t,this,e)}function kx(t,e){return pf(t,this,e)}function xx(t,e){return yf(this,t,e)}function ks(t){let e=this;e.compiler=n;function n(r){return pl(r,{...e.data("settings"),...t,extensions:e.data("toMarkdownExtensions")||[]})}}function gl(t){if(t)throw t}var vs=Ne(_f(),1);function Qr(t){if(typeof t!="object"||t===null)return!1;let e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)}function wl(){let t=[],e={run:n,use:r};return e;function n(...s){let o=-1,l=s.pop();if(typeof l!="function")throw new TypeError("Expected function as last argument, not "+l);u(null,...s);function u(d,...f){let m=t[++o],p=-1;if(d){l(d);return}for(;++p<s.length;)(f[p]===null||f[p]===void 0)&&(f[p]=s[p]);s=f,m?Ff(m,u)(...f):l(null,...f)}}function r(s){if(typeof s!="function")throw new TypeError("Expected `middelware` to be a function, not "+s);return t.push(s),e}}function Ff(t,e){let n;return r;function r(...l){let u=t.length>l.length,d;u&&l.push(s);try{d=t.apply(this,l)}catch(f){let m=f;if(u&&n)throw m;return s(m)}u||(d&&d.then&&typeof d.then=="function"?d.then(o,s):d instanceof Error?s(d):o(d))}function s(l,...u){n||(n=!0,e(l,...u))}function o(l){s(null,l)}}var Pe=class extends Error{constructor(e,n,r){super(),typeof n=="string"&&(r=n,n=void 0);let s="",o={},l=!1;if(n&&("line"in n&&"column"in n?o={place:n}:"start"in n&&"end"in n?o={place:n}:"type"in n?o={ancestors:[n],place:n.position}:o={...n}),typeof e=="string"?s=e:!o.cause&&e&&(l=!0,s=e.message,o.cause=e),!o.ruleId&&!o.source&&typeof r=="string"){let d=r.indexOf(":");d===-1?o.ruleId=r:(o.source=r.slice(0,d),o.ruleId=r.slice(d+1))}if(!o.place&&o.ancestors&&o.ancestors){let d=o.ancestors[o.ancestors.length-1];d&&(o.place=d.position)}let u=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=u?u.column:void 0,this.fatal=void 0,this.file,this.message=s,this.line=u?u.line:void 0,this.name=pn(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=l&&o.cause&&typeof o.cause.stack=="string"?o.cause.stack:"",this.actual,this.expected,this.note,this.url}};Pe.prototype.file="";Pe.prototype.name="";Pe.prototype.reason="";Pe.prototype.message="";Pe.prototype.stack="";Pe.prototype.column=void 0;Pe.prototype.line=void 0;Pe.prototype.ancestors=void 0;Pe.prototype.cause=void 0;Pe.prototype.fatal=void 0;Pe.prototype.place=void 0;Pe.prototype.ruleId=void 0;Pe.prototype.source=void 0;var mt=Ne(require("node:path"),1);var yl=Ne(require("node:process"),1);var bl=require("node:url");function As(t){return!!(t!==null&&typeof t=="object"&&"href"in t&&t.href&&"protocol"in t&&t.protocol&&t.auth===void 0)}var kl=["history","path","basename","stem","extname","dirname"],ei=class{constructor(e){let n;e?As(e)?n={path:e}:typeof e=="string"||Ax(e)?n={value:e}:n=e:n={},this.cwd="cwd"in n?"":yl.default.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<kl.length;){let o=kl[r];o in n&&n[o]!==void 0&&n[o]!==null&&(this[o]=o==="history"?[...n[o]]:n[o])}let s;for(s in n)kl.includes(s)||(this[s]=n[s])}get basename(){return typeof this.path=="string"?mt.default.basename(this.path):void 0}set basename(e){Al(e,"basename"),xl(e,"basename"),this.path=mt.default.join(this.dirname||"",e)}get dirname(){return typeof this.path=="string"?mt.default.dirname(this.path):void 0}set dirname(e){Pf(this.basename,"dirname"),this.path=mt.default.join(e||"",this.basename)}get extname(){return typeof this.path=="string"?mt.default.extname(this.path):void 0}set extname(e){if(xl(e,"extname"),Pf(this.dirname,"extname"),e){if(e.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=mt.default.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){As(e)&&(e=(0,bl.fileURLToPath)(e)),Al(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return typeof this.path=="string"?mt.default.basename(this.path,this.extname):void 0}set stem(e){Al(e,"stem"),xl(e,"stem"),this.path=mt.default.join(this.dirname||"",e+(this.extname||""))}fail(e,n,r){let s=this.message(e,n,r);throw s.fatal=!0,s}info(e,n,r){let s=this.message(e,n,r);return s.fatal=void 0,s}message(e,n,r){let s=new Pe(e,n,r);return this.path&&(s.name=this.path+":"+s.name,s.file=this.path),s.fatal=!1,this.messages.push(s),s}toString(e){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(e||void 0).decode(this.value)}};function xl(t,e){if(t&&t.includes(mt.default.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+mt.default.sep+"`")}function Al(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function Pf(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}function Ax(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var If=function(t){let r=this.constructor.prototype,s=r[t],o=function(){return s.apply(o,arguments)};return Object.setPrototypeOf(o,r),o};var Ex={}.hasOwnProperty,Sl=class t extends If{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=wl()}copy(){let e=new t,n=-1;for(;++n<this.attachers.length;){let r=this.attachers[n];e.use(...r)}return e.data((0,vs.default)(!0,{},this.namespace)),e}data(e,n){return typeof e=="string"?arguments.length===2?(Cl("data",this.frozen),this.namespace[e]=n,this):Ex.call(this.namespace,e)&&this.namespace[e]||void 0:e?(Cl("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;let e=this;for(;++this.freezeIndex<this.attachers.length;){let[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);let s=n.call(e,...r);typeof s=="function"&&this.transformers.use(s)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let n=Es(e),r=this.parser||this.Parser;return El("parse",r),r(String(n),n)}process(e,n){let r=this;return this.freeze(),El("process",this.parser||this.Parser),vl("process",this.compiler||this.Compiler),n?s(void 0,n):new Promise(s);function s(o,l){let u=Es(e),d=r.parse(u);r.run(d,u,function(m,p,y){if(m||!p||!y)return f(m);let g=p,A=r.stringify(g,y);Cx(A)?y.value=A:y.result=A,f(m,y)});function f(m,p){m||!p?l(m):o?o(p):n(void 0,p)}}}processSync(e){let n=!1,r;return this.freeze(),El("processSync",this.parser||this.Parser),vl("processSync",this.compiler||this.Compiler),this.process(e,s),Df("processSync","process",n),r;function s(o,l){n=!0,gl(o),r=l}}run(e,n,r){Rf(e),this.freeze();let s=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?o(void 0,r):new Promise(o);function o(l,u){let d=Es(n);s.run(e,d,f);function f(m,p,y){let g=p||e;m?u(m):l?l(g):r(void 0,g,y)}}}runSync(e,n){let r=!1,s;return this.run(e,n,o),Df("runSync","run",r),s;function o(l,u){gl(l),s=u,r=!0}}stringify(e,n){this.freeze();let r=Es(n),s=this.compiler||this.Compiler;return vl("stringify",s),Rf(e),s(e,r)}use(e,...n){let r=this.attachers,s=this.namespace;if(Cl("use",this.frozen),e!=null)if(typeof e=="function")d(e,n);else if(typeof e=="object")Array.isArray(e)?u(e):l(e);else throw new TypeError("Expected usable value, not `"+e+"`");return this;function o(f){if(typeof f=="function")d(f,[]);else if(typeof f=="object")if(Array.isArray(f)){let[m,...p]=f;d(m,p)}else l(f);else throw new TypeError("Expected usable value, not `"+f+"`")}function l(f){if(!("plugins"in f)&&!("settings"in f))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");u(f.plugins),f.settings&&(s.settings=(0,vs.default)(!0,s.settings,f.settings))}function u(f){let m=-1;if(f!=null)if(Array.isArray(f))for(;++m<f.length;){let p=f[m];o(p)}else throw new TypeError("Expected a list of plugins, not `"+f+"`")}function d(f,m){let p=-1,y=-1;for(;++p<r.length;)if(r[p][0]===f){y=p;break}if(y===-1)r.push([f,...m]);else if(m.length>0){let[g,...A]=m,P=r[y][1];Qr(P)&&Qr(g)&&(g=(0,vs.default)(!0,P,g)),r[y]=[f,g,...A]}}}},Tl=new Sl().freeze();function El(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `parser`")}function vl(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `compiler`")}function Cl(t,e){if(e)throw new Error("Cannot call `"+t+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Rf(t){if(!Qr(t)||typeof t.type!="string")throw new TypeError("Expected node, got `"+t+"`")}function Df(t,e,n){if(!n)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}function Es(t){return vx(t)?t:new ei(t)}function vx(t){return!!(t&&typeof t=="object"&&"message"in t&&"messages"in t)}function Cx(t){return typeof t=="string"||Sx(t)}function Sx(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var Of=Tl().use(cr).use(ks).freeze();var Fl={d:(t,e)=>{for(var n in e)Fl.o(e,n)&&!Fl.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},Pl={};Fl.d(Pl,{Z:()=>Px,$:()=>Bf});var pr={horizontalTab:-2,virtualSpace:-1,nul:0,eof:null,space:32};function Lf(t){return t<pr.nul||t===pr.space}function _l(t){return t<pr.horizontalTab}var Tx={553:t=>{t.exports=function(e){var n,r;return e._compiled||(n=e.before?"(?:"+e.before+")":"",r=e.after?"(?:"+e.after+")":"",e.atBreak&&(n="[\\r\\n][\\t ]*"+n),e._compiled=new RegExp((n?"("+n+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(r||""),"g")),e._compiled}},112:t=>{function e(n,r,s){var o;if(!r)return s;for(typeof r=="string"&&(r=[r]),o=-1;++o<r.length;)if(n.indexOf(r[o])!==-1)return!0;return!1}t.exports=function(n,r){return e(n,r.inConstruct,!0)&&!e(n,r.notInConstruct)}},113:(t,e,n)=>{t.exports=function(u,d,f){for(var m,p,y,g,A,P,S,E,_=(f.before||"")+(d||"")+(f.after||""),F=[],z=[],W={},C=-1;++C<u.unsafe.length;)if(g=u.unsafe[C],s(u.stack,g))for(A=r(g);P=A.exec(_);)m="before"in g||g.atBreak,p="after"in g,y=P.index+(m?P[1].length:0),F.indexOf(y)===-1?(F.push(y),W[y]={before:m,after:p}):(W[y].before&&!m&&(W[y].before=!1),W[y].after&&!p&&(W[y].after=!1));for(F.sort(o),S=f.before?f.before.length:0,E=_.length-(f.after?f.after.length:0),C=-1;++C<F.length;)(y=F[C])<S||y>=E||y+1<E&&F[C+1]===y+1&&W[y].after&&!W[y+1].before&&!W[y+1].after||(S!==y&&z.push(l(_.slice(S,y),"\\")),S=y,!/[!-/:-@[-`{-~]/.test(_.charAt(y))||f.encode&&f.encode.indexOf(_.charAt(y))!==-1?(z.push("&#x"+_.charCodeAt(y).toString(16).toUpperCase()+";"),S++):z.push("\\"));return z.push(l(_.slice(S,E),f.after)),z.join("")};var r=n(553),s=n(112);function o(u,d){return u-d}function l(u,d){for(var f,m=/\\(?=[!-/:-@[-`{-~])/g,p=[],y=[],g=-1,A=0,P=u+d;f=m.exec(P);)p.push(f.index);for(;++g<p.length;)A!==p[g]&&y.push(u.slice(A,p[g])),y.push("\\"),A=p[g];return y.push(u.slice(A)),y.join("")}}},Mf={};function Lt(t){var e=Mf[t];if(e!==void 0)return e.exports;var n=Mf[t]={exports:{}};return Tx[t](n,n.exports,Lt),n.exports}Lt.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return Lt.d(e,{a:e}),e},Lt.d=(t,e)=>{for(var n in e)Lt.o(e,n)&&!Lt.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},Lt.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var Il={};(()=>{function t(s={}){let o=s.permalinks||[],l=s.pageResolver||(y=>[y.replace(/ /g,"_").toLowerCase()]),u=s.newClassName||"new",d=s.wikiLinkClassName||"internal",f=s.hrefTemplate||(y=>`#/page/${y}`),m;function p(y){return y[y.length-1]}return{enter:{wikiLink:function(y){m={type:"wikiLink",value:null,data:{alias:null,permalink:null,exists:null}},this.enter(m,y)}},exit:{wikiLinkTarget:function(y){let g=this.sliceSerialize(y);p(this.stack).value=g},wikiLinkAlias:function(y){let g=this.sliceSerialize(y);p(this.stack).data.alias=g},wikiLink:function(y){this.exit(y);let g=m,A=l(g.value),P=A.find(z=>o.indexOf(z)!==-1),S=P!==void 0,E;E=S?P:A[0]||"";let _=g.value;g.data.alias&&(_=g.data.alias);let F=d;S||(F+=" "+u),g.data.alias=_,g.data.permalink=E,g.data.exists=S,g.data.hName="a",g.data.hProperties={className:F,href:f(E)},g.data.hChildren=[{type:"text",value:_}]}}}}Lt.d(Il,{V:()=>t,x:()=>r});var e=Lt(113),n=Lt.n(e);function r(s={}){let o=s.aliasDivider||":";return{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]},{character:"]",inConstruct:["label","reference"]}],handlers:{wikiLink:function(l,u,d){let f=d.enter("wikiLink"),m=n()(d,l.value,{before:"[",after:"]"}),p=n()(d,l.data.alias,{before:"[",after:"]"}),y;return y=p!==m?`[[${m}${o}${p}]]`:`[[${m}]]`,f(),y}}}}})();var _x=Il.V,Fx=Il.x,Nf=!1;function Bf(t={}){let e=this.data();function n(r,s){e[r]?e[r].push(s):e[r]=[s]}!Nf&&(this.Parser&&this.Parser.prototype&&this.Parser.prototype.blockTokenizers||this.Compiler&&this.Compiler.prototype&&this.Compiler.prototype.visitors)&&(Nf=!0,console.warn("[remark-wiki-link] Warning: please upgrade to remark 13 to use this plugin")),n("micromarkExtensions",function(){var r=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).aliasDivider||":",s="]]";return{text:{91:{tokenize:function(o,l,u){var d,f,m=0,p=0,y=0;return function(_){return _!=="[[".charCodeAt(p)?u(_):(o.enter("wikiLink"),o.enter("wikiLinkMarker"),g(_))};function g(_){return p===2?(o.exit("wikiLinkMarker"),function(F){return _l(F)||F===pr.eof?u(F):(o.enter("wikiLinkData"),o.enter("wikiLinkTarget"),A(F))}(_)):_!=="[[".charCodeAt(p)?u(_):(o.consume(_),p++,g)}function A(_){return _===r.charCodeAt(m)?d?(o.exit("wikiLinkTarget"),o.enter("wikiLinkAliasMarker"),P(_)):u(_):_===s.charCodeAt(y)?d?(o.exit("wikiLinkTarget"),o.exit("wikiLinkData"),o.enter("wikiLinkMarker"),E(_)):u(_):_l(_)||_===pr.eof?u(_):(Lf(_)||(d=!0),o.consume(_),A)}function P(_){return m===r.length?(o.exit("wikiLinkAliasMarker"),o.enter("wikiLinkAlias"),S(_)):_!==r.charCodeAt(m)?u(_):(o.consume(_),m++,P)}function S(_){return _===s.charCodeAt(y)?f?(o.exit("wikiLinkAlias"),o.exit("wikiLinkData"),o.enter("wikiLinkMarker"),E(_)):u(_):_l(_)||_===pr.eof?u(_):(Lf(_)||(f=!0),o.consume(_),S)}function E(_){return y===2?(o.exit("wikiLinkMarker"),o.exit("wikiLink"),l(_)):_!==s.charCodeAt(y)?u(_):(o.consume(_),y++,E)}}}}}}(t)),n("fromMarkdownExtensions",_x(t)),n("toMarkdownExtensions",Fx(t))}var Px=Bf,_I=Pl.Z,$f=Pl.$;(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Yf=".",Ix=[{constructor:ArrayBuffer,equalityComparer:Dx},{constructor:Date,equalityComparer:Ox},{constructor:RegExp,equalityComparer:Mx},{constructor:Map,equalityComparer:Lx},{constructor:Set,equalityComparer:Nx}];function pt(t,e){if(t===e)return!0;if(typeof t!="object"||typeof e!="object"||t===null||e===null)return!1;let n=t.constructor,r=e.constructor;if(n!==r)return!1;if(n!==Object){let d=Bx(t,e);if(d!==void 0)return d}let s=gr(t),o=gr(e);if(s.length!==o.length)return!1;let l=t,u=e;for(let d of s)if(!o.includes(d)||!pt(l[d],u[d]))return!1;return!0}function gr(t){let e=[],n=t;for(;n;){let r=Object.getOwnPropertyDescriptors(n);for(let[s,o]of Object.entries(r)){if(s==="__proto__"||typeof o.value=="function")continue;let l=typeof o.get=="function",u=typeof o.set=="function";if(l||u){l&&u&&e.push(s);continue}o.enumerable&&o.writable&&e.push(s)}n=Object.getPrototypeOf(n)}return e.sort()}function wr(t,e){let n=t,r=e.split(Yf);for(let s of r){if(n===void 0)return;n=n[s]}return n}function Vf(t,e,n){let r=new Error(`Property path ${e} not found`),s=t,o=e.split(Yf);for(let u of o.slice(0,-1)){if(s===void 0)throw r;s=s[u]}let l=o.at(-1);if(s===void 0||l===void 0)throw r;s[l]=n}function yr(t,e={}){let n={functionHandlingMode:"exclude",maxDepth:-1,shouldCatchToJSONErrors:!1,shouldHandleCircularReferences:!1,shouldHandleErrors:!1,shouldHandleUndefined:!1,shouldSortKeys:!1,space:2,tokenSubstitutions:{circularReference:Rl("CircularReference"),maxDepthLimitReached:Rl("MaxDepthLimitReached"),toJSONFailed:Rl("ToJSONFailed")}},r={...n,...e,tokenSubstitutions:{...n.tokenSubstitutions,...e.tokenSubstitutions}};r.maxDepth===-1&&(r.maxDepth=1/0);let s=[],l=Cs(t,"",0,!0,r,s,new WeakSet),u=JSON.stringify(l,null,r.space)??"";return u=be(u,/"\[\[(?<Key>[A-Za-z]+)(?<Index>\d*)\]\]"/g,(d,f,m)=>Rx({functionTexts:s,index:m?parseInt(m,10):0,key:f,substitutions:r.tokenSubstitutions})),u}function Rx(t){switch(t.key){case"CircularReference":return t.substitutions.circularReference;case"Function":return t.functionTexts[t.index]??Kt(new Error(`Function with index ${t.index.toString()} not found`));case"MaxDepthLimitReached":return t.substitutions.maxDepthLimitReached;case"MaxDepthLimitReachedArray":return`Array(${t.index.toString()})`;case"ToJSONFailed":return t.substitutions.toJSONFailed;case"Undefined":return"undefined";default:break}}function Dx(t,e){if(t.byteLength!==e.byteLength)return!1;let n=new Uint8Array(t),r=new Uint8Array(e);return pt(n,r)}function Ox(t,e){return t.getTime()===e.getTime()}function Lx(t,e){if(t.size!==e.size)return!1;for(let[n,r]of t.entries())if(!e.has(n)||!pt(r,e.get(n)))return!1;return!0}function Mx(t,e){return t.source===e.source&&t.flags===e.flags}function Nx(t,e){if(t.size!==e.size)return!1;for(let n of t){if(e.has(n))continue;let r=!1;for(let s of e)if(pt(n,s)){r=!0;break}if(!r)return!1}return!0}function Bx(t,e){for(let{constructor:n,equalityComparer:r}of Ix)if(t instanceof n&&e instanceof n)return r(t,e)}function $x(t,e,n,r,s,o){return e>r.maxDepth?br("MaxDepthLimitReachedArray",t.length):t.map((l,u)=>Cs(l,u.toString(),e+1,n,r,s,o))}function Yx(t,e,n){if(n.shouldHandleCircularReferences)return br("CircularReference");let r=t.constructor.name||"Object";throw new TypeError(`Converting circular structure to JSON
--> starting at object with constructor '${r}'
--- property '${e}' closes the circle`)}function Vx(t,e,n){if(n.functionHandlingMode==="exclude")return;let r=e.length,s=n.functionHandlingMode==="full"?t.toString():`function ${t.name||"anonymous"}() { /* ... */ }`;return e.push(s),br("Function",r)}function zx(t,e,n,r,s,o,l){if(l.has(t))return Yx(t,e,s);if(l.add(t),r){let u=Ux(t,e,n,s,o,l);if(u!==void 0)return u}return Array.isArray(t)?$x(t,n,r,s,o,l):n>s.maxDepth?br("MaxDepthLimitReached"):t instanceof Error&&s.shouldHandleErrors?Dc(t):Wx(t,n,r,s,o,l)}function Wx(t,e,n,r,s,o){let l=Object.entries(t);return r.shouldSortKeys&&l.sort(([u],[d])=>u.localeCompare(d)),Object.fromEntries(l.map(([u,d])=>[u,Cs(d,u,e+1,n,r,s,o)]))}function Rl(t){return`{ "[[${t}]]": null }`}function br(t,e){return`[[${t}${e?.toString()??""}]]`}function Cs(t,e,n,r,s,o,l){return t===void 0?n===0||s.shouldHandleUndefined?br("Undefined"):void 0:typeof t=="function"?Vx(t,o,s):typeof t!="object"||t===null?t:zx(t,e,n,r,s,o,l)}function Ux(t,e,n,r,s,o){let l=t.toJSON;if(typeof l=="function")try{let u=l.call(t,e);return Cs(u,e,n,!1,r,s,o)}catch(u){if(r.shouldCatchToJSONErrors)return br("ToJSONFailed");throw u}}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Hx=/^[A-Za-z][A-Za-z0-9+\-.]*:\S+$/;function Dl(t){if(/\s/.test(t))return!1;if(t.includes("://"))try{return new URL(t),!0}catch{return!1}return Hx.test(t)}var Nn=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function Ss(t){let e=(0,Nn.getFrontMatterInfo)(t);return(0,Nn.parseYaml)(e.frontmatter)??{}}function zf(t,e){let n=(0,Nn.getFrontMatterInfo)(t);if(Object.keys(e).length===0)return t.slice(n.contentStart);let r=(0,Nn.stringifyYaml)(e);return n.exists?ed(t,r,n.from,n.to):`---
${r}---
${t}`}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function Ol(t){return Wf(t)&&t.type==="file"}function Wf(t){return It(t)&&!!t.isCanvas}function qx(t){return Wf(t)&&t.type==="text"}function kr(t,e){if(dn(t))return{endIndex:t.position.end.offset,newContent:e,oldContent:t.original,startIndex:t.position.start.offset};if(Ol(t))return{isCanvas:!0,newContent:e,nodeIndex:t.nodeIndex,oldContent:t.original,type:"file"};if(qx(t))return{isCanvas:!0,newContent:e,nodeIndex:t.nodeIndex,oldContent:t.original,originalReference:t.originalReference,type:"text"};if(It(t))return{frontmatterKey:t.key,newContent:e,oldContent:t.original};throw new Error("Unknown link type")}function Uf(t){return t.sort((e,n)=>It(e)&&It(n)?e.key.localeCompare(n.key):dn(e)&&dn(n)?e.position.start.offset-n.position.start.offset:It(e)?1:-1)}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function Hf(t,e,n){let r=Be(t,e),s=ft(n);await Ts(t,s);let o=qf(t,n);try{await t.vault.copy(r,o)}catch(l){if(!await t.vault.exists(o))throw l}return o}async function Ts(t,e){if(await t.vault.adapter.exists(e))return!1;try{return await t.vault.createFolder(e),!0}catch(n){if(!await t.vault.exists(e))throw n;return!0}}function qf(t,e){let n=Fe(e);return t.vault.getAvailablePath(ve(Ee(e),ke(e,n)),n.slice(1))}function Ll(t,e,n){let r=we(t,e);if(t.vault.adapter.insensitive){let s=Ee(n),o=ke(n),l;for(;l=Ot(t,s,!0),!l;)o=ve(ke(s),o),s=Ee(s);n=ve(l.getParentPrefix(),o)}return r.toLowerCase()===n.toLowerCase()?n:qf(t,n)}async function Ml(t,e){let n=await Nl(t,we(t,e));return n.files.length===0&&n.folders.length===0}async function Nl(t,e){let n=we(t,e),r={files:[],folders:[]};if((await t.vault.adapter.stat(n))?.type!=="folder")return r;try{return await t.vault.adapter.list(n)}catch(s){if(await t.vault.exists(n))throw s;return r}}async function _s(t,e,n,r={}){let o={...{shouldFailOnMissingFile:!0},...r};await Ji(async()=>{let l=await Bl(t,e);if(l===null)return m();let u=await Fn(n,l);if(u===null)return!1;let d=!0;if(!await Gf(t,e,async p=>{await t.vault.process(p,y=>y!==l?(console.warn("Content has changed since it was read. Retrying...",{actualContent:y,expectedContent:l,path:p.path}),d=!1,y):u)}))return m();return d;function m(){if(o.shouldFailOnMissingFile){let p=we(t,e);throw new Error(`File '${p}' not found`)}return!0}},o)}async function Bl(t,e){let n=null;return await Gf(t,e,async r=>{n=await t.vault.read(r)}),n}async function Fs(t,e,n){let r=Be(t,e,!1,!0),s=Ll(t,e,n);if(r.path.toLowerCase()===s.toLowerCase())return r.path!==n&&await t.vault.rename(r,s),s;let o=ft(s);await Ts(t,o);try{await t.vault.rename(r,s)}catch(l){if(!await t.vault.exists(s)||await t.vault.exists(r.path))throw l}return s}async function Gf(t,e,n){let r=we(t,e),s=tt(t,r);if(!s||s.deleted)return!1;try{return await n(s),!0}catch(o){if(s=tt(t,r),!s||s.deleted)return!1;throw o}}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function $l(t,e,n){let r=await Fn(n),s={},o=!1;try{s=Ss(t)}catch(f){console.error(new Error(`Frontmatter parsing failed in ${e}`,{cause:f})),o=!0}for(let f of r)if(nn(f)){let m=t.slice(f.startIndex,f.endIndex);if(m!==f.oldContent)return console.warn("Content mismatch",{actualContent:m,endIndex:f.endIndex,expectedContent:f.oldContent,path:e,startIndex:f.startIndex}),null}else if(Ps(f)){let m=wr(s,f.frontmatterKey);if(m!==f.oldContent)return console.warn("Content mismatch",{actualContent:m,expectedContent:f.oldContent,frontmatterKey:f.frontmatterKey,path:e}),null}r.sort((f,m)=>nn(f)&&nn(m)?f.startIndex-m.startIndex:Ps(f)&&Ps(m)?f.frontmatterKey.localeCompare(m.frontmatterKey):nn(f)?-1:1),r=r.filter((f,m)=>f.oldContent===f.newContent?!1:m===0?!0:!pt(f,r[m-1]));for(let f=1;f<r.length;f++){let m=r[f];if(!m)continue;let p=r[f-1];if(p&&nn(p)&&nn(m)&&p.endIndex&&m.startIndex&&p.endIndex>m.startIndex)return console.warn("Overlapping changes",{change:m,previousChange:p}),null}let l="",u=0,d=!1;for(let f of r)nn(f)?(l+=t.slice(u,f.startIndex),l+=f.newContent,u=f.endIndex):Ps(f)&&(o?console.error(`Cannot apply frontmatter change in ${e}, because frontmatter parsing failed`,{change:f}):(Vf(s,f.frontmatterKey,f.newContent),d=!0));return l+=t.slice(u),d&&(l=zf(l,s)),l}async function Is(t,e,n,r={}){await _s(t,e,async s=>Qt(t,e)?Kx(s,we(t,e),n):await $l(s,we(t,e),n),r)}function ti(t){return!!t.isCanvas}function Gx(t){return ti(t)&&t.type==="file"}function jx(t){return ti(t)&&t.type==="text"}function nn(t){return t.startIndex!==void 0}function Ps(t){return t.frontmatterKey!==void 0}async function Kx(t,e,n){let r=await Fn(n),s=Xx(t),o=new Map;for(let l of r){if(!ti(l))return console.warn("Only canvas changes are supported for canvas files",{change:l,path:e}),null;let u=s.nodes[l.nodeIndex];if(!u)return console.warn("Node not found",{nodeIndex:l.nodeIndex,path:e}),null;if(Gx(l)){if(u.file!==l.oldContent)return console.warn("Content mismatch",{actualContent:u.file,expectedContent:l.oldContent,nodeIndex:l.nodeIndex,path:e,type:"file"}),null;u.file=l.newContent}else if(jx(l)){let d=o.get(l.nodeIndex);d||(d=[],o.set(l.nodeIndex,d)),d.push(l)}}for(let[l,u]of o.entries()){let d=s.nodes[l];if(!d)return console.warn("Node not found",{nodeIndex:l,path:e}),null;if(typeof d.text!="string")return console.warn("Node text is not a string",{nodeIndex:l,path:e}),null;let f=u.map(m=>kr(m.originalReference,m.newContent));d.text=await $l(d.text,`${e}.node${l.toString()}.VIRTUAL_FILE.md`,f)}return JSON.stringify(s,null,"	")}function Xx(t){let e;try{e=JSON.parse(t)}catch{e=null}return(e===null||typeof e!="object")&&(e={}),e}var jf=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function Kf(t){await new Promise(e=>{t.metadataCache.onCleanCache(e)})}function wn(t){let e=[];return t.links&&e.push(...t.links),t.embeds&&e.push(...t.embeds),t.frontmatterLinks&&e.push(...t.frontmatterLinks),Uf(e),e=e.filter((n,r)=>{if(r===0)return!0;let s=e[r-1];return s?dn(n)&&dn(s)?n.position.start.offset!==s.position.start.offset:It(n)&&It(s)?n.key!==s.key:!0:!0}),e}function Yl(t,e){let n=Be(t,e,!0);return ni(t,n,()=>t.metadataCache.getBacklinksForFile(n))}async function rn(t,e,n={}){let r=t.metadataCache.getBacklinksForFile.safe;if(r)return r(e);let s=null;return await Ji(async()=>{let o=Be(t,e);await Kf(t),s=Yl(t,o);for(let l of s.keys()){let u=tt(t,l);if(!u)return!1;await Xf(t,u);let d=await Bl(t,u);if(!d)return!1;let f=Ss(d),m=s.get(l);if(!m)return!1;for(let p of m){let y;if(dn(p))y=d.slice(p.position.start.offset,p.position.end.offset);else if(It(p)){let g=wr(f,p.key);if(typeof g!="string")return!1;y=g}else return!0;if(y!==p.original)return!1}}return!0},n),s}async function Rs(t,e){let n=tt(t,e);if(!n||n.deleted)return null;await Xf(t,n);let r=t.metadataCache.fileCache[n.path];return r&&r.mtime===n.stat.mtime&&r.size===n.stat.size&&t.metadataCache.metadataCache[r.hash]||(await t.metadataCache.computeFileMetadataAsync(n),await Kf(t)),t.metadataCache.getFileCache(n)}function Zx(t,e){if(!e.deleted)return St;let n=[],r=e;for(;r.deleted;)n.push(r.path),t.vault.fileMap[r.path]=r,r=r.parent??Ur(t,ft(r.path),!0);return ht(e)&&t.metadataCache.uniqueFileLookup.add(e.name.toLowerCase(),e),()=>{for(let s of n)delete t.vault.fileMap[s];ht(e)&&t.metadataCache.uniqueFileLookup.remove(e.name.toLowerCase(),e)}}function ni(t,e,n){let r=Zx(t,e);try{return n()}finally{r()}}async function Xf(t,e){if(!en(t,e))return;let n=we(t,e);for(let r of t.workspace.getLeavesOfType(qc.Markdown))r.view instanceof jf.MarkdownView&&r.view.file?.path===n&&r.view.dirty&&await r.view.save()}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function Zf(t){return t.vault.getConfig("newLinkFormat")==="relative"}function Jf(t){return!t.vault.getConfig("useMarkdownLinks")}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Qx="\\|",eA=/[\\\x00\x08\x0B\x0C\x0E-\x1F ]/g,tA=/[\\[\]<>_*~=`$]/g,Qf=/(?<!\\)\|/g,eh="|";function nA(t){let e=xr(t.app,t.link,t.oldSourcePathOrFile??t.newSourcePathOrFile);return e?si({app:t.app,link:t.link,newSourcePathOrFile:t.newSourcePathOrFile,newTargetPathOrFile:e,oldSourcePathOrFile:t.oldSourcePathOrFile,shouldForceMarkdownLinks:t.shouldForceMarkdownLinks,shouldUpdateFilenameAlias:t.shouldUpdateFilenameAlias}):t.link.original}async function Vl(t,e,n,r={}){await Is(t,e,async()=>{let s=await Rs(t,e);return await uA(s,Qt(t,e),n)},r)}function ri(t){return be(t,eA,({substring:e})=>encodeURIComponent(e))}function xr(t,e,n){let{linkPath:r}=th(e.link);return t.metadataCache.getFirstLinkpathDest(r,we(t,n))}function zl(t){let{app:e}=t,r=(e.fileManager.generateMarkdownLink.defaultOptionsFn??(()=>({})))();t={...{isEmptyEmbedAliasAllowed:!0},...r,...t};let o=Be(e,t.targetPathOrFile,t.isNonExistingFileAllowed);return ni(e,o,()=>oA(t))}function ii(t){let e=fA(t);if(e)return e;let n="!",r=t.startsWith(n);r&&(t=ir(t,n));let o=Of().use(cr).use($f,{aliasDivider:eh}).parse(t);if(o.children.length!==1)return null;let l=o.children[0];if(l?.type!=="paragraph"||l.children.length!==1)return null;let u=l.children[0];if(u?.position?.start.offset!==0||u.position.end.offset!==t.length)return null;switch(u.type){case"link":return dA(u,t,r);case"wikiLink":return hA(u,t,r);default:return null}}function rA(t){let{app:e,displayText:n,isWikilink:r,newSourcePathOrFile:s,oldSourcePathOrFile:o,oldTargetPath:l,targetPathOrFile:u}=t;if(r===!1)return!1;if(!n)return!0;let d=Be(e,u,!0),f=we(e,s),m=we(e,o??s),p=Ee(f),y=Ee(m),g=new Set;for(let P of[d.path,l]){if(!P)continue;let S=we(e,P);g.add(S),g.add(ke(S)),g.add(sr(p,S)),g.add(sr(y,S))}for(let P of[m,f])g.add(e.metadataCache.fileToLinktext(d,P,!1));let A=be((0,Ds.normalizePath)(n.split(" > ")[0]??""),/^\.\//g,"").toLowerCase();for(let P of g){if(P.toLowerCase()===A)return!0;let S=Ee(P),E=ke(P,Fe(P));if(ve(S,E).toLowerCase()===A)return!0}return!1}function th(t){let e=(0,Ds.parseLinktext)(Wr(t));return{linkPath:e.path,subpath:e.subpath}}function Wl(t){return ii(t)?.hasAngleBrackets??!1}function nh(t){return ii(t)?.isEmbed??!1}function iA(t){return ii(t)?.url.startsWith("./")??!1}function Os(t){return ii(t)?.isWikilink??!1}function si(t){let{app:e,link:n,newSourcePathOrFile:r,newTargetPathOrFile:s,oldSourcePathOrFile:o,oldTargetPathOrFile:l,shouldForceMarkdownLinks:u,shouldUpdateFilenameAlias:d}=t;if(!s)return n.original;let f=Be(e,s,!0),m=we(e,l??s),p=Os(n.original)&&u!==!0,{subpath:y}=th(n.link),g=!d;if(Qt(e,r)&&Ol(n))return f.path+y;let A;if(p){let S=ii(n.original);S?.alias&&(A=S.alias,g=!0)}return A??=rA({app:e,displayText:n.displayText,isWikilink:p,newSourcePathOrFile:r,oldSourcePathOrFile:o,oldTargetPath:m,targetPathOrFile:f})?void 0:n.displayText,g||(A===ke(m,Fe(m))?A=f.basename:A===ke(m)&&(A=f.name)),zl({alias:A,app:e,isWikilink:u?!1:void 0,originalLink:n.original,sourcePathOrFile:r,subpath:y,targetPathOrFile:f})}async function rh(t){let{app:e,newSourcePathOrFile:n,oldSourcePathOrFile:r,shouldForceMarkdownLinks:s,shouldUpdateEmbedOnlyLinks:o,shouldUpdateFilenameAlias:l}=t;Qt(e,n)&&!e.internalPlugins.getEnabledPluginById(Zt.Canvas)||await Vl(e,n,u=>{let d=nh(u.original);if(!(o!==void 0&&o!==d))return nA({app:e,link:u,newSourcePathOrFile:n,oldSourcePathOrFile:r,shouldForceMarkdownLinks:s,shouldUpdateFilenameAlias:l})},t)}function sA(t,e,n,r,s){let o;return e.path===n&&r?o=r:s.shouldForceRelativePath?o=sr(Ee(n),s.isWikilink?od(t,e):e.path)+r:o=t.metadataCache.fileToLinktext(e,n,s.isWikilink)+r,s.shouldForceRelativePath&&s.shouldUseLeadingDot&&!o.startsWith(".")&&!o.startsWith("#")&&(o=`./${o}`),o}function oA(t){let{app:e}=t,n=Be(e,t.targetPathOrFile,t.isNonExistingFileAllowed),r=we(e,t.sourcePathOrFile),s=t.subpath??"",o=cA(t,n),l=sA(e,n,r,s,o);return o.isWikilink?lA(l,t.alias,o.isEmbed):aA(l,n,t,o)}function aA(t,e,n,r){let{app:s}=n,o=r.isEmbed?"!":"",l=r.shouldUseAngleBrackets?`<${t}>`:ri(t),u=n.alias??"";!u&&(!r.isEmbed||!n.isEmptyEmbedAliasAllowed)&&(u=!n.shouldIncludeAttachmentExtensionToEmbedAlias||en(s,e)?e.basename:e.name);let d=be(u,tA,"\\$&");return`${o}[${d}](${l})`}function lA(t,e,n){let r=n?"!":"",s=e??"";if(s&&s.toLowerCase()===t.toLowerCase())return`${r}[[${s}]]`;let o=s?`|${s}`:"";return`${r}[[${t}${o}]]`}async function uA(t,e,n){if(!t)return[];let r=[],s=(t.sections??[]).filter(o=>o.type==="table").map(o=>({end:o.position.end.offset,start:o.position.start.offset}));for(let o of wn(t)){let l=await n(o);if(l===void 0)continue;let u=kr(o,l);e?ti(u)?r.push(u):console.warn("Unsupported file change",u):(mA(u,s)&&(u.newContent=u.newContent.replaceAll(Qf,Qx)),r.push(u))}return r}function cA(t,e){let{app:n}=t;return{isEmbed:t.isEmbed??(t.originalLink?nh(t.originalLink):void 0)??!en(n,e),isWikilink:t.isWikilink??(t.originalLink?Os(t.originalLink):void 0)??Jf(n),shouldForceRelativePath:t.shouldForceRelativePath??Zf(n),shouldUseAngleBrackets:t.shouldUseAngleBrackets??(t.originalLink?Wl(t.originalLink):void 0)??!1,shouldUseLeadingDot:t.shouldUseLeadingDot??(t.originalLink?iA(t.originalLink):void 0)??!1}}function dA(t,e,n){let r="<",s="](",o=")",l=t.children[0],u=e.slice((l?.position?.end.offset??1)+s.length,(t.position?.end.offset??0)-o.length),d=e.startsWith(r)||u.startsWith(r),f=Dl(t.url),m=t.url;if(!f&&!d)try{m=decodeURIComponent(m)}catch(p){console.error(`Failed to decode URL ${m}`,p)}return{alias:l?.value,encodedUrl:f?ri(m):void 0,hasAngleBrackets:d,isEmbed:n,isExternal:f,isWikilink:!1,title:t.title??void 0,url:m}}function fA(t){let e=t.startsWith("<")&&t.endsWith(">");return e&&(t=t.slice(1,-1)),Dl(t)?{encodedUrl:ri(t),hasAngleBrackets:e,isEmbed:!1,isExternal:!0,isWikilink:!1,url:t}:null}function hA(t,e,n){return{alias:e.includes(eh)?t.data.alias:void 0,isEmbed:n,isExternal:!1,isWikilink:!0,url:t.value}}function mA(t,e){return!nn(t)||!Qf.test(t.newContent)?!1:e.some(n=>n.start<=t.startIndex&&t.endIndex<=n.end)}var ch=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var B=(t=>(t.AlertModal="alert-modal",t.CancelButton="cancel-button",t.CheckboxComponent="checkbox-component",t.CodeHighlighterComponent="code-highlighter-component",t.ConfirmModal="confirm-modal",t.DateComponent="date-component",t.DateTimeComponent="datetime-component",t.EmailComponent="email-component",t.FileComponent="file-component",t.IsPlaceholder="is-placeholder",t.LibraryName="obsidian-dev-utils",t.MonthComponent="month-component",t.MultipleDropdownComponent="multiple-dropdown-component",t.MultipleEmailComponent="multiple-email-component",t.MultipleFileComponent="multiple-file-component",t.MultipleTextComponent="multiple-text-component",t.NumberComponent="number-component",t.OkButton="ok-button",t.OverlayValidator="overlay-validator",t.PasswordComponent="password-component",t.PluginSettingsTab="plugin-settings-tab",t.PromptModal="prompt-modal",t.SelectItemModal="select-item-modal",t.SettingComponentWrapper="setting-component-wrapper",t.TelephoneComponent="telephone-component",t.TextBox="text-box",t.TimeComponent="time-component",t.Tooltip="tooltip",t.TooltipArrow="tooltip-arrow",t.TooltipValidator="tooltip-validator",t.TriStateCheckboxComponent="tri-state-checkbox-component",t.TypedDropdownComponent="typed-dropdown-component",t.TypedMultipleDropdownComponent="typed-multiple-dropdown-component",t.UrlComponent="url-component",t.WeekComponent="week-component",t))(B||{});var uh=require("obsidian");var ah=Ne(sh(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var oh=`${Ui}-styles`;function ue(t,...e){t.addClass(B.LibraryName,ji(),...e)}function lh(t,e){Mc(e),$c(e);let n=Xt(t,"lastLibraryVersion","0.0.0");if((0,ah.compareVersions)(Ea,n.value)<=0)return;n.value=Ea;let r=pA();r.DEBUG=Bc(),document.head.querySelector(`#${oh}`)?.remove(),document.head.createEl("style",{attr:{id:oh},text:Rc})}function pA(){return window}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var yn=class extends uh.Modal{constructor(e,n,r){super(e.app),this.resolve=n,ue(this.containerEl,r),e.cssClass&&this.containerEl.addClass(e.cssClass)}};async function Ar(t){return await new Promise(e=>{t(e).open()})}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Ul=class extends yn{options;constructor(e,n){super(e,n,B.AlertModal);let r={app:e.app,cssClass:"",message:e.message,okButtonText:"OK",title:""};this.options={...r,...e}}onClose(){super.onClose(),this.resolve()}onOpen(){super.onOpen(),this.titleEl.setText(this.options.title),this.contentEl.createEl("p",{text:this.options.message});let e=new ch.ButtonComponent(this.contentEl);e.setButtonText(this.options.okButtonText),e.setCta(),e.onClick(this.close.bind(this)),e.setClass(B.OkButton)}};async function dh(t){await Ar(e=>new Ul(t,e))}function fh(t,e){let n=Object.keys(e).map(r=>gA(t,r,e[r]));return n.length===1?n[0]:function(){n.forEach(r=>r())}}function gA(t,e,n){let r=t[e],s=t.hasOwnProperty(e),o=s?r:function(){return Object.getPrototypeOf(t)[e].apply(this,arguments)},l=n(o);return r&&Object.setPrototypeOf(l,r),Object.setPrototypeOf(u,l),t[e]=u,d;function u(...f){return l===o&&t[e]===u&&d(),l.apply(this,f)}function d(){t[e]===u&&(s?t[e]=o:delete t[e]),l!==o&&(l=o,Object.setPrototypeOf(u,r||Function))}}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function wA(t,e){return fh(t,e)}function Bn(t,e,n){let r=wA(e,n),s=!1;function o(){if(!s)try{r()}finally{s=!0}}return t.register(o),r}var $s=require("obsidian");var hh=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Ms=class extends hh.Component{registerAsyncEvent(e){Ns(this,e)}};function Ns(t,e){t.register(()=>{e.asyncEvents.offref(e)})}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Bs=class extends $s.Plugin{events=new Pt;get abortSignal(){return this._abortSignal}get settings(){return this.settingsManager.settingsWrapper.safeSettings}get settingsManager(){if(!this._settingsManager)throw new Error("Settings manager not defined");return this._settingsManager}get settingsTab(){if(!this._settingsTab)throw new Error("Settings tab not defined");return this._settingsTab}_abortSignal;_settingsManager=null;_settingsTab=null;lifecycleEventNames=new Set;notice;consoleDebug(e,...n){Xi(this.manifest.id,1)(e,...n)}async onExternalSettingsChange(){await super.onExternalSettingsChange?.(),await this._settingsManager?.loadFromFile(!1)}async onload(){await super.onload(),await this.onloadImpl(),zc(this.afterLoad.bind(this))}onunload(){super.onunload(),et(async()=>{try{await this.onunloadImpl()}finally{await this.triggerLifecycleEvent("unload")}})}async waitForLifecycleEvent(e){this.lifecycleEventNames.has(e)||await new Promise(n=>{this.events.once(e,()=>{n()})})}createSettingsManager(){return null}createSettingsTab(){return null}async onLayoutReady(){await Tt()}async onloadImpl(){lh(this.app,this.manifest.id),this.register(Oc(()=>{this.showNotice("An unhandled error occurred. Please check the console for more information.")})),this._settingsManager=this.createSettingsManager(),this._settingsManager&&(Ns(this,this._settingsManager.on("loadSettings",this.onLoadSettings.bind(this))),Ns(this,this._settingsManager.on("saveSettings",this.onSaveSettings.bind(this)))),await this._settingsManager?.loadFromFile(!0),this._settingsTab=this.createSettingsTab(),this._settingsTab&&this.addSettingTab(this._settingsTab);let e=new AbortController;this._abortSignal=e.signal,this.register(()=>{e.abort()})}async onLoadSettings(e,n){await Tt()}async onSaveSettings(e,n,r){await Tt()}async onunloadImpl(){await Tt()}showNotice(e){this.notice&&this.notice.hide(),this.notice=new $s.Notice(`${this.manifest.name}
${e}`)}async afterLoad(){await this.triggerLifecycleEvent("load"),this.app.workspace.onLayoutReady(st(this.onLayoutReadyBase.bind(this)))}async onLayoutReadyBase(){try{await this.onLayoutReady()}finally{await this.triggerLifecycleEvent("layoutReady")}}async triggerLifecycleEvent(e){this.lifecycleEventNames.add(e),await this.events.triggerAsync(e)}};var kh=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function mh(t,e,n){let r=dt("Logger:invokeAsyncAndLog"),s=performance.now();n??=_n(1),r(`${t}:start`,{fn:e,timestampStart:s}),r.printStackTrace(n);try{await e();let o=performance.now();r(`${t}:end`,{duration:o-s,fn:e,timestampEnd:o,timestampStart:s}),r.printStackTrace(n)}catch(o){let l=performance.now();throw r(`${t}:error`,{duration:l-s,error:o,fn:e,timestampEnd:l,timestampStart:s}),r.printStackTrace(n),o}}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function bn(t,e,n,r){r??=_n(1),et(()=>yA(t,e,n,r))}async function yA(t,e,n,r){n??=6e4,r??=_n(1);let o=ph(t).value;o.items.push({fn:e,stackTrace:r,timeoutInMilliseconds:n}),o.promise=o.promise.then(()=>gh(t)),await o.promise}function ph(t){return Xt(t,"queue",{items:[],promise:Promise.resolve()})}async function gh(t){let e=ph(t).value,n=e.items[0];n&&(await Ia(()=>Ra(n.timeoutInMilliseconds,()=>mh(gh.name,n.fn,n.stackTrace),{queuedFn:n.fn})),e.items.shift())}var wh=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function Ys(t,e){let n=Ot(t,e);for(;n;){if(!await Ml(t,n))return;let r=n.parent;await Er(t,n.path),n=r}}async function Er(t,e,n,r,s){let o=Pn(t,e);if(!o)return!1;let l=ht(o)||(s??!0);if(ht(o)){let u=await rn(t,o);n&&u.clear(n),u.count()!==0&&(r&&new wh.Notice(`Attachment ${o.path} is still used by other notes. It will not be deleted.`),l=!1)}else if(La(o)){let u=await Nl(t,o);for(let d of[...u.files,...u.folders])l&&=await Er(t,d,n,r);l&&=await Ml(t,o)}if(l)try{await t.fileManager.trashFile(o)}catch(u){await t.vault.exists(o.path)&&(Yr(new Error(`Failed to delete ${o.path}`,{cause:u})),l=!1)}return l}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Hl=new Map,oi=new Set,Vs=new Map,nt=(t=>(t.Delete="Delete",t.DeleteWithEmptyParents="DeleteWithEmptyParents",t.Keep="Keep",t))(nt||{});function xh(t,e){let n=zs(t.app),r=t.manifest.id;n.set(r,e),bh(t.app),t.register(()=>{n.delete(r),bh(t.app)});let s=t.app;t.registerEvent(s.vault.on("delete",o=>{EA(t,o)})),t.registerEvent(s.vault.on("rename",(o,l)=>{TA(t,o,l)})),t.registerEvent(s.metadataCache.on("deleted",(o,l)=>{CA(t,o,l)})),Bn(t,s.fileManager,{runAsyncLinkUpdate:o=>l=>FA(s,o,l)})}async function Ah(t,e,n){let r=$n(t);if(r.emptyAttachmentFolderBehavior!=="Keep")for(let s of e)switch(r.emptyAttachmentFolderBehavior){case"Delete":await Er(t,s,n,void 0,!0);break;case"DeleteWithEmptyParents":await Ys(t,s);break;default:break}}async function bA(t,e,n,r,s){let o=Vs.get(e);if(o){Vs.delete(e);for(let l of o)await ql(t,l.oldPath,n,r,s,l.combinedBacklinksMap)}}async function kA(t,e,n,r,s){if(r.set(e,n),!ai(t,e))return;let o=$n(t),l=await or(t,e),u=o.shouldRenameAttachmentFolder?await or(t,n):l,d=l==="/",f=Ot(t,l);if(!f||l===u&&!o.shouldRenameAttachmentFiles)return;let m=[];if(await Na(t,e))kh.Vault.recurseChildren(f,g=>{ht(g)&&m.push(g)});else for(let g of s){let A=xr(t,g,e);A&&(d||A.path.startsWith(l))&&(await rn(t,A)).keys().length===1&&m.push(A)}let p=ke(e,Fe(e)),y=ke(n,Fe(n));for(let g of m){if(ai(t,g.path))continue;let A=d?g.path:sr(l,g.path),P=ve(u,Ee(A)),S=o.shouldRenameAttachmentFiles?be(g.basename,p,y):g.basename,E=ve(P,fn(S,g.extension));if(g.path!==E){if(o.shouldDeleteConflictingAttachments){let _=tt(t,E);_&&await t.fileManager.trashFile(_)}else E=t.vault.getAvailablePath(ve(P,S),g.extension);r.set(g.path,E)}}}function zs(t){return Xt(t,"renameDeleteHandlersMap",new Map).value}function $n(t){let e=zs(t),n=Array.from(e.values()).reverse(),r={};r.isNote=s=>Hr(t,s),r.isPathIgnored=()=>!1;for(let s of n){let o=s();r.shouldDeleteConflictingAttachments||=o.shouldDeleteConflictingAttachments??!1,o.emptyAttachmentFolderBehavior&&(r.emptyAttachmentFolderBehavior??=o.emptyAttachmentFolderBehavior),r.shouldHandleDeletions||=o.shouldHandleDeletions??!1,r.shouldHandleRenames||=o.shouldHandleRenames??!1,r.shouldRenameAttachmentFiles||=o.shouldRenameAttachmentFiles??!1,r.shouldRenameAttachmentFolder||=o.shouldRenameAttachmentFolder??!1,r.shouldUpdateFilenameAliases||=o.shouldUpdateFilenameAliases??!1;let l=r.isPathIgnored;r.isPathIgnored=d=>l(d)||(o.isPathIgnored?.(d)??!1);let u=r.isNote;r.isNote=d=>u(d)&&(o.isNote?.(d)??!0)}return r.emptyAttachmentFolderBehavior??="Keep",r}async function xA(t,e,n,r,s){if(!t.vault.adapter.insensitive||e.toLowerCase()!==n.toLowerCase())return!1;let o=ve(Ee(n),`__temp__${ke(n)}`);return await vh(t,n,o),await ql(t,e,o,r,s),await t.vault.rename(Be(t,o),n),!0}async function AA(t,e){if(dt("RenameDeleteHandler:handleDelete")(`Handle Delete ${e}`),!ai(t,e))return;let n=$n(t);if(!n.shouldHandleDeletions||n.isPathIgnored?.(e))return;let r=Hl.get(e);Hl.delete(e);let s=new Set;if(r){let u=wn(r);for(let d of u){let f=xr(t,d,e);f&&(ai(t,f.path)||(s.add(f.parent?.path??""),await Er(t,f,e,!1,n.emptyAttachmentFolderBehavior!=="Keep")))}}await Ah(t,Array.from(s),e);let o=await or(t,e),l=Ot(t,o);l&&await Na(t,e)&&await Er(t,l,e,!1,n.emptyAttachmentFolderBehavior!=="Keep")}function EA(t,e){let n=t.app;if(!Gl(t))return;let r=e.path;bn(n,()=>AA(n,r))}function vA(t,e,n){let r=$n(t);r.isPathIgnored?.(e.path)||r.shouldHandleDeletions&&en(t,e)&&n&&Hl.set(e.path,n)}function CA(t,e,n){Gl(t)&&vA(t.app,e,n)}function SA(t,e,n){let r=Eh(e,n);if(dt("RenameDeleteHandler:handleRename")(`Handle Rename ${r}`),oi.has(r)){oi.delete(r);return}let s=$n(t);if(!s.shouldHandleRenames||s.isPathIgnored?.(e)||s.isPathIgnored?.(n))return;let o=t.metadataCache.getCache(e)??t.metadataCache.getCache(n),l=o?wn(o):[],u=Yl(t,e).data;bn(t,()=>ql(t,e,n,u,l))}async function ql(t,e,n,r,s,o){if(await bA(t,e,n,r,s),await _A(t,e,n,r,s),!await xA(t,e,n,r,s))try{let l=new Map;await kA(t,e,n,l,s);let u=new Map;yh(r,l,u,e);for(let m of l.keys()){if(m===e)continue;let p=(await rn(t,m)).data;yh(p,l,u,m)}let d=new Set;for(let[m,p]of l.entries()){if(m===e)continue;let y=await vh(t,m,p);l.set(m,y),d.add(Ee(m))}await Ah(t,Array.from(d),e);let f=$n(t);for(let[m,p]of Array.from(u.entries()).concat(Array.from(o?.entries()??[])))await Vl(t,m,y=>{let g=p.get(yr(y));if(!g)return;let A=l.get(g);if(A)return si({app:t,link:y,newSourcePathOrFile:m,newTargetPathOrFile:A,oldTargetPathOrFile:g,shouldUpdateFilenameAlias:f.shouldUpdateFilenameAliases})},{shouldFailOnMissingFile:!1});if(ai(t,n)&&await rh({app:t,newSourcePathOrFile:n,oldSourcePathOrFile:e,shouldFailOnMissingFile:!1,shouldUpdateFilenameAlias:f.shouldUpdateFilenameAliases}),!tt(t,n)){let m=Vs.get(n);m||(m=[],Vs.set(n,m)),m.push({combinedBacklinksMap:u,oldPath:e})}}finally{let l=Array.from(oi);bn(t,()=>{for(let u of l)oi.delete(u)})}}function TA(t,e,n){if(!Gl(t)||!ht(e))return;let r=e.path;SA(t.app,n,r)}function yh(t,e,n,r){for(let[s,o]of t.entries()){let l=e.get(s)??s,u=n.get(l)??new Map;n.set(l,u);for(let d of o)u.set(yr(d),r)}}function ai(t,e){return $n(t).isNote?.(e)??!1}function bh(t){let e=zs(t);dt("RenameDeleteHandler:logRegisteredHandlers")(`Plugins with registered rename/delete handlers: ${JSON.stringify(Array.from(e.keys()))}`)}function Eh(t,e){return`${t} -> ${e}`}async function _A(t,e,n,r,s){let o=t.metadataCache.getCache(e)??t.metadataCache.getCache(n),l=o?wn(o):[],u=Be(t,e,!0),d=new Map;await ni(t,u,async()=>{d=(await rn(t,u)).data});for(let f of l)s.includes(f)||s.push(f);for(let[f,m]of d.entries()){let p=r.get(f);p||(p=[],r.set(f,p));for(let y of m)p.includes(y)||p.push(y)}}async function vh(t,e,n){if(n=Ll(t,e,n),e===n)return n;let r=Eh(e,n);return oi.add(r),n=await Fs(t,e,n),n}async function FA(t,e,n){await e.call(t.fileManager,r);async function r(s){let o=!1,l=t.vault.on("rename",()=>{o=!0});try{await n(s)}finally{t.vault.offref(l)}o&&s.splice(0)}}function Gl(t){let e=t.app,n=t.manifest.id,r=zs(e);return Array.from(r.keys())[0]===n}var qp=Ne(hp(),1);var Vn=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function ce(t,e){t.createEl("strong",{cls:"markdown-rendered code"},n=>{n.createEl("code",{text:e})})}var mp=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();async function pp(t){let n={...{abortSignal:AbortSignal.any([]),buildNoticeMessage(){throw new Error("buildNoticeMessage is required")},items:[],noticeMinTimeoutInMilliseconds:2e3,processItem:St,progressBarTitle:"",shouldContinueOnError:!0,shouldShowProgressBar:!0,uiUpdateThresholdInMilliseconds:100},...t},r=n.items,s=0,o=null;n.shouldShowProgressBar&&(o=new mp.Notice("",0));let l=sleep(n.noticeMinTimeoutInMilliseconds),u=createEl("progress");if(u.max=r.length,n.shouldShowProgressBar){let f=createFragment();f.createDiv({text:n.progressBarTitle}),f.appendChild(u),o?.setMessage(f)}let d=performance.now();for(let f of r){if(n.abortSignal.aborted){o?.hide();return}s++;let m=`# ${s.toString()} / ${r.length.toString()}`,p=n.buildNoticeMessage(f,m);n.shouldShowProgressBar||o?.setMessage(p),dt("Loop")(p);let y=new Error(qi);try{performance.now()-d>n.uiUpdateThresholdInMilliseconds&&(await Wc(),d=performance.now()),await n.processItem(f)}catch(g){if(console.error("Error processing item",f),!n.shouldContinueOnError)throw o?.hide(),g;y.cause=g,Gi(y)}u.value++}await l,o?.hide()}var fu=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var hu=class extends yn{isConfirmed=!1;options;constructor(e,n){super(e,n,B.ConfirmModal);let r={app:e.app,cancelButtonText:"Cancel",cssClass:"",message:e.message,okButtonText:"OK",title:""};this.options={...r,...e}}onClose(){super.onClose(),this.resolve(this.isConfirmed)}onOpen(){super.onOpen(),this.titleEl.setText(this.options.title),this.contentEl.createEl("p",{text:this.options.message});let e=new fu.ButtonComponent(this.contentEl);e.setButtonText(this.options.okButtonText),e.setCta(),e.onClick(()=>{this.isConfirmed=!0,this.close()}),e.setClass(B.OkButton);let n=new fu.ButtonComponent(this.contentEl);n.setButtonText(this.options.cancelButtonText),n.onClick(this.close.bind(this)),n.setClass(B.CancelButton)}};async function gp(t){return await Ar(e=>new hu(t,e))}var Ap=require("obsidian");var yu=Ne(jt(),1);var pi=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var mu=class extends yn{isOkClicked=!1;options;value;constructor(e,n){super(e,n,B.PromptModal);let r={app:e.app,cancelButtonText:"Cancel",defaultValue:"",okButtonText:"OK",placeholder:"",title:"",valueValidator:St};this.options={...r,...e},this.value=e.defaultValue??""}onClose(){super.onClose(),this.resolve(this.isOkClicked?this.value:null)}onOpen(){super.onOpen(),this.titleEl.setText(this.options.title);let e=new pi.TextComponent(this.contentEl),n=e.inputEl,r=async()=>{let l=await this.options.valueValidator(n.value);n.setCustomValidity(l??""),n.reportValidity()};e.setValue(this.value),e.setPlaceholder(this.options.placeholder),n.addClass(B.TextBox),e.onChange(l=>{this.value=l}),n.addEventListener("keydown",l=>{l.key==="Enter"?this.handleOk(l,e):l.key==="Escape"&&this.close()}),n.addEventListener("input",st(r)),n.addEventListener("focus",st(r)),et(r);let s=new pi.ButtonComponent(this.contentEl);s.setButtonText(this.options.okButtonText),s.setCta(),s.onClick(l=>{this.handleOk(l,e)}),s.setClass(B.OkButton);let o=new pi.ButtonComponent(this.contentEl);o.setButtonText(this.options.cancelButtonText),o.onClick(this.close.bind(this)),o.setClass(B.CancelButton)}handleOk(e,n){e.preventDefault(),n.inputEl.checkValidity()&&(this.isOkClicked=!0,this.close())}};async function wp(t){return await Ar(e=>new mu(t,e))}var to=Ne(yp(),1),CC="default"in to.default?to.default.default:to.default,SC=/^\.{3,}$/,TC=/[. ]+$/,bu=/[\\/:*?"<>|]/,gi=/\${(?<Token>.+?)(?::(?<Format>.+?))?}/g;function ku(t){let e=new Map;try{let n=new Function("exports",t),r={};n(r);for(let[s,o]of Object.entries(r))e.set(s,o);return e}catch(n){throw new Error("Error initializing custom token formatters",{cause:n})}}function _C(t){return(0,yu.default)().format(t)}function bp(t,e,n,r){let s=tt(t,e);return s?(0,yu.default)(r(s)).format(n):""}function wu(t,e){switch(e){case"":return t;case"lower":return t.toLowerCase();case"slug":return DC(t);case"upper":return t.toUpperCase();default:throw new Error(`Invalid file name format: ${e}`)}}function FC(t,e){switch(e){case"":case"B":return String(t);case"KB":return String(Math.floor(t/1024));case"MB":return String(Math.floor(t/1048576));default:throw new Error(`Invalid file size format: ${e}`)}}function PC(t){if(t==="uuid")return crypto.randomUUID();let e=/^(?<BaseFormat>D|L|DL)(?<Count>\d*)$/.exec(t);if(!e)throw new Error(`Invalid random value format: ${t}`);let n=e.groups?.BaseFormat,r=parseInt((e.groups?.Count??"")||"1",10),s;switch(n){case"D":s="0123456789";break;case"DL":s="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";break;case"L":s="ABCDEFGHIJKLMNOPQRSTUVWXYZ";break;default:throw new Error(`Invalid random value format: ${t}`)}let o="";for(let l=0;l<r;l++)o+=RC(s);return o}function IC(t,e,n){let r=tt(t,e);if(!r)return"";let s=t.metadataCache.getFileCache(r);if(!s?.frontmatter)return"";let o=wr(s.frontmatter,n)??"";return String(o)}var yt=class t{static formatters=new Map;static{this.registerCustomFormatters("")}noteFolderPath;app;attachmentFileSizeInBytes;noteFileName;noteFilePath;noteFolderName;originalAttachmentFileExtension;originalAttachmentFileName;constructor(e){this.app=e.app,this.noteFilePath=e.noteFilePath,this.noteFileName=ke(this.noteFilePath,Fe(this.noteFilePath)),this.noteFolderName=ke(Ee(this.noteFilePath)),this.noteFolderPath=Ee(this.noteFilePath);let n=e.originalAttachmentFileName??"",r=Fe(n);this.originalAttachmentFileName=ke(n,r),this.originalAttachmentFileExtension=r.slice(1),this.attachmentFileSizeInBytes=e.attachmentFileSizeInBytes??0}static isRegisteredToken(e){return t.formatters.has(e.toLowerCase())}static registerCustomFormatters(e){this.formatters.clear(),this.registerFormatter("date",(r,s)=>_C(s)),this.registerFormatter("noteFileCreationDate",(r,s)=>bp(r.app,r.noteFilePath,s,o=>o.stat.ctime)),this.registerFormatter("noteFileModificationDate",(r,s)=>bp(r.app,r.noteFilePath,s,o=>o.stat.mtime)),this.registerFormatter("noteFileName",(r,s)=>wu(r.noteFileName,s)),this.registerFormatter("noteFilePath",r=>r.noteFilePath),this.registerFormatter("noteFolderName",(r,s)=>wu(r.noteFolderName,s)),this.registerFormatter("noteFolderPath",r=>r.noteFolderPath),this.registerFormatter("frontmatter",(r,s)=>IC(r.app,r.noteFilePath,s)),this.registerFormatter("originalAttachmentFileExtension",r=>r.originalAttachmentFileExtension),this.registerFormatter("originalAttachmentFileName",(r,s)=>wu(r.originalAttachmentFileName,s)),this.registerFormatter("prompt",r=>r.prompt()),this.registerFormatter("random",(r,s)=>PC(s)),this.registerFormatter("attachmentFileSize",(r,s)=>FC(r.attachmentFileSizeInBytes,s));let n=ku(e)??new Map;for(let[r,s]of n.entries())this.registerFormatter(r,s)}static registerFormatter(e,n){this.formatters.set(e.toLowerCase(),n)}async fillTemplate(e){return await td(e,gi,async(n,r,s)=>{let o=t.formatters.get(r.toLowerCase());if(!o)throw new Error(`Invalid token: ${r}`);try{return String(await o(this,s)??"")}catch(l){throw new Error(`Error formatting token \${${r}}`,{cause:l})}})}async prompt(){let e=await wp({app:this.app,defaultValue:this.originalAttachmentFileName,title:"Provide a value for ${prompt} template",valueValidator:n=>no(n,!1)});if(e===null)throw new Error("Prompt cancelled");return e}};function no(t,e=!0){if(e){t=kp(t);let n=xp(t);if(n)return`Unknown token: ${n}`}else if(t.match(gi))return"Tokens are not allowed in file name";return t==="."||t===".."?"":t?bu.test(t)?`File name "${t}" contains invalid symbols`:SC.test(t)?`File name "${t}" contains more than two dots`:TC.test(t)?`File name "${t}" contains trailing dots or spaces`:"":"File name is empty"}function wi(t,e=!0){if(e){t=kp(t);let r=xp(t);if(r)return`Unknown token: ${r}`}else if(t.match(gi))return"Tokens are not allowed in path";if(t=ir(t,"/"),t=ts(t,"/"),t==="")return"";let n=t.split("/");for(let r of n){let s=no(r);if(s)return s}return""}function RC(t){return t[Math.floor(Math.random()*t.length)]??""}function kp(t){return be(t,gi,(e,n)=>`\${${n}}`)}function DC(t){return CC(t,{lower:!0})}function xp(t){let e=t.matchAll(gi);for(let n of e){let r=n[1]??"";if(!yt.isRegisteredToken(r))return r}return null}async function yi(t,e,n,r){return await LC(t,new yt({app:t.app,attachmentFileSizeInBytes:r??0,noteFilePath:e,originalAttachmentFileName:n}))}async function ro(t,e){return await Ep(t,t.settings.generatedAttachmentFilename,e)}function OC(t,e){return t.settings.specialCharacters&&(e=e.replace(t.settings.specialCharactersRegExp,t.settings.specialCharactersReplacement)),e}async function LC(t,e){return await Ep(t,t.settings.attachmentFolderPath,e)}async function Ep(t,e,n){let r=await n.fillTemplate(e),s=wi(r,!1);if(s)throw new Error(`Resolved path ${r} is invalid: ${s}`);return t.settings.shouldRenameAttachmentsToLowerCase&&(r=r.toLowerCase()),r=OC(t,r),(r.startsWith("./")||r.startsWith("../"))&&(r=ve(n.noteFolderPath,r)),r=(0,Ap.normalizePath)(r),r}async function vp(t,e){let n=t.app,r=new Vn.Notice(`Collecting attachments for ${e.path}`),s=new Map,o=Qt(n,e);await Is(n,e,async()=>{let l=await Rs(n,e);if(!l)return[];let u=o?await MC(n,e):wn(l),d=[];for(let f of u){let m=await NC(t,f,e.path,e.path);if(!m||t.settings.isExcludedFromAttachmentCollecting(m.oldAttachmentPath))continue;if((await rn(n,m.oldAttachmentPath)).keys().length>1)if(t.settings.shouldDuplicateCollectedAttachments)m.newAttachmentPath=await Hf(n,m.oldAttachmentPath,m.newAttachmentPath);else continue;else m.newAttachmentPath=await Fs(n,m.oldAttachmentPath,m.newAttachmentPath),await Ys(n,Ee(m.oldAttachmentPath));if(s.set(m.oldAttachmentPath,m.newAttachmentPath),!o){let y=si({app:n,link:f,newSourcePathOrFile:e,newTargetPathOrFile:m.newAttachmentPath,oldTargetPathOrFile:m.oldAttachmentPath});d.push(kr(f,y))}}return d}),o&&await _s(n,e,l=>{let u=JSON.parse(l);for(let d of u.nodes){if(d.type!=="file")continue;let f=s.get(d.file);f&&(d.file=f)}return yr(u)}),r.hide()}function Cp(t,e){let n=t.app.workspace.getActiveFile();return zn(t,n)?(e||bn(t.app,()=>io(t,n?.parent??Kt(new Error("Parent folder not found")))),!0):!1}function Sp(t,e){let n=t.app.workspace.getActiveFile();if(!n||!zn(t,n))return!1;if(!e){if(t.settings.isPathIgnored(n.path))return new Vn.Notice("Note path is ignored"),!0;bn(t.app,()=>vp(t,n))}return!0}function Tp(t){bn(t.app,()=>io(t,t.app.vault.getRoot()))}async function io(t,e){if(!await gp({app:t.app,message:createFragment(r=>{r.appendText("Do you want to collect attachments for all notes in folder: "),ce(r,e.path),r.appendText(" and all its subfolders?"),r.createEl("br"),r.appendText("This operation cannot be undone.")}),title:createFragment(r=>{(0,Vn.setIcon)(r.createSpan(),"lucide-alert-triangle"),r.appendText(" Collect attachments in folder")})}))return;t.consoleDebug(`Collect attachments in folder: ${e.path}`);let n=[];Vn.Vault.recurseChildren(e,r=>{zn(t,r)&&n.push(r)}),n.sort((r,s)=>r.path.localeCompare(s.path)),await pp({abortSignal:t.abortSignal,buildNoticeMessage:(r,s)=>`Collecting attachments ${s} - ${r.path}`,items:n,processItem:async r=>{t.settings.isPathIgnored(r.path)||await vp(t,r)},progressBarTitle:"Custom Attachment Location: Collecting attachments...",shouldContinueOnError:!0,shouldShowProgressBar:!0})}function zn(t,e){if(!e||!Hr(t.app,e))return!1;let n=we(t.app,e);return t.settings.treatAsAttachmentExtensions.every(r=>!n.endsWith(r))}async function MC(t,e){return(await t.vault.readJson(e.path)).nodes.filter(s=>s.type==="file").map(s=>s.file).map(s=>({link:s,original:s,position:{end:{col:0,line:0,loc:0,offset:0},start:{col:0,line:0,loc:0,offset:0}}}))}async function NC(t,e,n,r){let s=t.app,o=xr(s,e,r);if(!o||zn(t,o))return null;let l=o.path,u=o.name,d=ke(r,Fe(r)),f=ke(n,Fe(n)),m;t.settings.shouldRenameCollectedAttachments?m=fn(await ro(t,new yt({app:t.app,attachmentFileSizeInBytes:o.stat.size,noteFilePath:n,originalAttachmentFileName:o.name})),o.extension):t.settings.shouldRenameAttachmentFiles?m=u.replaceAll(d,f):m=u;let p=await yi(t,n,m),y=ve(p,m);return l===y?null:{newAttachmentPath:y,oldAttachmentPath:l}}var _p=/(?:)/,so=/$./,Sr=(r=>(r.None="None",r.OnlyPastedImages="Only pasted images",r.All="All",r))(Sr||{}),oo=class{attachmentFolderPath="./assets/${noteFileName}";attachmentRenameMode="Only pasted images";duplicateNameSeparator=" ";emptyAttachmentFolderBehavior=nt.DeleteWithEmptyParents;generatedAttachmentFilename="file-${date:YYYYMMDDHHmmssSSS}";jpegQuality=.8;markdownUrlFormat="";shouldConvertPastedImagesToJpeg=!1;shouldDeleteOrphanAttachments=!1;shouldDuplicateCollectedAttachments=!1;shouldRenameAttachmentFiles=!1;shouldRenameAttachmentFolder=!0;shouldRenameAttachmentsToLowerCase=!1;shouldRenameCollectedAttachments=!1;specialCharacters="#^[]|*\\<>:?";specialCharactersReplacement="-";treatAsAttachmentExtensions=[".excalidraw.md"];warningVersion="0.0.0";get customTokensStr(){return this._customTokensStr}set customTokensStr(e){this._customTokensStr=e,yt.registerCustomFormatters(this._customTokensStr)}get excludePaths(){return this._excludePaths}set excludePaths(e){this._excludePaths=e.filter(Boolean),this._excludePathsRegExp=xu(this._excludePaths,so)}get excludePathsFromAttachmentCollecting(){return this._excludePathsFromAttachmentCollecting}set excludePathsFromAttachmentCollecting(e){this._excludePathsFromAttachmentCollecting=e.filter(Boolean),this._excludePathsFromAttachmentCollectingRegExp=xu(this._excludePathsFromAttachmentCollecting,so)}get includePaths(){return this._includePaths}set includePaths(e){this._includePaths=e.filter(Boolean),this._includePathsRegExp=xu(this._includePaths,_p)}get specialCharactersRegExp(){return new RegExp(`[${es(this.specialCharacters)}]+`,"g")}_customTokensStr="";_excludePaths=[];_excludePathsFromAttachmentCollecting=[];_excludePathsFromAttachmentCollectingRegExp=so;_excludePathsRegExp=so;_includePaths=[];_includePathsRegExp=_p;isExcludedFromAttachmentCollecting(e){return this._excludePathsFromAttachmentCollectingRegExp.test(e)}isPathIgnored(e){return!this._includePathsRegExp.test(e)||this._excludePathsRegExp.test(e)}};function xu(t,e){if(t.length===0)return e;let n=t.map(r=>r.startsWith("/")&&r.endsWith("/")?r.slice(1,-1):`^${es(r)}`).map(r=>`(${r})`).join("|");return new RegExp(n)}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var kn=class{getTransformer(e){if(e===this.id)return this;throw new Error(`Transformer with id ${e} not found`)}transformObjectRecursively(e){return this.transformValueRecursively(e,"")}getTransformerId(e,n){return this.canTransform(e,n)?this.id:null}transformValueRecursively(e,n){let r=this.getTransformerId(e,n);if(r){let l=this.transformValue(e,n);return l===void 0?void 0:{__transformerId:r,transformedValue:l}}if(e===null)return null;if(typeof e!="object")return e;if(Array.isArray(e))return e.map((l,u)=>this.transformValueRecursively(l,u.toString()));let s=e;if(s.__transformerId)return this.getTransformer(s.__transformerId).restoreValue(s.transformedValue,n);let o={};for(let l of gr(e)){let u=e[l],d=this.transformValueRecursively(u,l);o[l]=d}return o}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var bt=class extends kn{};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var ao=class extends bt{get id(){return"date"}canTransform(e){return e instanceof Date}restoreValue(e){return new Date(e)}transformValue(e){return e.toISOString()}};var Fp=Ne(jt(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var lo=class extends bt{get id(){return"duration"}canTransform(e){let n=e??{};return!!n.asHours&&!!n.asMinutes&&!!n.asSeconds&&!!n.asMilliseconds}restoreValue(e){return(0,Fp.duration)(e)}transformValue(e){return e.toISOString()}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var uo=class extends kn{constructor(e){super(),this.transformers=e}get id(){return"group"}canTransform(e,n){return this.getFirstTransformerThatCanTransform(e,n)!==null}getTransformer(e){return this.transformers.find(n=>n.id===e)??Kt(`No transformer with id ${e} found`)}transformValue(e,n){let r=this.getFirstTransformerThatCanTransform(e,n);if(r===null)throw new Error("No transformer can transform the value");return r.transformValue(e,n)}getTransformerId(e,n){let r=this.getFirstTransformerThatCanTransform(e,n);return r===null?null:r.id}restoreValue(){throw new Error("GroupTransformer does not support restoring values")}getFirstTransformerThatCanTransform(e,n){return this.transformers.find(r=>r.canTransform(e,n))??null}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var co=class extends bt{get id(){return"map"}canTransform(e){return e instanceof Map}restoreValue(e){return new Map(e)}transformValue(e){return Array.from(e.entries())}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var fo=class extends bt{get id(){return"set"}canTransform(e){return e instanceof Set}restoreValue(e){return new Set(e)}transformValue(e){return Array.from(e)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var BC="_",ho=class extends kn{get id(){return"skip-private-property"}canTransform(e,n){return n.startsWith(BC)}transformValue(){}restoreValue(){throw new Error("SkipPrivatePropertyTransformer does not support restoring values")}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var bi=class{keyValueMap=new Map;valueKeyMap=new Map;constructor(e=[]){for(let[n,r]of e)this.set(n,r)}clear(){this.keyValueMap.clear(),this.valueKeyMap.clear()}deleteKey(e){let n=this.getValue(e);n!==void 0&&this.valueKeyMap.delete(n),this.keyValueMap.delete(e)}deleteValue(e){let n=this.getKey(e);n!==void 0&&this.keyValueMap.delete(n),this.valueKeyMap.delete(e)}entries(){return this.keyValueMap.entries()}getKey(e){return this.valueKeyMap.get(e)}getValue(e){return this.keyValueMap.get(e)}hasKey(e){return this.keyValueMap.has(e)}hasValue(e){return this.valueKeyMap.has(e)}keys(){return this.keyValueMap.keys()}set(e,n){this.deleteKey(e),this.deleteValue(n),this.keyValueMap.set(e,n),this.valueKeyMap.set(n,e)}values(){return this.valueKeyMap.keys()}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var mo=class extends bt{get id(){return"two-way-map"}canTransform(e){return e instanceof bi}restoreValue(e){return new bi(e)}transformValue(e){return Array.from(e.entries())}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var $C=new uo([new ho,new ao,new lo,new co,new fo,new mo]),po=class extends Pt{constructor(e){super(),this.plugin=e,this.app=e.app,this.defaultSettings=this.createDefaultSettings(),this.currentSettingsWrapper=this.createDefaultSettingsWrapper(),this.lastSavedSettingsWrapper=this.createDefaultSettingsWrapper(),this.propertyNames=gr(this.currentSettingsWrapper.settings),this.registerValidators()}app;defaultSettings;get settingsWrapper(){return this.currentSettingsWrapper}currentSettingsWrapper;lastSavedSettingsWrapper;propertyNames;validators=new Map;async editAndSave(e,n){await this.edit(e),await this.saveToFile(n)}async ensureSafe(e){let n=await this.validate(e);for(let r of this.propertyNames)n[r]&&(e[r]=this.defaultSettings[r])}async getSafeCopy(e){let n=await this.cloneSettings(e);return await this.ensureSafe(n),n}async loadFromFile(e){let n=await this.plugin.loadData();this.lastSavedSettingsWrapper=this.createDefaultSettingsWrapper(),this.currentSettingsWrapper=this.createDefaultSettingsWrapper();try{if(n==null)return;if(typeof n!="object"){console.error(`Invalid settings from data.json. Expected Object, got: ${typeof n}`);return}let r=n,s=await this.rawRecordToSettings(r),o=await this.validate(s);for(let u of this.propertyNames)this.setPropertyImpl(u,s[u],o[u]);this.lastSavedSettingsWrapper=await this.cloneSettingsWrapper(this.currentSettingsWrapper);let l=await this.settingsToRawRecord(this.currentSettingsWrapper.settings);pt(l,n)||await this.saveToFileImpl()}finally{await this.triggerAsync("loadSettings",this.currentSettingsWrapper,e)}}on(e,n,r){return super.on(e,n,r)}async saveToFile(e){pt(this.lastSavedSettingsWrapper.settings,this.currentSettingsWrapper.settings)||(await this.saveToFileImpl(),await this.triggerAsync("saveSettings",this.currentSettingsWrapper,this.lastSavedSettingsWrapper,e),this.lastSavedSettingsWrapper=await this.cloneSettingsWrapper(this.currentSettingsWrapper))}async setProperty(e,n){return await this.edit(r=>{r[e]=n}),this.currentSettingsWrapper.validationMessages[e]}async validate(e){let n={};for(let[r,s]of this.validators.entries()){let o=await s(e[r],e);o&&(n[r]=o)}return n}getTransformer(){return $C}async onLoadRecord(e){await Tt()}async onSavingRecord(e){await Tt()}registerValidator(e,n){this.validators.set(e,n)}registerValidators(){}async cloneSettings(e){let n=await this.settingsToRawRecord(e),r=JSON.stringify(n),s=JSON.parse(r);return await this.rawRecordToSettings(s)}async cloneSettingsWrapper(e){return{safeSettings:await this.cloneSettings(e.safeSettings),settings:await this.cloneSettings(e.settings),validationMessages:{...e.validationMessages}}}createDefaultSettingsWrapper(){return{safeSettings:this.createDefaultSettings(),settings:this.createDefaultSettings(),validationMessages:{}}}async edit(e){try{await e(this.currentSettingsWrapper.settings)}finally{let n=await this.validate(this.currentSettingsWrapper.settings);for(let r of this.propertyNames){let s=n[r]??"";this.currentSettingsWrapper.validationMessages[r]=s,this.currentSettingsWrapper.safeSettings[r]=s?this.defaultSettings[r]:this.currentSettingsWrapper.settings[r]}}}isValidPropertyName(e){return typeof e!="string"?!1:this.propertyNames.includes(e)}async rawRecordToSettings(e){e=this.getTransformer().transformObjectRecursively(e),await this.onLoadRecord(e);let n=this.createDefaultSettings();for(let[r,s]of Object.entries(e)){if(!this.isValidPropertyName(r)){console.warn(`Unknown property: ${r}`);continue}typeof s!=typeof this.defaultSettings[r]&&console.warn("Possible invalid value type. It might lead to an unexpected behavior of the plugin. There is also a chance it is a false-negative warning, as we are unable to determine the exact type of the value in runtime.",{defaultValue:this.defaultSettings[r],propertyName:r,value:s}),n[r]=s}return n}async saveToFileImpl(){await this.plugin.saveData(await this.settingsToRawRecord(this.currentSettingsWrapper.settings))}setPropertyImpl(e,n,r){this.currentSettingsWrapper.settings[e]=n,this.currentSettingsWrapper.validationMessages[e]=r??"",this.currentSettingsWrapper.safeSettings[e]=r?this.defaultSettings[e]:n}async settingsToRawRecord(e){let n={};for(let r of this.propertyNames)n[r]=e[r];return await this.onSavingRecord(n),this.getTransformer().transformObjectRecursively(n)}};var go=class extends po{createDefaultSettings(){return new oo}async onLoadRecord(e){await super.onLoadRecord(e);let n=e,r=n.dateTimeFormat??"YYYYMMDDHHmmssSSS";n.attachmentFolderPath=Pp(n.attachmentFolderPath??"",r),n.generatedAttachmentFilename=Pp(n.generatedAttachmentFilename??n.pastedFileName??n.pastedImageFileName??"file-${date}",r),n.replaceWhitespace!==void 0&&(n.whitespaceReplacement=n.replaceWhitespace?"-":""),n.autoRenameFiles!==void 0&&(n.shouldRenameAttachmentFiles=n.autoRenameFiles),n.autoRenameFolder!==void 0&&(n.shouldRenameAttachmentFolder=n.autoRenameFolder),n.deleteOrphanAttachments!==void 0&&(n.shouldDeleteOrphanAttachments=n.deleteOrphanAttachments),n.keepEmptyAttachmentFolders!==void 0&&(n.shouldKeepEmptyAttachmentFolders=n.keepEmptyAttachmentFolders),n.renameCollectedFiles!==void 0&&(n.shouldRenameCollectedAttachments=n.renameCollectedFiles),n.toLowerCase!==void 0&&(n.shouldRenameAttachmentsToLowerCase=n.toLowerCase),n.convertImagesToJpeg!==void 0&&(n.shouldConvertPastedImagesToJpeg=n.convertImagesToJpeg),n.whitespaceReplacement&&(n.specialCharacters=`${n.specialCharacters??""} `,n.specialCharactersReplacement=n.whitespaceReplacement),n.shouldKeepEmptyAttachmentFolders!==void 0&&(n.emptyAttachmentFolderBehavior=n.shouldKeepEmptyAttachmentFolders?nt.Keep:nt.DeleteWithEmptyParents),n.attachmentFolderPath=this.replaceLegacyTokens(n.attachmentFolderPath),n.generatedAttachmentFilename=this.replaceLegacyTokens(n.generatedAttachmentFilename),n.customTokensStr=this.replaceLegacyTokens(n.customTokensStr??"")}registerValidators(){this.registerValidator("attachmentFolderPath",e=>wi(e)),this.registerValidator("generatedAttachmentFilename",e=>wi(e)),this.registerValidator("specialCharacters",e=>{if(e.includes("/"))return"Special characters must not contain /"}),this.registerValidator("specialCharactersReplacement",e=>{if(bu.exec(e))return"Special character replacement must not contain invalid filename path characters."}),this.registerValidator("duplicateNameSeparator",e=>no(`filename${e}1`,!1)),this.registerValidator("includePaths",e=>Ip(e)),this.registerValidator("excludePaths",e=>Ip(e)),this.registerValidator("customTokensStr",e=>{YC(e)})}replaceLegacyTokens(e){let n={fileCreationDate:"noteFileCreationDate",fileModificationDate:"noteFileModificationDate",fileName:"noteFileName",filePath:"noteFilePath",folderName:"noteFolderName",folderPath:"noteFolderPath",originalCopiedFileExtension:"originalAttachmentFileExtension",originalCopiedFileName:"originalAttachmentFileName",randomDigit:"random:D",randomDigitOrLetter:"random:DL",randomLetter:"random:L",uuid:"random:uuid"};for(let[r,s]of Object.entries(n))e=be(e,new RegExp(`\\\${${r}(?<Suffix>[:}])`,"i"),`\${${s}$<Suffix>`),e=be(e,`substitutions.${r}`,`substitutions.${s}`);return e}};function Pp(t,e){return t.replaceAll("${date}",`\${date:${e}}`)}function YC(t){if(ku(t)===null)return"Invalid custom tokens code"}function Ip(t){for(let e of t)if(e.startsWith("/")&&e.endsWith("/")){let n=e.slice(1,-1);if(!Qc(n))return`Invalid regular expression ${e}`}}var Up=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function Au(t,e){let n=Object.keys(t).find(r=>t[r]===e);if(n===void 0)throw new Error(`Invalid enum value: ${e}`);return n}function Eu(t,e){let n=t[e];if(n===void 0)throw new Error(`Invalid enum key: ${e}`);return n}var Wn=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function wo(t){let e=t.parentElement;if(!e)throw new Error("Element must be attached to the DOM");if(e.classList.contains(B.SettingComponentWrapper))return e;let n=Array.from(e.children),r=createDiv();ue(r,B.SettingComponentWrapper);for(let s of n)r.appendChild(s);return e.appendChild(r),r}var Rp=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var vu=class{constructor(e){this.abstractTextComponent=e}empty(){this.abstractTextComponent.setValue("")}isEmpty(){return this.abstractTextComponent.getValue()===""}setPlaceholderValue(e){return this.abstractTextComponent.setPlaceholder(e),this}};function Dp(t){return VC(t)?t:t instanceof Rp.AbstractTextComponent?new vu(t):null}function VC(t){let e=t;return typeof e.setPlaceholderValue=="function"&&typeof e.isEmpty=="function"}var Ve=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var yo=class{constructor(e){this.el=e;let n=wo(e);this._validatorEl=n.createEl("input",{attr:{tabindex:-1}}),ue(this._validatorEl,B.OverlayValidator),this._validatorEl.addEventListener("focus",()=>{this.el.focus()}),this._validatorEl.isActiveElement=this.isElementOrDescendantActive.bind(this);let r=this.el.querySelector("[tabindex]");r||(this.el.getAttr("tabindex")===null&&(this.el.tabIndex=-1),r=this.el),this.el.addEventListener("focusin",()=>{this.forceBlurValidatorEl()}),this.el.addEventListener("click",()=>{r.focus()}),this.el.addEventListener("focusout",()=>{setTimeout(()=>{this.isElementOrDescendantActive()||this.forceBlurValidatorEl()},0)})}get validatorEl(){return this._validatorEl}_validatorEl;forceBlurValidatorEl(){this._validatorEl.dispatchEvent(new Event("blur"))}isElementOrDescendantActive(){return this.el.contains(document.activeElement)}},xn=class{constructor(e){this.validatorEl=e}};function Op(t){return zC(t)?t:t instanceof Ve.ColorComponent?new xn(t.colorPickerEl):t instanceof Ve.DropdownComponent?new xn(t.selectEl):t instanceof Ve.ProgressBarComponent?new yo(t.progressBar):t instanceof Ve.SearchComponent?new xn(t.inputEl):t instanceof Ve.SliderComponent?new xn(t.sliderEl):t instanceof Ve.TextAreaComponent?new xn(t.inputEl):t instanceof Ve.TextComponent?new xn(t.inputEl):t instanceof Ve.ToggleComponent?new yo(t.toggleEl):null}function zC(t){return!!t.validatorEl}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();function Lp(t){return t.validationMessage!==void 0}(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Cu="PluginSettingsTab",bo=class extends Wn.PluginSettingTab{constructor(e){super(e.app,e),this.plugin=e,ue(this.containerEl,B.PluginSettingsTab),this.saveSettingsDebounced=(0,Wn.debounce)(st(()=>this.plugin.settingsManager.saveToFile(Cu)),this.saveSettingsDebounceTimeoutInMilliseconds),this.asyncEventsComponent=new Ms,this.asyncEvents=new Pt}get isOpen(){return this._isOpen}get saveSettingsDebounceTimeoutInMilliseconds(){return 2e3}_isOpen=!1;asyncEvents;asyncEventsComponent;saveSettingsDebounced;get pluginSettings(){return this.plugin.settingsManager.settingsWrapper.settings}bind(e,n,r){let o={...{componentToPluginSettingsValueConverter:_=>_,onChanged:St,pluginSettingsToComponentValueConverter:_=>_,shouldResetSettingWhenComponentIsEmpty:!0,shouldShowPlaceholderForDefaultValues:!0,shouldShowValidationMessage:!0},...r},l=Op(e)?.validatorEl,u=Dp(e),d=this.pluginSettings[n],f=this.plugin.settingsManager.defaultSettings[n];u?.setPlaceholderValue(o.pluginSettingsToComponentValueConverter(f));let m,p=null,y=null;if(l){let _=wo(l);p=_.createDiv(),ue(p,B.Tooltip,B.TooltipValidator),y=p.createSpan();let F=p.createDiv();ue(F,B.TooltipArrow),p.hide(),_.appendChild(p)}this.asyncEventsComponent.registerAsyncEvent(this.on("validationMessageChanged",(_,F)=>{n===_&&(m=F,S())}));let g=!1;u&&o.shouldShowPlaceholderForDefaultValues&&pt(d,f)?u.empty():e.setValue(o.pluginSettingsToComponentValueConverter(d));let A=!1,S=(0,Wn.debounce)(()=>{requestAnimationFrame(()=>{E()})},100);return e.onChange(async _=>{if(A){A=!1;return}g=!1;let F=this.pluginSettings[n],z,W=!0;if(u?.isEmpty()&&o.shouldResetSettingWhenComponentIsEmpty)z=f;else{let C=o.componentToPluginSettingsValueConverter(_);Lp(C)?(m=C.validationMessage,W=!1):z=C}W&&(m=await this.plugin.settingsManager.setProperty(n,z),u&&o.shouldShowPlaceholderForDefaultValues&&!u.isEmpty()&&pt(z,f)&&(g=!0)),S(),z!==void 0&&await o.onChanged(z,F),this.saveSettingsDebounced()}),l?.addEventListener("focus",()=>{S()}),l?.addEventListener("blur",()=>{S()}),l?.addEventListener("click",()=>{requestAnimationFrame(()=>{S()})}),m=this.plugin.settingsManager.settingsWrapper.validationMessages[n]??"",S(),e;function E(){g&&!l?.isActiveElement()&&(g=!1,u?.isEmpty()||(A=!0,u?.empty())),l&&(m===""&&(l.setCustomValidity(""),l.checkValidity(),m=l.validationMessage),l.setCustomValidity(m),o.shouldShowValidationMessage?(y&&(y.textContent=m),p?.toggle(!!m)):m&&(0,Wn.setTooltip)(l,m))}}display(){this.containerEl.empty(),this._isOpen=!0,this.asyncEventsComponent.load(),this.asyncEventsComponent.registerAsyncEvent(this.plugin.settingsManager.on("loadSettings",this.onLoadSettings.bind(this))),this.asyncEventsComponent.registerAsyncEvent(this.plugin.settingsManager.on("saveSettings",this.onSaveSettings.bind(this)))}hide(){super.hide(),this.saveSettingsDebounced.cancel(),this._isOpen=!1,this.asyncEventsComponent.unload(),this.asyncEventsComponent.load(),et(()=>this.hideAsync())}async hideAsync(){await this.plugin.settingsManager.saveToFile(Cu)}show(){this.app.setting.openTab(this)}async onLoadSettings(e,n){this.display(),await Tt()}on(e,n,r){return this.asyncEvents.on(e,n,r)}async onSaveSettings(e,n,r){if(r===Cu){for(let[s,o]of Object.entries(e.validationMessages))await this.asyncEvents.triggerAsync("validationMessageChanged",s,o);return}this.display()}};var Wp=require("obsidian");var Mp=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var ko=class extends Mp.ValueComponent{inputEl;get validatorEl(){return this.inputEl}changeCallback;constructor(e){super(),ue(e,B.CheckboxComponent),this.inputEl=e.createEl("input",{type:"checkbox"}),this.inputEl.addEventListener("change",this.onChanged.bind(this))}getValue(){return this.inputEl.checked}onChange(e){return this.changeCallback=e,this}onChanged(){this.changeCallback?.(this.getValue())}setDisabled(e){return super.setDisabled(e),this.inputEl.disabled=e,this}setValue(e){return this.inputEl.checked=e,this}};var Tr=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var xo=class extends Tr.ValueComponent{get inputEl(){return this.textAreaComponent.inputEl}get validatorEl(){return this.inputEl}codeEl;placeholder="";preEl;tabSize;textAreaComponent;constructor(e){super(),ue(e,B.CodeHighlighterComponent);let n=e.createDiv();ue(n,B.SettingComponentWrapper),this.textAreaComponent=new Tr.TextAreaComponent(n),this.preEl=n.createEl("pre"),this.codeEl=this.preEl.createEl("code"),this.inputEl.addEventListener("input",st(this.updateHighlightedCode.bind(this))),this.inputEl.addEventListener("scroll",this.handleScroll.bind(this)),this.inputEl.addEventListener("keydown",this.handleKeyDown.bind(this));let r=2;this.tabSize=r}empty(){this.setValue("")}getValue(){return this.textAreaComponent.getValue()}isEmpty(){return this.textAreaComponent.getValue()===""}onChange(e){return this.textAreaComponent.onChange(()=>e(this.getValue())),this}setDisabled(e){return super.setDisabled(e),this.textAreaComponent.setDisabled(e),this}setLanguage(e){let n="language-";for(let r of[this.preEl,this.codeEl]){for(let s of Array.from(r.classList))s.startsWith(n)&&r.classList.remove(s);r.classList.add(`${n}${e}`)}return this}setPlaceholder(e){return this.placeholder=e,et(this.updateHighlightedCode.bind(this)),this}setPlaceholderValue(e){return this.setPlaceholder(e),this}setTabSize(e){return this.tabSize=e,this}setValue(e){return this.textAreaComponent.setValue(e),et(this.updateHighlightedCode.bind(this)),this}handleKeyDown(e){if(e.key!=="Tab")return;e.preventDefault();let n=this.getValue(),r=this.inputEl.selectionStart,s=this.inputEl.selectionEnd,o=n.slice(0,r),l=n.slice(s),u=" ".repeat(this.tabSize),d=o;e.shiftKey?o.endsWith(u)&&(d=o.slice(0,-this.tabSize)):d=o+u;let f=`${d}${l}`;this.setValue(f),this.inputEl.selectionStart=d.length,this.inputEl.selectionEnd=d.length}handleScroll(){this.preEl.scrollTop=this.inputEl.scrollTop,this.preEl.scrollLeft=this.inputEl.scrollLeft}async updateHighlightedCode(){this.codeEl.textContent=this.inputEl.value||this.placeholder,(await(0,Tr.loadPrism)()).highlightElement(this.codeEl),this.preEl.toggleClass(B.IsPlaceholder,this.isEmpty())}};var Su=Ne(jt(),1);var Ao=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Ie=class extends Ao.ValueComponent{inputEl;get validatorEl(){return this.inputEl}textComponent;constructor(e,n,r){super(),this.textComponent=new Ao.TextComponent(e),this.inputEl=this.textComponent.inputEl,this.inputEl.type=n,ue(e,r)}getValue(){return this.valueFromString(this.textComponent.getValue())}onChange(e){return this.textComponent.onChange(()=>e(this.getValue())),this}onChanged(){this.textComponent.onChanged()}setDisabled(e){return super.setDisabled(e),this.textComponent.setDisabled(e),this}setValue(e){return this.textComponent.setValue(this.valueToString(e)),this}valueToString(e){return String(e)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var it=class extends Ie{setMax(e){return this.inputEl.max=this.valueToString(e),this}setMin(e){return this.inputEl.min=this.valueToString(e),this}setStep(e){return this.inputEl.step=e.toString(),this}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Np="YYYY-MM-DD",Eo=class extends it{constructor(e){super(e,"date",B.DateComponent)}valueFromString(e){return(0,Su.default)(e,Np).toDate()}valueToString(e){return(0,Su.default)(e).format(Np)}};var Tu=Ne(jt(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Bp="YYYY-MM-DDTHH:mm",vo=class extends it{constructor(e){super(e,"datetime-local",B.DateTimeComponent)}valueFromString(e){return(0,Tu.default)(e,Bp).toDate()}valueToString(e){return(0,Tu.default)(e).format(Bp)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Co=class extends Ie{constructor(e){super(e,"email",B.EmailComponent)}empty(){this.setValue("")}isEmpty(){return this.getValue()===""}setPlaceholderValue(e){return this.textComponent.setPlaceholder(e),this}valueFromString(e){return e}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var So=class extends Ie{constructor(e){super(e,"file",B.FileComponent)}getValue(){return this.inputEl.files?.[0]??null}valueFromString(){return this.getValue()}valueToString(e){return e?.name??""}};var _u=Ne(jt(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var $p="YYYY-MM",To=class extends it{constructor(e){super(e,"month",B.MonthComponent)}valueFromString(e){let n=(0,_u.default)(e,$p);if(!n.isValid())throw new Error("Invalid month");return{month:n.month()+1,year:n.year()}}valueToString(e){return(0,_u.default)().year(e.year).month(e.month-1).format($p)}};var _o=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var _r=class extends _o.ValueComponent{get selectEl(){return this.dropdownComponent.selectEl}get validatorEl(){return this.selectEl}dropdownComponent;constructor(e){super(),this.dropdownComponent=new _o.DropdownComponent(e),this.dropdownComponent.selectEl.multiple=!0,ue(e,B.MultipleDropdownComponent)}addOption(e,n){return this.dropdownComponent.addOption(e,n),this}addOptions(e){return this.dropdownComponent.addOptions(e),this}getValue(){return Array.from(this.dropdownComponent.selectEl.selectedOptions).map(e=>e.value)}onChange(e){return this.dropdownComponent.onChange(()=>e(this.getValue())),this}setDisabled(e){return super.setDisabled(e),this.dropdownComponent.setDisabled(e),this}setValue(e){for(let n of Array.from(this.dropdownComponent.selectEl.options))n.selected=e.includes(n.value);return this}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Fo=class extends Ie{constructor(e){super(e,"email",B.MultipleEmailComponent),this.inputEl.multiple=!0}valueFromString(e){return e.split(",").map(n=>n.trim())}valueToString(e){return e.join(", ")}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Po=class extends Ie{constructor(e){super(e,"file",B.MultipleFileComponent),this.inputEl.multiple=!0}getValue(){return Array.from(this.inputEl.files??[])}valueFromString(){return this.getValue()}valueToString(e){return e[0]?.name??""}};var Ro=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Io=class extends Ro.ValueComponent{get inputEl(){return this.textAreaComponent.inputEl}get validatorEl(){return this.inputEl}textAreaComponent;constructor(e){super(),this.textAreaComponent=new Ro.TextAreaComponent(e),ue(e,B.MultipleTextComponent)}empty(){this.textAreaComponent.setValue("")}getValue(){return this.textAreaComponent.getValue().split(`
`)}isEmpty(){return this.textAreaComponent.getValue()===""}onChange(e){return this.textAreaComponent.onChange(()=>e(this.getValue())),this}setDisabled(e){return super.setDisabled(e),this.textAreaComponent.setDisabled(e),this}setPlaceholder(e){return this.textAreaComponent.setPlaceholder(e),this}setPlaceholderValue(e){return this.setPlaceholder(this.valueToString(e)),this}setValue(e){return this.textAreaComponent.setValue(this.valueToString(e)),this}valueToString(e){return e.join(`
`)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Do=class extends it{constructor(e){super(e,"number",B.NumberComponent)}empty(){this.textComponent.setValue("")}isEmpty(){return this.textComponent.getValue()===""}setPlaceholder(e){return this.textComponent.setPlaceholder(e),this}setPlaceholderValue(e){return this.textComponent.setPlaceholder(this.valueToString(e)),this}valueFromString(e){return parseInt(e,10)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Oo=class extends Ie{constructor(e){super(e,"password",B.PasswordComponent)}setPlaceholder(e){return this.textComponent.setPlaceholder(e),this}valueFromString(e){return e}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Lo=class extends Ie{constructor(e){super(e,"tel",B.TelephoneComponent)}empty(){this.textComponent.setValue("")}isEmpty(){return this.textComponent.getValue()===""}setPlaceholderValue(e){return this.textComponent.setPlaceholder(e),this}valueFromString(e){return e}};var No=Ne(jt(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Mo=class extends it{constructor(e){super(e,"time",B.TimeComponent)}valueFromString(e){return(0,No.duration)(e)}valueToString(e){let n;return e.milliseconds()>0?n="HH:mm:ss.SSS":e.seconds()>0?n="HH:mm:ss":n="HH:mm",(0,No.utc)(e.asMilliseconds()).format(n)}};var Yp=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Bo=class extends Yp.ValueComponent{inputEl;get validatorEl(){return this.inputEl}changeCallback;constructor(e){super(),ue(e,B.TriStateCheckboxComponent),this.inputEl=e.createEl("input",{type:"checkbox"}),this.inputEl.addEventListener("change",this.onChanged.bind(this))}getValue(){return this.inputEl.indeterminate?null:this.inputEl.checked}onChange(e){return this.changeCallback=e,this}onChanged(){this.changeCallback?.(this.getValue())}setDisabled(e){return super.setDisabled(e),this.inputEl.disabled=e,this}setValue(e){return this.inputEl.indeterminate=e===null,this.inputEl.checked=e??!1,this}};var Yo=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var $o=class extends Yo.ValueComponent{get selectEl(){return this.dropdownComponent.selectEl}get validatorEl(){return this.selectEl}dropdownComponent;values=[];constructor(e){super(),this.dropdownComponent=new Yo.DropdownComponent(e),ue(e,B.TypedDropdownComponent)}addOption(e,n){let r=this.values.indexOf(e);return r===-1&&(this.values.push(e),r=this.values.length-1),this.dropdownComponent.addOption(r.toString(),n),this}addOptions(e){for(let[n,r]of e.entries())this.addOption(n,r);return this}getValue(){return this.values[this.dropdownComponent.selectEl.selectedIndex]??null}onChange(e){return this.dropdownComponent.onChange(()=>e(this.getValue())),this}setDisabled(e){return super.setDisabled(e),this.dropdownComponent.setDisabled(e),this}setValue(e){let n=e===null?-1:this.values.indexOf(e);return this.dropdownComponent.selectEl.selectedIndex=n,this}};var Vp=require("obsidian");(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var Vo=class extends Vp.ValueComponent{get selectEl(){return this.multipleDropdownComponent.selectEl}get validatorEl(){return this.selectEl}multipleDropdownComponent;values=[];constructor(e){super(),this.multipleDropdownComponent=new _r(e),ue(e,B.TypedMultipleDropdownComponent)}addOption(e,n){let r=this.values.indexOf(e);return r===-1&&(this.values.push(e),r=this.values.length-1),this.multipleDropdownComponent.addOption(r.toString(),n),this}addOptions(e){for(let[n,r]of e.entries())this.addOption(n,r);return this}getValue(){return this.multipleDropdownComponent.getValue().map(n=>parseInt(n,10)).map(n=>this.values[n]).filter(n=>n!==void 0)}onChange(e){return this.multipleDropdownComponent.onChange(()=>e(this.getValue())),this}setDisabled(e){return super.setDisabled(e),this.multipleDropdownComponent.setDisabled(e),this}setValue(e){let n=e.map(r=>this.values.indexOf(r)).filter(r=>r!==-1);return this.multipleDropdownComponent.setValue(n.map(r=>r.toString())),this}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var zo=class extends Ie{constructor(e){super(e,"url",B.UrlComponent)}empty(){this.setValue("")}isEmpty(){return this.getValue()===""}setPlaceholderValue(e){return this.textComponent.setPlaceholder(e),this}valueFromString(e){return e}};var Fu=Ne(jt(),1);(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var zp="YYYY-[W]WW",Wo=class extends it{constructor(e){super(e,"week",B.WeekComponent)}valueFromString(e){let n=(0,Fu.default)(e,zp);if(!n.isValid())throw new Error("Invalid week");return{weekNumber:n.isoWeek(),year:n.year()}}valueToString(e){return(0,Fu.default)().year(e.year).isoWeek(e.weekNumber).format(zp)}};(function(){if(globalThis.process)return;let e={browser:!0,cwd:__name(()=>"/","cwd"),env:{},platform:"android"};globalThis.process=e})();var pe=class extends Wp.Setting{addCheckbox(e){return this.addComponent(ko,e)}addCodeHighlighter(e){return this.addComponent(xo,e)}addComponent(e,n){let r=new e(this.controlEl);return this.components.push(r),n(r),this}addDate(e){return this.addComponent(Eo,e)}addDateTime(e){return this.addComponent(vo,e)}addEmail(e){return this.addComponent(Co,e)}addFile(e){return this.addComponent(So,e)}addMonth(e){return this.addComponent(To,e)}addMultipleDropdown(e){return this.addComponent(_r,e)}addMultipleEmail(e){return this.addComponent(Fo,e)}addMultipleFile(e){return this.addComponent(Po,e)}addMultipleText(e){return this.addComponent(Io,e)}addNumber(e){return this.addComponent(Do,e)}addPassword(e){return this.addComponent(Oo,e)}addTelephone(e){return this.addComponent(Lo,e)}addTime(e){return this.addComponent(Mo,e)}addTriStateCheckbox(e){return this.addComponent(Bo,e)}addTypedDropdown(e){return this.addComponent($o,e)}addTypedMultipleDropdown(e){return this.addComponent(Vo,e)}addUrl(e){return this.addComponent(zo,e)}addWeek(e){return this.addComponent(Wo,e)}};var Ho=require("obsidian");var Fr="custom-attachment-location-tokenized-string",Uo=class extends Ho.Component{onload(){super.onload(),et(this.initPrism.bind(this))}async initPrism(){let e=await(0,Ho.loadPrism)();e.languages[Fr]={expression:{greedy:!0,inside:{format:{alias:"number",pattern:/[a-zA-Z0-9_]+/},formatDelimiter:{alias:"regex",pattern:/:/},prefix:{alias:"regex",pattern:/[${}]/},token:{alias:"string",pattern:/^[a-zA-Z0-9_]+/}},pattern:/\${[a-zA-Z0-9_]+(?::[a-zA-Z0-9_]+)?}/},important:{pattern:/^\./},operator:{alias:"entity",pattern:/\//}},this.register(()=>{delete e.languages[Fr]})}};var qo="\u2423",Go=class extends bo{display(){super.display(),this.containerEl.empty(),new pe(this.containerEl).setName("Location for new attachments").setDesc(createFragment(e=>{e.appendText("Start with "),ce(e,"."),e.appendText(" to use relative path."),e.createEl("br"),e.appendText("See available "),e.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"tokens"}),e.createEl("br"),e.appendText("Dot-folders like "),ce(e,".attachments"),e.appendText(" are not recommended, because Obsidian doesn't track them. You might need to use "),e.createEl("a",{href:"https://github.com/polyipseity/obsidian-show-hidden-files/",text:"Show Hidden Files"}),e.appendText(" Plugin to manage them.")})).addCodeHighlighter(e=>{e.setLanguage(Fr),e.inputEl.addClass("tokenized-string-setting-control"),this.bind(e,"attachmentFolderPath",{componentToPluginSettingsValueConverter(n){return(0,Up.normalizePath)(n)},pluginSettingsToComponentValueConverter(n){return n}})}),new pe(this.containerEl).setName("Generated attachment filename").setDesc(createFragment(e=>{e.appendText("See available "),e.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"tokens"})})).addCodeHighlighter(e=>{e.setLanguage(Fr),e.inputEl.addClass("tokenized-string-setting-control"),this.bind(e,"generatedAttachmentFilename")}),new pe(this.containerEl).setName("Markdown URL format").setDesc(createFragment(e=>{e.appendText("Format for the URL that will be inserted into Markdown."),e.createEl("br"),e.appendText("See available "),e.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"tokens"}),e.createEl("br"),e.appendText("Leave blank to use the default format.")})).addCodeHighlighter(e=>{e.setLanguage(Fr),e.inputEl.addClass("tokenized-string-setting-control"),this.bind(e,"markdownUrlFormat")}),new pe(this.containerEl).setName("Attachment rename mode").setDesc(createFragment(e=>{e.appendText("When attaching files, "),e.createEl("br"),ce(e,"None"),e.appendText(" - their names are preserved, "),e.createEl("br"),ce(e,"Only pasted images"),e.appendText(" - only pasted images are renamed."),e.createEl("br"),ce(e,"All"),e.appendText(" - all files are renamed.")})).addDropdown(e=>{e.addOptions(Sr),this.bind(e,"attachmentRenameMode",{componentToPluginSettingsValueConverter:n=>Eu(Sr,n),pluginSettingsToComponentValueConverter:n=>Au(Sr,n)})}),new pe(this.containerEl).setName("Should rename attachment folder").setDesc(createFragment(e=>{e.appendText("When renaming md files, automatically rename attachment folder if folder name contains "),ce(e,"${filename}"),e.appendText(".")})).addToggle(e=>{this.bind(e,"shouldRenameAttachmentFolder")}),new pe(this.containerEl).setName("Should rename attachment files").setDesc(createFragment(e=>{e.appendText("When renaming md files, automatically rename attachment files if file name contains "),ce(e,"${filename}"),e.appendText(".")})).addToggle(e=>{this.bind(e,"shouldRenameAttachmentFiles")}),new pe(this.containerEl).setName("Special characters").setDesc(createFragment(e=>{e.appendText("Special characters in attachment folder and file name to be replaced or removed."),e.createEl("br"),e.appendText("Leave blank to preserve special characters.")})).addText(e=>{this.bind(e,"specialCharacters",{componentToPluginSettingsValueConverter:n=>n.replaceAll(qo,""),pluginSettingsToComponentValueConverter:n=>n.replaceAll(" ",qo),shouldResetSettingWhenComponentIsEmpty:!1}),e.inputEl.addEventListener("input",()=>{e.inputEl.value=Pu(e.inputEl.value)})}),new pe(this.containerEl).setName("Special characters replacement").setDesc(createFragment(e=>{e.appendText("Replacement string for special characters in attachment folder and file name."),e.createEl("br"),e.appendText("Leave blank to remove special characters.")})).addText(e=>{this.bind(e,"specialCharactersReplacement",{shouldResetSettingWhenComponentIsEmpty:!1})}),new pe(this.containerEl).setName("Should rename attachments to lowercase").setDesc("Automatically set all characters in folder name and pasted image name to be lowercase.").addToggle(e=>{this.bind(e,"shouldRenameAttachmentsToLowerCase")}),new pe(this.containerEl).setName("Should convert pasted images to JPEG").setDesc("Paste images from clipboard converting them to JPEG.").addToggle(e=>{this.bind(e,"shouldConvertPastedImagesToJpeg")}),new pe(this.containerEl).setName("JPEG Quality").setDesc("The smaller the quality, the greater the compression ratio.").addDropdown(e=>{e.addOptions(WC()),this.bind(e,"jpegQuality",{componentToPluginSettingsValueConverter:n=>Number(n),pluginSettingsToComponentValueConverter:n=>n.toString()})}),new pe(this.containerEl).setName("Should rename collected attachments").setDesc(createFragment(e=>{e.appendText("If enabled, attachments processed via "),ce(e,"Collect attachments"),e.appendText(" commands will be renamed according to the "),ce(e,"Pasted File Name"),e.appendText(" setting.")})).addToggle(e=>{this.bind(e,"shouldRenameCollectedAttachments")}),new pe(this.containerEl).setName("Should duplicate collected attachments").setDesc(createFragment(e=>{e.appendText("If enabled, for attachments processed via "),ce(e,"Collect attachments"),e.appendText(" command, that are linked by multiple notes, duplicate copy of those attachments will be created for each note."),e.createEl("br"),e.appendText("When disabled, such attachments will be kept in the original location.")})).addToggle(e=>{this.bind(e,"shouldDuplicateCollectedAttachments")}),new pe(this.containerEl).setName("Duplicate name separator").setDesc(createFragment(e=>{e.appendText("When you are pasting/dragging a file with the same name as an existing file, this separator will be added to the file name."),e.createEl("br"),e.appendText("E.g., when you are dragging file "),ce(e,"existingFile.pdf"),e.appendText(", it will be renamed to "),ce(e,"existingFile 1.pdf"),e.appendText(", "),ce(e,"existingFile 2.pdf"),e.appendText(", etc, getting the first name available.")})).addText(e=>{this.bind(e,"duplicateNameSeparator",{componentToPluginSettingsValueConverter:n=>n.replaceAll(qo," "),pluginSettingsToComponentValueConverter:Pu}),e.inputEl.addEventListener("input",()=>{e.inputEl.value=Pu(e.inputEl.value)})}),new pe(this.containerEl).setName("Empty attachment folder behavior").setDesc(createFragment(e=>{e.appendText("When the attachment folder becomes empty, "),e.createEl("br"),ce(e,"Keep"),e.appendText(" - will keep the empty attachment folder, "),e.createEl("br"),ce(e,"Delete"),e.appendText(" - will delete the empty attachment folder, "),e.createEl("br"),ce(e,"Delete with empty parents"),e.appendText(" - will delete the empty attachment folder and its empty parent folders.")})).addDropdown(e=>{e.addOptions({[nt.Keep]:"Keep",[nt.Delete]:"Delete",[nt.DeleteWithEmptyParents]:"Delete with empty parents"}),this.bind(e,"emptyAttachmentFolderBehavior",{componentToPluginSettingsValueConverter:n=>Eu(nt,n),pluginSettingsToComponentValueConverter:n=>Au(nt,n)})}),new pe(this.containerEl).setName("Should delete orphan attachments").setDesc("If enabled, when the note is deleted, its orphan attachments are deleted as well.").addToggle(e=>{this.bind(e,"shouldDeleteOrphanAttachments")}),new pe(this.containerEl).setName("Include paths").setDesc(createFragment(e=>{e.appendText("Include notes from the following paths"),e.createEl("br"),e.appendText("Insert each path on a new line"),e.createEl("br"),e.appendText("You can use path string or "),ce(e,"/regular expression/"),e.createEl("br"),e.appendText("If the setting is empty, all notes are included")})).addMultipleText(e=>{this.bind(e,"includePaths")}),new pe(this.containerEl).setName("Exclude paths").setDesc(createFragment(e=>{e.appendText("Exclude notes from the following paths"),e.createEl("br"),e.appendText("Insert each path on a new line"),e.createEl("br"),e.appendText("You can use path string or "),ce(e,"/regular expression/"),e.createEl("br"),e.appendText("If the setting is empty, no notes are excluded")})).addMultipleText(e=>{this.bind(e,"excludePaths")}),new pe(this.containerEl).setName("Exclude paths from attachment collecting").setDesc(createFragment(e=>{e.appendText("Exclude attachments from the following paths when "),ce(e,"Collect attachments"),e.appendText(" command is executed."),e.createEl("br"),e.appendText("Insert each path on a new line"),e.createEl("br"),e.appendText("You can use path string or "),ce(e,"/regular expression/"),e.createEl("br"),e.appendText("If the setting is empty, no paths are excluded from attachment collecting.")})).addMultipleText(e=>{this.bind(e,"excludePathsFromAttachmentCollecting")}),new pe(this.containerEl).setName("Custom tokens").setDesc(createFragment(e=>{e.appendText("Custom tokens to be used in the attachment folder path and pasted file name."),e.createEl("br"),e.appendText("See "),e.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#custom-tokens",text:"documentation"}),e.appendText(" for more information.")})).addCodeHighlighter(e=>{e.setLanguage("javascript"),e.inputEl.addClass("custom-tokens-setting-control"),this.bind(e,"customTokensStr"),e.setPlaceholder(`exports.myCustomToken1 = (substitutions, format) => {
  return substitutions.fileName + substitutions.app.appId + format;
};

exports.myCustomToken2 = async (substitutions, format) => {
  return await Promise.resolve(
    substitutions.fileName + substitutions.app.appId + format
  );
};`)}),new pe(this.containerEl).setName("Treat as attachment extensions").setDesc(createFragment(e=>{e.appendText("Treat files with these extensions as attachments."),e.createEl("br"),e.appendText("By default, "),ce(e,".md"),e.appendText(" and "),ce(e,".canvas"),e.appendText(" linked files are not treated as attachments and are not moved with the note."),e.createEl("br"),e.appendText("You can add custom extensions, e.g. "),ce(e,".foo.md"),e.appendText(", "),ce(e,".bar.canvas"),e.appendText(", to override this behavior.")})).addMultipleText(e=>{this.bind(e,"treatAsAttachmentExtensions")})}};function WC(){let e={};for(let n=1;n<=10;n++){let r=(n/10).toFixed(1);e[r]=r}return e}function Pu(t){return t.replaceAll(" ",qo)}var UC=/Pasted image (?<Timestamp>\d{14})/,HC="YYYYMMDDHHmmss",qC=10,jo=class extends Bs{currentAttachmentFolderPath=null;lastOpenFilePath=null;pathMarkdownUrlMap=new Map;createSettingsManager(){return new go(this)}createSettingsTab(){return new Go(this)}async onLayoutReady(){await super.onLayoutReady(),Bn(this,this.app.vault,{getAvailablePath:()=>this.getAvailablePath.bind(this),getAvailablePathForAttachments:()=>{let e={isExtended:!0};return Object.assign(this.getAvailablePathForAttachments.bind(this),e)},getConfig:e=>n=>this.getConfig(e,n)}),Iu.webUtils&&Bn(this,Iu.webUtils,{getPathForFile:e=>n=>this.getPathForFile(n,e)}),Bn(this,this.app.fileManager,{generateMarkdownLink:e=>(n,r,s,o)=>this.generateMarkdownLink(e,n,r,s,o)}),(0,qp.compare)(this.settings.warningVersion,"8.1.0")<0&&(await dh({app:this.app,message:createFragment(e=>{e.appendText("In plugin version 8.1.0, some token names changed. Please update your tokens accordingly. Refer to the "),e.createEl("a",{href:"https://github.com/RainCat1998/obsidian-custom-attachment-location?tab=readme-ov-file#tokens",text:"documentation"}),e.appendText(" for more information.")})}),await this.settingsManager.editAndSave(e=>{e.warningVersion=this.manifest.version}))}async onloadImpl(){await super.onloadImpl(),xh(this,()=>({emptyAttachmentFolderBehavior:this.settings.emptyAttachmentFolderBehavior,isNote:n=>zn(this,n),isPathIgnored:n=>this.settings.isPathIgnored(n),shouldHandleDeletions:this.settings.shouldDeleteOrphanAttachments,shouldHandleRenames:!0,shouldRenameAttachmentFiles:this.settings.shouldRenameAttachmentFiles,shouldRenameAttachmentFolder:this.settings.shouldRenameAttachmentFolder,shouldUpdateFilenameAliases:!0})),this.addCommand({checkCallback:e=>Sp(this,e),id:"collect-attachments-current-note",name:"Collect attachments in current note"}),this.addCommand({checkCallback:e=>Cp(this,e),id:"collect-attachments-current-folder",name:"Collect attachments in current folder"}),this.addCommand({callback:()=>{Tp(this)},id:"collect-attachments-entire-vault",name:"Collect attachments in entire vault"}),this.registerEvent(this.app.workspace.on("file-menu",this.handleFileMenu.bind(this))),Bn(this,this.app,{saveAttachment:()=>(e,n,r)=>this.saveAttachment(e,n,r)}),this.addChild(new Uo),this.registerEvent(this.app.workspace.on("file-open",st(this.handleFileOpen.bind(this)))),this.registerEvent(this.app.vault.on("rename",st(this.handleRename.bind(this))))}generateMarkdownLink(e,n,r,s,o){let l=e.call(this.app.fileManager,n,r,s,o);if(!this.settings.markdownUrlFormat)return l;let u=this.pathMarkdownUrlMap.get(n.path);return u?(Os(l)&&(l=zl({app:this.app,isWikilink:!1,originalLink:l,sourcePathOrFile:r,targetPathOrFile:n})),Wl(l)?l.replace(/\]\(<.+?>\)/,`](<${u}>)`):l.replace(/\]\(.+?\)/,`](${ri(u)})`)):l}getAvailablePath(e,n){let r=0;for(;;){let s=fn(r===0?e:`${e}${this.settings.duplicateNameSeparator}${r.toString()}`,n);if(!Pn(this.app,s,!0))return s;r++}}async getAvailablePathForAttachments(e,n,r,s,o){let l;if(!r||!zn(this,r))l=await Ma(this.app,e,n,r,!0);else{let u=await yi(this,r.path,fn(e,n),o);l=this.app.vault.getAvailablePath(ve(u,e),n)}if(!s){let u=ft(l);await this.app.vault.exists(u)||(await Ts(this.app,u),this.settings.emptyAttachmentFolderBehavior===nt.Keep&&await this.app.vault.create(ve(u,".gitkeep"),""))}return l}getConfig(e,n){return n!=="attachmentFolderPath"||this.currentAttachmentFolderPath===null?e.call(this.app.vault,n):this.currentAttachmentFolderPath}getPathForFile(e,n){let r=e;return r.path?r.path:n(e)}handleFileMenu(e,n){n instanceof Hp.TFolder&&e.addItem(r=>{r.setTitle("Collect attachments in folder").setIcon("download").onClick(()=>io(this,n))})}async handleFileOpen(e){if(e===null){this.currentAttachmentFolderPath=null,this.lastOpenFilePath=null;return}e.path!==this.lastOpenFilePath&&(this.lastOpenFilePath=e.path,this.currentAttachmentFolderPath=await yi(this,e.path,"dummy.pdf"))}async handleRename(){await this.handleFileOpen(this.app.workspace.getActiveFile())}async saveAttachment(e,n,r){let s=this.app.workspace.getActiveFile();if(!s||this.settings.isPathIgnored(s.path))return await this.saveAttachmentCore(e,n,r);let o=!1,l=UC.exec(e);if(l){let f=l.groups?.Timestamp;if(f){let m=(0,Ru.default)(f,HC);m.isValid()&&(0,Ru.default)().diff(m,"seconds")<qC&&(o=!0)}}o&&n==="png"&&this.settings.shouldConvertPastedImagesToJpeg&&(n="jpg",r=await Hc(new Blob([r],{type:"image/png"}),this.settings.jpegQuality));let u=!1;switch(this.settings.attachmentRenameMode){case"All":u=!0;break;case"None":break;case"Only pasted images":u=o;break;default:throw new Error("Invalid attachment rename mode")}u&&(e=await ro(this,new yt({app:this.app,attachmentFileSizeInBytes:r.byteLength,noteFilePath:s.path,originalAttachmentFileName:fn(e,n)})));let d=await this.saveAttachmentCore(e,n,r);if(this.settings.markdownUrlFormat){let f=await new yt({app:this.app,attachmentFileSizeInBytes:r.byteLength,noteFilePath:s.path,originalAttachmentFileName:d.name}).fillTemplate(this.settings.markdownUrlFormat);this.pathMarkdownUrlMap.set(d.path,f)}else this.pathMarkdownUrlMap.delete(d.path);return d}async saveAttachmentCore(e,n,r){let s=this.app.workspace.getActiveFile(),o=await this.getAvailablePathForAttachments(e,n,s,!1,r.byteLength);return await this.app.vault.createBinary(o,r)}};var GC=jo;
/*! Bundled license information:

moment/moment.js:
  (*! moment.js *)
  (*! version : 2.30.1 *)
  (*! authors : Tim Wood, Iskren Chernev, Moment.js contributors *)
  (*! license : MIT *)
  (*! momentjs.com *)
*/

/* nosourcemap */