#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNS安全检查工具
用于检测DNS污染、劫持和中间人攻击
"""

import socket
import time
import subprocess
import sys
import json
from datetime import datetime
import concurrent.futures
import argparse

class DNSSecurityChecker:
    def __init__(self):
        self.test_domains = [
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'github.com', 'stackoverflow.com', 'wikipedia.org',
            'cloudflare.com', 'baidu.com', 'qq.com'
        ]
        
        self.dns_servers = {
            'Cloudflare': '*******',
            'Google': '*******',
            'Quad9': '*******',
            'OpenDNS': '**************',
            '阿里DNS': '*********',
            '腾讯DNS': '************',
            '百度DNS': '************'
        }
        
        # 可疑IP段（常用于DNS劫持）
        self.suspicious_patterns = [
            '127.0.0.1', '0.0.0.0', '192.168.', '10.', '172.16.',
            '172.17.', '172.18.', '172.19.', '172.20.', '172.21.',
            '172.22.', '172.23.', '172.24.', '172.25.', '172.26.',
            '172.27.', '172.28.', '172.29.', '172.30.', '172.31.'
        ]

    def resolve_domain(self, domain, dns_server, timeout=5):
        """使用指定DNS服务器解析域名"""
        try:
            # 使用nslookup命令进行DNS查询
            cmd = f'nslookup {domain} {dns_server}'
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                ips = []
                for line in lines:
                    if 'Address:' in line and dns_server not in line:
                        ip = line.split('Address:')[-1].strip()
                        if self.is_valid_ip(ip):
                            ips.append(ip)
                return ips
            return []
        except Exception as e:
            print(f"解析 {domain} 时出错 (DNS: {dns_server}): {e}")
            return []

    def is_valid_ip(self, ip):
        """验证IP地址格式"""
        try:
            socket.inet_aton(ip)
            return True
        except socket.error:
            return False

    def is_suspicious_ip(self, ip):
        """检查IP是否可疑"""
        return any(ip.startswith(pattern) for pattern in self.suspicious_patterns)

    def check_domain_consistency(self, domain):
        """检查域名在不同DNS服务器上的解析一致性"""
        results = {}
        
        print(f"\n检查域名: {domain}")
        print("-" * 50)
        
        for name, dns_server in self.dns_servers.items():
            start_time = time.time()
            ips = self.resolve_domain(domain, dns_server)
            response_time = (time.time() - start_time) * 1000
            
            results[name] = {
                'ips': ips,
                'response_time': response_time,
                'dns_server': dns_server
            }
            
            if ips:
                ip_str = ', '.join(ips)
                suspicious = any(self.is_suspicious_ip(ip) for ip in ips)
                status = "🚨 可疑" if suspicious else "✓ 正常"
                print(f"{name:12} ({dns_server:15}): {ip_str:30} [{response_time:6.1f}ms] {status}")
            else:
                print(f"{name:12} ({dns_server:15}): 解析失败")
        
        # 分析一致性
        all_ips = set()
        for result in results.values():
            all_ips.update(result['ips'])
        
        if len(all_ips) > 1:
            print(f"\n⚠️  警告: 发现不一致的解析结果，可能存在DNS污染!")
            print(f"   发现的不同IP: {', '.join(all_ips)}")
        
        return results

    def get_system_dns(self):
        """获取系统DNS配置"""
        try:
            if sys.platform == "win32":
                cmd = 'ipconfig /all'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                lines = result.stdout.split('\n')
                dns_servers = []
                for line in lines:
                    if 'DNS' in line and ':' in line:
                        dns_servers.append(line.strip())
                return dns_servers
            else:
                # Linux/Mac
                with open('/etc/resolv.conf', 'r') as f:
                    lines = f.readlines()
                return [line.strip() for line in lines if line.startswith('nameserver')]
        except Exception as e:
            print(f"获取系统DNS配置失败: {e}")
            return []

    def test_dns_connectivity(self):
        """测试DNS服务器连通性"""
        print("\n检查DNS服务器连通性:")
        print("-" * 50)
        
        for name, server in self.dns_servers.items():
            try:
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3)
                result = sock.connect_ex((server, 53))
                sock.close()
                response_time = (time.time() - start_time) * 1000
                
                if result == 0:
                    print(f"✓ {name:12} ({server:15}): 连接正常 [{response_time:.1f}ms]")
                else:
                    print(f"✗ {name:12} ({server:15}): 连接失败")
            except Exception as e:
                print(f"✗ {name:12} ({server:15}): 测试失败 - {e}")

    def generate_report(self, results):
        """生成检查报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_dns': self.get_system_dns(),
            'test_results': results,
            'summary': {
                'total_domains': len(self.test_domains),
                'suspicious_domains': [],
                'inconsistent_domains': []
            }
        }
        
        # 分析结果
        for domain, domain_results in results.items():
            all_ips = set()
            suspicious_found = False
            
            for dns_name, result in domain_results.items():
                all_ips.update(result['ips'])
                if any(self.is_suspicious_ip(ip) for ip in result['ips']):
                    suspicious_found = True
            
            if suspicious_found:
                report['summary']['suspicious_domains'].append(domain)
            
            if len(all_ips) > 1:
                report['summary']['inconsistent_domains'].append(domain)
        
        return report

    def run_check(self, export_report=False):
        """运行完整的DNS安全检查"""
        print("=" * 60)
        print("           DNS安全检查工具")
        print("=" * 60)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示系统DNS配置
        print("\n当前系统DNS配置:")
        print("-" * 50)
        system_dns = self.get_system_dns()
        for dns in system_dns:
            print(f"  {dns}")
        
        # 测试DNS连通性
        self.test_dns_connectivity()
        
        # 检查域名解析一致性
        results = {}
        for domain in self.test_domains:
            results[domain] = self.check_domain_consistency(domain)
        
        # 生成报告
        report = self.generate_report(results)
        
        # 显示总结
        print("\n" + "=" * 60)
        print("检查总结:")
        print("-" * 60)
        print(f"测试域名总数: {report['summary']['total_domains']}")
        print(f"发现可疑域名: {len(report['summary']['suspicious_domains'])}")
        print(f"解析不一致域名: {len(report['summary']['inconsistent_domains'])}")
        
        if report['summary']['suspicious_domains']:
            print(f"\n可疑域名: {', '.join(report['summary']['suspicious_domains'])}")
        
        if report['summary']['inconsistent_domains']:
            print(f"\n解析不一致域名: {', '.join(report['summary']['inconsistent_domains'])}")
        
        # 建议
        print("\n建议:")
        print("1. 如发现DNS污染，建议更换DNS服务器")
        print("2. 推荐使用: ******* (Cloudflare) 或 ******* (Google)")
        print("3. 清除DNS缓存: ipconfig /flushdns (Windows)")
        print("4. 考虑使用DoH或DoT加密DNS")
        
        if export_report:
            filename = f"dns_security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n详细报告已导出: {filename}")

def main():
    parser = argparse.ArgumentParser(description='DNS安全检查工具')
    parser.add_argument('--export', action='store_true', help='导出详细报告')
    args = parser.parse_args()
    
    checker = DNSSecurityChecker()
    checker.run_check(export_report=args.export)

if __name__ == "__main__":
    main()
