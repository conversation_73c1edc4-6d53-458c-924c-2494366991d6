#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DNS安全检查工具 - HTTP代理版本
通过HTTP代理测试DNS解析
"""

import socket
import time
import subprocess
import sys
import json
import requests
from datetime import datetime
import argparse

class DNSCheckerHTTPProxy:
    def __init__(self, proxy_host='127.0.0.1', proxy_port=7890):
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        
        # 测试域名
        self.test_domains = [
            'google.com', 'youtube.com', 'facebook.com', 'github.com',
            'baidu.com', 'qq.com', 'taobao.com'
        ]
        
        # DNS服务器
        self.dns_servers = {
            'Cloudflare': '*******',
            'Google': '*******',
            'Alibaba': '*********',
            'Tencent': '************'
        }

    def test_proxy_connection(self):
        """测试代理连接"""
        try:
            # 尝试HTTP代理
            proxies = {
                'http': f'http://{self.proxy_host}:{self.proxy_port}',
                'https': f'http://{self.proxy_host}:{self.proxy_port}'
            }
            
            response = requests.get('http://httpbin.org/ip', 
                                  proxies=proxies, 
                                  timeout=10)
            if response.status_code == 200:
                return True, f"HTTP代理连接正常 (IP: {response.json().get('origin', 'unknown')})"
        except Exception as e:
            pass
        
        try:
            # 尝试SOCKS5代理
            proxies = {
                'http': f'socks5://{self.proxy_host}:{self.proxy_port}',
                'https': f'socks5://{self.proxy_host}:{self.proxy_port}'
            }
            
            response = requests.get('http://httpbin.org/ip', 
                                  proxies=proxies, 
                                  timeout=10)
            if response.status_code == 200:
                return True, f"SOCKS5代理连接正常 (IP: {response.json().get('origin', 'unknown')})"
        except Exception as e:
            pass
        
        return False, "代理连接失败"

    def resolve_domain_direct(self, domain, dns_server, timeout=5):
        """直接DNS解析"""
        try:
            cmd = f'nslookup {domain} {dns_server}'
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                ips = []
                for line in lines:
                    if 'Address:' in line and dns_server not in line:
                        ip = line.split('Address:')[-1].strip()
                        if self.is_valid_ip(ip):
                            ips.append(ip)
                return ips
            return []
        except Exception as e:
            return []

    def resolve_domain_via_proxy(self, domain, timeout=10):
        """通过代理使用DoH解析域名"""
        try:
            # 尝试HTTP代理
            proxies = {
                'http': f'http://{self.proxy_host}:{self.proxy_port}',
                'https': f'http://{self.proxy_host}:{self.proxy_port}'
            }
            
            # 使用Cloudflare DoH
            url = f'https://cloudflare-dns.com/dns-query?name={domain}&type=A'
            headers = {'Accept': 'application/dns-json'}
            
            response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                ips = []
                if 'Answer' in data:
                    for answer in data['Answer']:
                        if answer.get('type') == 1:  # A record
                            ips.append(answer['data'])
                return ips, "HTTP代理"
        except Exception as e:
            pass
        
        try:
            # 尝试SOCKS5代理
            proxies = {
                'http': f'socks5://{self.proxy_host}:{self.proxy_port}',
                'https': f'socks5://{self.proxy_host}:{self.proxy_port}'
            }
            
            response = requests.get(url, headers=headers, proxies=proxies, timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                ips = []
                if 'Answer' in data:
                    for answer in data['Answer']:
                        if answer.get('type') == 1:  # A record
                            ips.append(answer['data'])
                return ips, "SOCKS5代理"
        except Exception as e:
            pass
        
        return [], "代理失败"

    def is_valid_ip(self, ip):
        """验证IP地址格式"""
        try:
            socket.inet_aton(ip)
            return True
        except socket.error:
            return False

    def check_domain(self, domain):
        """检查单个域名的解析"""
        print(f"\n检查域名: {domain}")
        print("-" * 60)
        
        results = {}
        
        # 直接DNS解析
        for name, dns_server in self.dns_servers.items():
            start_time = time.time()
            ips = self.resolve_domain_direct(domain, dns_server)
            response_time = (time.time() - start_time) * 1000
            
            results[f"{name}_direct"] = {
                'ips': ips,
                'response_time': response_time,
                'method': '直连',
                'dns_server': dns_server
            }
            
            if ips:
                ip_str = ', '.join(ips)
                print(f"{name:12} (直连): {ip_str:30} [{response_time:6.1f}ms]")
            else:
                print(f"{name:12} (直连): 解析失败")
        
        # 通过代理解析
        start_time = time.time()
        proxy_ips, proxy_method = self.resolve_domain_via_proxy(domain)
        response_time = (time.time() - start_time) * 1000
        
        results['proxy'] = {
            'ips': proxy_ips,
            'response_time': response_time,
            'method': proxy_method,
            'dns_server': 'Cloudflare DoH'
        }
        
        if proxy_ips:
            ip_str = ', '.join(proxy_ips)
            print(f"{'代理DoH':12} ({proxy_method}): {ip_str:30} [{response_time:6.1f}ms]")
        else:
            print(f"{'代理DoH':12} ({proxy_method}): 解析失败")
        
        # 分析结果
        all_ips = set()
        for result in results.values():
            all_ips.update(result['ips'])
        
        if len(all_ips) > 1:
            print(f"\n⚠️  警告: 发现不一致的解析结果!")
            print(f"   发现的不同IP: {', '.join(all_ips)}")
        elif len(all_ips) == 1:
            print(f"\n✅ 解析结果一致: {list(all_ips)[0]}")
        else:
            print(f"\n❌ 所有解析都失败")
        
        return results

    def run_check(self, export_report=False):
        """运行完整检查"""
        print("=" * 70)
        print("           DNS安全检查工具 (HTTP代理版本)")
        print("=" * 70)
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试代理连接
        print(f"\n测试代理连接 ({self.proxy_host}:{self.proxy_port}):")
        print("-" * 60)
        proxy_ok, proxy_msg = self.test_proxy_connection()
        print(f"代理状态: {proxy_msg}")
        
        # 检查所有域名
        all_results = {}
        for domain in self.test_domains:
            all_results[domain] = self.check_domain(domain)
        
        # 生成总结
        print("\n" + "=" * 70)
        print("检查总结:")
        print("-" * 70)
        print(f"代理状态: {'正常' if proxy_ok else '异常'}")
        print(f"测试域名总数: {len(self.test_domains)}")
        
        inconsistent_domains = []
        proxy_success_domains = []
        
        for domain, results in all_results.items():
            all_ips = set()
            proxy_success = False
            
            for method, result in results.items():
                all_ips.update(result['ips'])
                if 'proxy' in method and result['ips']:
                    proxy_success = True
            
            if len(all_ips) > 1:
                inconsistent_domains.append(domain)
            
            if proxy_success:
                proxy_success_domains.append(domain)
        
        print(f"解析不一致域名: {len(inconsistent_domains)}")
        print(f"代理成功解析域名: {len(proxy_success_domains)}")
        
        if inconsistent_domains:
            print(f"\n解析不一致域名: {', '.join(inconsistent_domains)}")
        
        if proxy_success_domains:
            print(f"\n代理成功解析域名: {', '.join(proxy_success_domains)}")
        
        # 建议
        print("\n建议:")
        if proxy_ok:
            print("✓ 代理工作正常，可以通过代理访问国外DNS服务")
        else:
            print("⚠️  代理连接异常，建议检查代理设置")
        
        print("1. 对于解析不一致的域名，建议使用可信DNS服务器")
        print("2. 可以通过代理使用DoH服务获得更准确的解析结果")
        print("3. 清除DNS缓存: ipconfig /flushdns")
        
        if export_report:
            filename = f"dns_http_proxy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report = {
                'timestamp': datetime.now().isoformat(),
                'proxy_status': {'proxy_ok': proxy_ok, 'proxy_msg': proxy_msg},
                'test_results': all_results,
                'summary': {
                    'total_domains': len(self.test_domains),
                    'inconsistent_domains': inconsistent_domains,
                    'proxy_success_domains': proxy_success_domains
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n详细报告已导出: {filename}")

def main():
    parser = argparse.ArgumentParser(description='DNS安全检查工具 (HTTP代理版本)')
    parser.add_argument('--export', action='store_true', help='导出详细报告')
    parser.add_argument('--proxy-host', default='127.0.0.1', help='代理主机地址')
    parser.add_argument('--proxy-port', type=int, default=7890, help='代理端口')
    args = parser.parse_args()
    
    checker = DNSCheckerHTTPProxy(proxy_host=args.proxy_host, proxy_port=args.proxy_port)
    checker.run_check(export_report=args.export)

if __name__ == "__main__":
    main()
