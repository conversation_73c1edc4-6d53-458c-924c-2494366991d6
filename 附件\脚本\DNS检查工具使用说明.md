# DNS检查工具使用说明

## 概述
本工具集用于检测DNS污染、劫持和可能的中间人攻击，包含三个不同复杂度的脚本。

## 工具列表

### 1. quick_dns_check.bat (快速检查)
**用途**: 快速检查DNS基本状态
**运行方式**: 双击运行或在命令行执行
```cmd
quick_dns_check.bat
```

**功能**:
- 显示当前DNS配置
- 清除DNS缓存
- 测试关键域名解析
- 检查DNS服务器连通性

### 2. check_dns.ps1 (详细检查)
**用途**: 全面的DNS安全检查
**运行方式**: 
```powershell
# 基本检查
powershell -ExecutionPolicy Bypass -File check_dns.ps1

# 详细模式
powershell -ExecutionPolicy Bypass -File check_dns.ps1 -Detailed

# 导出报告
powershell -ExecutionPolicy Bypass -File check_dns.ps1 -Export -OutputFile "my_dns_report.txt"
```

**功能**:
- 检查DNS配置
- 测试多个DNS服务器的解析一致性
- 检测DNS劫持迹象
- 分析DNS缓存
- 测试网络连接
- 生成详细报告

### 3. dns_security_check.py (专业检查)
**用途**: 最全面的DNS安全分析
**运行方式**:
```bash
# 基本检查
python dns_security_check.py

# 导出JSON报告
python dns_security_check.py --export
```

**功能**:
- 多DNS服务器对比分析
- 响应时间测量
- 可疑IP检测
- 一致性分析
- JSON格式报告导出

## 检查项目

### DNS污染检测
- 对比多个可信DNS服务器的解析结果
- 检测是否返回错误或可疑的IP地址
- 识别内网IP、本地IP等异常响应

### 中间人攻击迹象
- 检查解析结果的一致性
- 监测响应时间异常
- 识别可疑的DNS响应模式

### 网络连通性
- 测试到各大DNS服务器的连接
- 检查DNS端口(53)的可达性
- 评估网络质量

## 常见问题及解决方案

### 1. DNS污染
**症状**: 不同DNS服务器返回不同IP地址
**解决方案**:
```cmd
# 更换DNS服务器
netsh interface ip set dns "本地连接" static *******
netsh interface ip add dns "本地连接" ******* index=2

# 清除DNS缓存
ipconfig /flushdns
```

### 2. DNS劫持
**症状**: 返回内网IP或错误IP地址
**解决方案**:
- 检查路由器DNS设置
- 使用加密DNS (DoH/DoT)
- 更换网络环境测试

### 3. 连接问题
**症状**: 无法连接到DNS服务器
**解决方案**:
- 检查防火墙设置
- 确认网络连接正常
- 尝试不同的DNS服务器

## 推荐DNS服务器

### 国际DNS
- **Cloudflare**: *******, *******
- **Google**: *******, *******
- **Quad9**: *******, ***************

### 国内DNS
- **阿里DNS**: *********, *********
- **腾讯DNS**: ************, ***************
- **百度DNS**: ************

## 安全建议

1. **定期检查**: 建议每周运行一次完整检查
2. **多重验证**: 使用不同工具交叉验证结果
3. **及时更新**: 保持DNS设置的及时更新
4. **加密DNS**: 考虑使用DoH或DoT加密DNS查询
5. **监控异常**: 注意浏览器证书警告和连接异常

## 报告解读

### 正常状态
- 所有DNS服务器返回相同IP地址
- 响应时间在合理范围内(通常<100ms)
- 无可疑IP地址

### 异常状态
- 不同DNS服务器返回不同IP
- 返回内网IP地址(192.168.x.x, 10.x.x.x等)
- 响应时间异常长或短
- 解析失败率高

## 技术支持

如果发现严重的DNS安全问题:
1. 立即更换DNS服务器
2. 检查网络设备配置
3. 考虑联系网络管理员
4. 必要时更换网络环境

---
*最后更新: 2025-01-05*
