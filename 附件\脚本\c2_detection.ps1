# C2 (Command & Control) Detection Script
# Detects potential C2 communications and malware indicators
# Author: Security Check Script
# Date: 2025-01-05

param(
    [switch]$Detailed,
    [switch]$Export,
    [string]$OutputFile = "c2_detection_report.txt"
)

Write-Host "=== C2 Detection Tool ===" -ForegroundColor Red
Write-Host "Scan Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

$SuspiciousIndicators = @()
$Warnings = @()

# Suspicious ports commonly used by malware
$SuspiciousPorts = @(4444, 5555, 6666, 7777, 8080, 8888, 9999, 1337, 31337, 4445, 5554)

# Suspicious processes (common RAT/malware names)
$SuspiciousProcessNames = @(
    "nc.exe", "netcat.exe", "ncat.exe", "socat.exe",
    "psexec.exe", "mimikatz.exe", "procdump.exe",
    "winlogon.exe", "csrss.exe", "lsass.exe", "smss.exe"
)

# Suspicious file locations
$SuspiciousLocations = @(
    "$env:TEMP",
    "$env:APPDATA",
    "$env:LOCALAPPDATA",
    "C:\Windows\Temp",
    "C:\Users\<USER>\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)" -and 
                $Connection.LocalPort -lt 1024) {
                $ProcessInfo = Get-Process -Id $Connection.OwningProcess -ErrorAction SilentlyContinue
                if ($ProcessInfo.ProcessName -in @("svchost", "winlogon", "csrss")) {
                    $Warning = "System process $($ProcessInfo.ProcessName) connecting to private IP: $($Connection.RemoteAddress)"
                    $script:Warnings += $Warning
                    Write-Host "  [WARN] $Warning" -ForegroundColor Yellow
                }
            }
        }
        
        # Check for unusual listening ports
        $Listeners = Get-NetTCPConnection | Where-Object {$_.State -eq "Listen"}
        foreach ($Listener in $Listeners) {
            if ($SuspiciousPorts -contains $Listener.LocalPort) {
                $ProcessInfo = Get-Process -Id $Listener.OwningProcess -ErrorAction SilentlyContinue
                $Indicator = @{
                    Type = "Suspicious Listening Port"
                    Details = "Process: $($ProcessInfo.ProcessName) (PID: $($Listener.OwningProcess)) listening on port $($Listener.LocalPort)"
                    Severity = "Medium"
                }
                $script:SuspiciousIndicators += $Indicator
                Write-Host "  [MED] Suspicious listening port: $($ProcessInfo.ProcessName) on port $($Listener.LocalPort)" -ForegroundColor Orange
            }
        }
        
        Write-Host "  Network connections check completed" -ForegroundColor Green
    }
    catch {
        Write-Host "  Error checking network connections: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-SuspiciousProcesses {
    Write-Host "2. Checking suspicious processes..." -ForegroundColor Cyan
    
    try {
        $Processes = Get-Process
        
        foreach ($Process in $Processes) {
            # Check for suspicious process names
            if ($SuspiciousProcessNames -contains $Process.ProcessName) {
                $Indicator = @{
                    Type = "Suspicious Process Name"
                    Details = "Process: $($Process.ProcessName) (PID: $($Process.Id)) - Path: $($Process.Path)"
                    Severity = "High"
                }
                $script:SuspiciousIndicators += $Indicator
                Write-Host "  [HIGH] Suspicious process: $($Process.ProcessName) (PID: $($Process.Id))" -ForegroundColor Red
            }
            
            # Check for processes without file path (potentially injected)
            if (-not $Process.Path -and $Process.ProcessName -ne "Idle" -and $Process.ProcessName -ne "System") {
                $Warning = "Process without file path: $($Process.ProcessName) (PID: $($Process.Id))"
                $script:Warnings += $Warning
                Write-Host "  [WARN] $Warning" -ForegroundColor Yellow
            }
            
            # Check for processes in suspicious locations
            if ($Process.Path) {
                foreach ($SuspiciousLocation in $SuspiciousLocations) {
                    if ($Process.Path -like "$SuspiciousLocation*") {
                        $Indicator = @{
                            Type = "Process in Suspicious Location"
                            Details = "Process: $($Process.ProcessName) (PID: $($Process.Id)) - Path: $($Process.Path)"
                            Severity = "Medium"
                        }
                        $script:SuspiciousIndicators += $Indicator
                        Write-Host "  [MED] Process in suspicious location: $($Process.ProcessName) - $($Process.Path)" -ForegroundColor Orange
                        break
                    }
                }
            }
        }
        
        Write-Host "  Process check completed" -ForegroundColor Green
    }
    catch {
        Write-Host "  Error checking processes: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-StartupItems {
    Write-Host "3. Checking startup items..." -ForegroundColor Cyan
    
    try {
        # Check registry startup locations
        $StartupLocations = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
            "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
        )
        
        foreach ($Location in $StartupLocations) {
            if (Test-Path $Location) {
                $Items = Get-ItemProperty $Location -ErrorAction SilentlyContinue
                if ($Items) {
                    $Items.PSObject.Properties | ForEach-Object {
                        if ($_.Name -notlike "PS*") {
                            $Value = $_.Value
                            # Check for suspicious startup items
                            foreach ($SuspiciousLocation in $SuspiciousLocations) {
                                if ($Value -like "$SuspiciousLocation*") {
                                    $Indicator = @{
                                        Type = "Suspicious Startup Item"
                                        Details = "Registry: $Location - Name: $($_.Name) - Value: $Value"
                                        Severity = "Medium"
                                    }
                                    $script:SuspiciousIndicators += $Indicator
                                    Write-Host "  [MED] Suspicious startup item: $($_.Name) -> $Value" -ForegroundColor Orange
                                    break
                                }
                            }
                        }
                    }
                }
            }
        }
        
        Write-Host "  Startup items check completed" -ForegroundColor Green
    }
    catch {
        Write-Host "  Error checking startup items: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-Services {
    Write-Host "4. Checking suspicious services..." -ForegroundColor Cyan
    
    try {
        $Services = Get-Service | Where-Object {$_.Status -eq "Running"}
        
        foreach ($Service in $Services) {
            # Get service details
            $ServiceDetails = Get-WmiObject -Class Win32_Service | Where-Object {$_.Name -eq $Service.Name}
            
            if ($ServiceDetails -and $ServiceDetails.PathName) {
                # Check for services in suspicious locations
                foreach ($SuspiciousLocation in $SuspiciousLocations) {
                    if ($ServiceDetails.PathName -like "$SuspiciousLocation*") {
                        $Indicator = @{
                            Type = "Suspicious Service Location"
                            Details = "Service: $($Service.Name) - Path: $($ServiceDetails.PathName)"
                            Severity = "High"
                        }
                        $script:SuspiciousIndicators += $Indicator
                        Write-Host "  [HIGH] Suspicious service location: $($Service.Name) - $($ServiceDetails.PathName)" -ForegroundColor Red
                        break
                    }
                }
            }
        }
        
        Write-Host "  Services check completed" -ForegroundColor Green
    }
    catch {
        Write-Host "  Error checking services: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-RecentFiles {
    Write-Host "5. Checking recent suspicious files..." -ForegroundColor Cyan
    
    try {
        $SuspiciousExtensions = @("*.exe", "*.scr", "*.bat", "*.cmd", "*.ps1", "*.vbs")
        $RecentThreshold = (Get-Date).AddDays(-7)
        
        foreach ($Location in $SuspiciousLocations) {
            if (Test-Path $Location) {
                foreach ($Extension in $SuspiciousExtensions) {
                    $Files = Get-ChildItem -Path $Location -Filter $Extension -Recurse -ErrorAction SilentlyContinue | 
                             Where-Object {$_.CreationTime -gt $RecentThreshold}
                    
                    foreach ($File in $Files) {
                        $Indicator = @{
                            Type = "Recent Suspicious File"
                            Details = "File: $($File.FullName) - Created: $($File.CreationTime)"
                            Severity = "Medium"
                        }
                        $script:SuspiciousIndicators += $Indicator
                        Write-Host "  [MED] Recent suspicious file: $($File.FullName)" -ForegroundColor Orange
                    }
                }
            }
        }
        
        Write-Host "  Recent files check completed" -ForegroundColor Green
    }
    catch {
        Write-Host "  Error checking recent files: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Run all checks
Test-SuspiciousConnections
Write-Host ""
Test-SuspiciousProcesses
Write-Host ""
Test-StartupItems
Write-Host ""
Test-Services
Write-Host ""
Test-RecentFiles

# Generate summary
Write-Host ""
Write-Host "=== C2 Detection Summary ===" -ForegroundColor Red
Write-Host "Suspicious Indicators Found: $($SuspiciousIndicators.Count)" -ForegroundColor $(if($SuspiciousIndicators.Count -gt 0){"Red"}else{"Green"})
Write-Host "Warnings: $($Warnings.Count)" -ForegroundColor $(if($Warnings.Count -gt 0){"Yellow"}else{"Green"})

if ($SuspiciousIndicators.Count -gt 0) {
    Write-Host ""
    Write-Host "CRITICAL FINDINGS:" -ForegroundColor Red
    $SuspiciousIndicators | ForEach-Object {
        Write-Host "[$($_.Severity)] $($_.Type): $($_.Details)" -ForegroundColor Red
    }
}

if ($Warnings.Count -gt 0 -and $Detailed) {
    Write-Host ""
    Write-Host "WARNINGS:" -ForegroundColor Yellow
    $Warnings | ForEach-Object {
        Write-Host "[WARN] $_" -ForegroundColor Yellow
    }
}

# Recommendations
Write-Host ""
Write-Host "RECOMMENDATIONS:" -ForegroundColor Cyan
if ($SuspiciousIndicators.Count -gt 0) {
    Write-Host "1. IMMEDIATE ACTION REQUIRED - Potential C2 activity detected!" -ForegroundColor Red
    Write-Host "2. Disconnect from network if possible" -ForegroundColor Red
    Write-Host "3. Run full antivirus scan" -ForegroundColor Red
    Write-Host "4. Check firewall logs" -ForegroundColor Red
    Write-Host "5. Consider professional incident response" -ForegroundColor Red
} else {
    Write-Host "1. No immediate C2 threats detected" -ForegroundColor Green
    Write-Host "2. Continue regular security monitoring" -ForegroundColor Green
    Write-Host "3. Keep antivirus updated" -ForegroundColor Green
}

# Export report
if ($Export) {
    $ReportContent = @"
C2 Detection Report
Generated: $(Get-Date)

=== SUMMARY ===
Suspicious Indicators: $($SuspiciousIndicators.Count)
Warnings: $($Warnings.Count)

=== SUSPICIOUS INDICATORS ===
$($SuspiciousIndicators | ForEach-Object { "[$($_.Severity)] $($_.Type): $($_.Details)" } | Out-String)

=== WARNINGS ===
$($Warnings | ForEach-Object { "[WARN] $_" } | Out-String)
"@
    
    $ReportContent | Out-File -FilePath $OutputFile -Encoding UTF8
    Write-Host "Report exported to: $OutputFile" -ForegroundColor Green
}
