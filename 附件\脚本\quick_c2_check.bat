@echo off
title C2 Quick Check Tool

echo ================================
echo       C2 Quick Check Tool
echo ================================
echo.

echo [1] Checking suspicious network connections...
echo.
netstat -an | findstr /i "ESTABLISHED" | findstr /i ":4444 :5555 :6666 :7777 :8888 :9999 :1337"
if %errorlevel% equ 0 (
    echo [WARNING] Suspicious port connections found!
) else (
    echo [OK] No suspicious port connections detected
)
echo.

echo [2] Checking for suspicious processes...
echo.
tasklist | findstr /i "nc.exe netcat.exe mimikatz.exe psexec.exe"
if %errorlevel% equ 0 (
    echo [WARNING] Suspicious processes found!
) else (
    echo [OK] No obvious suspicious processes detected
)
echo.

echo [3] Checking listening ports...
echo.
netstat -an | findstr /i "LISTENING" | findstr /i ":4444 :5555 :6666 :7777 :8888 :9999 :1337"
if %errorlevel% equ 0 (
    echo [WARNING] Suspicious listening ports found!
) else (
    echo [OK] No suspicious listening ports detected
)
echo.

echo [4] Checking recent files in temp directories...
echo.
forfiles /p %TEMP% /m *.exe /d -7 2>nul && echo [WARNING] Recent executables in temp directory found!
forfiles /p %APPDATA% /m *.exe /d -7 2>nul && echo [WARNING] Recent executables in AppData found!
echo.

echo [5] Quick system info...
echo Current user: %USERNAME%
echo Computer name: %COMPUTERNAME%
echo Current time: %DATE% %TIME%
echo.

echo [6] Recommendations:
echo - If warnings found, run detailed scan: powershell -ExecutionPolicy Bypass -File c2_detection.ps1
echo - Or run Python version: python c2_detection.py --export
echo - Keep antivirus updated and run full scan
echo - Monitor network traffic for unusual activity
echo.

pause
