# Simple DNS Check Script
# For detecting DNS pollution and potential attacks

param(
    [switch]$Export,
    [string]$OutputFile = "dns_check_report.txt"
)

Write-Host "=== DNS Security Check Tool ===" -ForegroundColor Green
Write-Host "Check Time: $(Get-Date)" -ForegroundColor Yellow
Write-Host ""

# Test domains
$TestDomains = @(
    "google.com",
    "youtube.com", 
    "facebook.com",
    "github.com",
    "baidu.com"
)

# Trusted DNS servers
$TrustedDNS = @{
    "Local DNS" = "***********"
    "Cloudflare" = "*******"
    "Google" = "*******"
    "Alibaba" = "*********"
}

$Results = @()

function Test-DNSResolution {
    param($Domain, $DNSServer, $ServerName)
    
    try {
        $Result = Resolve-DnsName -Name $Domain -Server $DNSServer -Type A -ErrorAction Stop
        $IPs = $Result | Where-Object {$_.Type -eq "A"} | Select-Object -ExpandProperty IPAddress
        
        return @{
            Domain = $Domain
            DNSServer = $ServerName
            IPs = $IPs -join ", "
            Status = "Success"
        }
    }
    catch {
        return @{
            Domain = $Domain
            DNSServer = $ServerName
            IPs = "Resolution Failed"
            Status = "Failed"
        }
    }
}

Write-Host "1. Checking current DNS configuration..." -ForegroundColor Cyan
$NetworkAdapters = Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object {$_.ServerAddresses.Count -gt 0}
foreach ($Adapter in $NetworkAdapters) {
    Write-Host "  Interface: $($Adapter.InterfaceAlias)" -ForegroundColor White
    Write-Host "  DNS Servers: $($Adapter.ServerAddresses -join ', ')" -ForegroundColor White
}
Write-Host ""

Write-Host "2. Testing DNS resolution consistency..." -ForegroundColor Cyan
foreach ($Domain in $TestDomains) {
    Write-Host "Testing domain: $Domain" -ForegroundColor Yellow
    
    $DomainResults = @()
    foreach ($DNS in $TrustedDNS.GetEnumerator()) {
        $Result = Test-DNSResolution -Domain $Domain -DNSServer $DNS.Value -ServerName $DNS.Key
        $DomainResults += $Result
        
        if ($Result.Status -eq "Success") {
            Write-Host "  $($DNS.Key): $($Result.IPs)" -ForegroundColor Green
        } else {
            Write-Host "  $($DNS.Key): $($Result.Status)" -ForegroundColor Red
        }
    }
    
    # Check IP consistency
    $UniqueIPs = $DomainResults | Where-Object {$_.Status -eq "Success"} | Select-Object -ExpandProperty IPs | Sort-Object -Unique
    if ($UniqueIPs.Count -gt 1) {
        Write-Host "  WARNING: Different DNS servers return different IPs - possible DNS pollution!" -ForegroundColor Red
    }
    
    $Results += $DomainResults
    Write-Host ""
}

Write-Host "3. Checking for DNS hijacking signs..." -ForegroundColor Cyan

# Check for suspicious DNS responses
$SuspiciousIPs = @(
    "127.0.0.1",
    "0.0.0.0", 
    "192.168.",
    "10.",
    "172.16."
)

$SuspiciousResults = $Results | Where-Object {
    $_.Status -eq "Success" -and 
    ($SuspiciousIPs | Where-Object { $_.IPs -like "*$_*" })
}

if ($SuspiciousResults.Count -gt 0) {
    Write-Host "Found suspicious DNS resolution results:" -ForegroundColor Red
    $SuspiciousResults | ForEach-Object {
        Write-Host "  $($_.Domain) -> $($_.IPs) (via $($_.DNSServer))" -ForegroundColor Red
    }
} else {
    Write-Host "No obvious DNS hijacking signs detected" -ForegroundColor Green
}
Write-Host ""

Write-Host "4. DNS cache check..." -ForegroundColor Cyan
try {
    $DNSCache = Get-DnsClientCache | Select-Object Name, Data, TimeToLive | Sort-Object Name
    Write-Host "Current DNS cache entries: $($DNSCache.Count)" -ForegroundColor White
} catch {
    Write-Host "Cannot get DNS cache information: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "5. Network connectivity check..." -ForegroundColor Cyan
$TestConnections = @(
    @{Host="*******"; Port=53; Name="Cloudflare DNS"},
    @{Host="*******"; Port=53; Name="Google DNS"},
    @{Host="*********"; Port=53; Name="Alibaba DNS"}
)

foreach ($Test in $TestConnections) {
    try {
        $Connection = Test-NetConnection -ComputerName $Test.Host -Port $Test.Port -WarningAction SilentlyContinue
        if ($Connection.TcpTestSucceeded) {
            Write-Host "  [OK] $($Test.Name) connection normal" -ForegroundColor Green
        } else {
            Write-Host "  [FAIL] $($Test.Name) connection failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "  [FAIL] $($Test.Name) test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Check Complete ===" -ForegroundColor Green

# Generate recommendations
Write-Host "Recommendations:" -ForegroundColor Yellow
Write-Host "1. If DNS pollution detected, change DNS servers to ******* or *******" -ForegroundColor White
Write-Host "2. Clear DNS cache: ipconfig /flushdns" -ForegroundColor White
Write-Host "3. If issues persist, consider using DoH or DoT" -ForegroundColor White
Write-Host "4. Check router DNS settings for tampering" -ForegroundColor White

# Export report
if ($Export) {
    $ReportContent = @"
DNS Security Check Report
Generated: $(Get-Date)

=== DNS Configuration ===
$(($NetworkAdapters | ForEach-Object { "Interface: $($_.InterfaceAlias), DNS: $($_.ServerAddresses -join ', ')" }) -join "`n")

=== Resolution Results ===
$($Results | ForEach-Object { "$($_.Domain) - $($_.DNSServer): $($_.IPs) ($($_.Status))" } | Out-String)

=== Suspicious Results ===
$(if ($SuspiciousResults.Count -gt 0) { $SuspiciousResults | ForEach-Object { "$($_.Domain) -> $($_.IPs) (via $($_.DNSServer))" } } else { "No suspicious results" })
"@
    
    $ReportContent | Out-File -FilePath $OutputFile -Encoding UTF8
    Write-Host "Report exported to: $OutputFile" -ForegroundColor Green
}
