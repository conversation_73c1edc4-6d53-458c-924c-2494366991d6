@echo off
title DNS Quick Check Tool

echo ================================
echo       DNS Quick Check Tool
echo ================================
echo.

echo [1] Current DNS Configuration:
ipconfig /all | findstr /i "DNS"
echo.

echo [2] Clearing DNS Cache...
ipconfig /flushdns
echo DNS cache cleared
echo.

echo [3] Testing Key Domain Resolution:
echo.

set domains=google.com youtube.com github.com baidu.com qq.com

for %%d in (%domains%) do (
    echo Testing: %%d
    nslookup %%d ******* | findstr /i "address"
    echo.
)

echo [4] Checking Network Connectivity:
ping -n 1 ******* >nul && echo [OK] Cloudflare DNS (*******) reachable || echo [FAIL] Cloudflare DNS unreachable
ping -n 1 ******* >nul && echo [OK] Google DNS (*******) reachable || echo [FAIL] Google DNS unreachable
ping -n 1 ********* >nul && echo [OK] Alibaba DNS (*********) reachable || echo [FAIL] Alibaba DNS unreachable
echo.

echo [5] Recommendations:
echo - If anomalies found, manually set DNS to ******* and *******
echo - Run detailed check: powershell -ExecutionPolicy Bypass -File check_dns.ps1
echo.

pause
