@echo off
chcp 65001 >nul
title DNS快速检查工具

echo ================================
echo       DNS快速检查工具
echo ================================
echo.

echo [1] 当前DNS配置:
ipconfig /all | findstr /i "DNS"
echo.

echo [2] 清除DNS缓存...
ipconfig /flushdns
echo DNS缓存已清除
echo.

echo [3] 测试关键域名解析:
echo.

set domains=google.com youtube.com github.com baidu.com qq.com

for %%d in (%domains%) do (
    echo 测试: %%d
    nslookup %%d ******* | findstr /i "address"
    echo.
)

echo [4] 检查网络连接:
ping -n 1 ******* >nul && echo ✓ Cloudflare DNS (*******) 可达 || echo ✗ Cloudflare DNS 不可达
ping -n 1 ******* >nul && echo ✓ Google DNS (*******) 可达 || echo ✗ Google DNS 不可达
ping -n 1 ********* >nul && echo ✓ 阿里DNS (*********) 可达 || echo ✗ 阿里DNS 不可达
echo.

echo [5] 建议操作:
echo - 如发现异常，建议手动设置DNS为 ******* 和 *******
echo - 运行完整检查: powershell -ExecutionPolicy Bypass -File check_dns.ps1
echo.

pause
