#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C2 (Command & Control) Detection Tool
Advanced detection of potential C2 communications and malware indicators
"""

import psutil
import socket
import subprocess
import os
import sys
import json
import time
import winreg
from datetime import datetime, timedelta
import argparse

class C2Detector:
    def __init__(self):
        self.suspicious_indicators = []
        self.warnings = []
        
        # Suspicious ports commonly used by malware
        self.suspicious_ports = [4444, 5555, 6666, 7777, 8080, 8888, 9999, 1337, 31337, 4445, 5554, 6667, 6697]
        
        # Suspicious process names
        self.suspicious_processes = [
            'nc.exe', 'netcat.exe', 'ncat.exe', 'socat.exe',
            'psexec.exe', 'mimikatz.exe', 'procdump.exe',
            'meterpreter.exe', 'beacon.exe', 'cobalt.exe'
        ]
        
        # Suspicious file locations
        self.suspicious_locations = [
            os.environ.get('TEMP', ''),
            os.environ.get('APPDATA', ''),
            os.environ.get('LOCALAPPDATA', ''),
            'C:\\Windows\\Temp',
            'C:\\Users\\<USER>\Microsoft\Windows\CurrentVersion\Run"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce")
        ]
        
        try:
            for hkey, subkey in startup_locations:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        i = 0
                        while True:
                            try:
                                name, value, _ = winreg.EnumValue(key, i)
                                
                                # Check for suspicious startup items
                                for location in self.suspicious_locations:
                                    if location and location in value:
                                        self.add_indicator(
                                            "Suspicious Startup Item",
                                            f"Registry: {subkey} - Name: {name} - Value: {value}",
                                            "Medium"
                                        )
                                        print(f"  [MED] Suspicious startup item: {name} -> {value}")
                                        break
                                
                                i += 1
                            except WindowsError:
                                break
                except FileNotFoundError:
                    pass
            
            print("  Startup items check completed")
        except Exception as e:
            print(f"  Error checking startup items: {e}")

    def check_recent_files(self):
        """Check for recent suspicious files"""
        print("4. Checking recent suspicious files...")
        
        suspicious_extensions = ['.exe', '.scr', '.bat', '.cmd', '.ps1', '.vbs', '.dll']
        recent_threshold = datetime.now() - timedelta(days=7)
        
        try:
            for location in self.suspicious_locations:
                if location and os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            file_path = os.path.join(root, file)
                            try:
                                # Check file extension
                                _, ext = os.path.splitext(file)
                                if ext.lower() in suspicious_extensions:
                                    # Check creation time
                                    creation_time = datetime.fromtimestamp(os.path.getctime(file_path))
                                    if creation_time > recent_threshold:
                                        self.add_indicator(
                                            "Recent Suspicious File",
                                            f"File: {file_path} - Created: {creation_time}",
                                            "Medium"
                                        )
                                        print(f"  [MED] Recent suspicious file: {file_path}")
                            except (OSError, PermissionError):
                                pass
            
            print("  Recent files check completed")
        except Exception as e:
            print(f"  Error checking recent files: {e}")

    def check_dns_queries(self):
        """Check for suspicious DNS queries (basic check)"""
        print("5. Checking DNS cache for suspicious domains...")
        
        try:
            # Get DNS cache using nslookup
            result = subprocess.run(['ipconfig', '/displaydns'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                dns_cache = result.stdout
                
                for indicator in self.known_c2_indicators:
                    if indicator in dns_cache:
                        self.add_indicator(
                            "Suspicious DNS Query",
                            f"Found cached DNS query for known C2 domain: {indicator}",
                            "High"
                        )
                        print(f"  [HIGH] Suspicious DNS query found: {indicator}")
            
            print("  DNS cache check completed")
        except Exception as e:
            print(f"  Error checking DNS cache: {e}")

    def generate_report(self):
        """Generate detection report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'suspicious_indicators': len(self.suspicious_indicators),
                'warnings': len(self.warnings),
                'risk_level': self.calculate_risk_level()
            },
            'indicators': self.suspicious_indicators,
            'warnings': self.warnings
        }
        return report

    def calculate_risk_level(self):
        """Calculate overall risk level"""
        high_count = sum(1 for i in self.suspicious_indicators if i['severity'] == 'High')
        medium_count = sum(1 for i in self.suspicious_indicators if i['severity'] == 'Medium')
        
        if high_count >= 3:
            return "Critical"
        elif high_count >= 1:
            return "High"
        elif medium_count >= 3:
            return "Medium"
        else:
            return "Low"

    def run_detection(self, export_report=False):
        """Run complete C2 detection"""
        print("=" * 60)
        print("           C2 Detection Tool")
        print("=" * 60)
        print(f"Scan Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("")
        
        # Run all checks
        self.check_network_connections()
        print("")
        self.check_processes()
        print("")
        self.check_startup_items()
        print("")
        self.check_recent_files()
        print("")
        self.check_dns_queries()
        
        # Generate summary
        risk_level = self.calculate_risk_level()
        print("")
        print("=" * 60)
        print("C2 Detection Summary")
        print("-" * 60)
        print(f"Risk Level: {risk_level}")
        print(f"Suspicious Indicators: {len(self.suspicious_indicators)}")
        print(f"Warnings: {len(self.warnings)}")
        
        if self.suspicious_indicators:
            print("")
            print("CRITICAL FINDINGS:")
            for indicator in self.suspicious_indicators:
                print(f"[{indicator['severity']}] {indicator['type']}: {indicator['details']}")
        
        # Recommendations
        print("")
        print("RECOMMENDATIONS:")
        if risk_level in ['Critical', 'High']:
            print("1. IMMEDIATE ACTION REQUIRED - Potential C2 activity detected!")
            print("2. Disconnect from network if possible")
            print("3. Run full antivirus scan")
            print("4. Check firewall logs")
            print("5. Consider professional incident response")
        else:
            print("1. No immediate C2 threats detected")
            print("2. Continue regular security monitoring")
            print("3. Keep antivirus updated")
        
        if export_report:
            report = self.generate_report()
            filename = f"c2_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\nDetailed report exported: {filename}")

def main():
    parser = argparse.ArgumentParser(description='C2 Detection Tool')
    parser.add_argument('--export', action='store_true', help='Export detailed report')
    args = parser.parse_args()
    
    detector = C2Detector()
    detector.run_detection(export_report=args.export)

if __name__ == "__main__":
    main()
